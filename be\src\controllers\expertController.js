const expertService = require('../services/expertService');

class ExpertController {
  async createExpert(req, res) {
    try {
      const { name, description, systemPrompt, model, pricingPercentage, isPublic, voiceEnabled, labels } = req.body;
      const userId = req.user?.user_id;
      const knowledgeBaseFile = req.files?.file?.[0];
      const imageFile = req.files?.image?.[0];

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      // Parse labels if provided
      let parsedLabels = [];
      if (labels) {
        try {
          parsedLabels = typeof labels === 'string' ? JSON.parse(labels) : labels;
        } catch (error) {
          return res.status(400).json({
            success: false,
            error: 'Invalid labels format'
          });
        }
      }

      // Validate required fields
      const validationErrors = expertService.validateExpertData({
        name,
        systemPrompt,
        model,
        pricingPercentage,
        labels: parsedLabels
      });

      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: validationErrors
        });
      }

      // Process expert creation
      const result = await expertService.createExpert({
        userId,
        name: name.trim(),
        description: description?.trim() || '',
        systemPrompt: systemPrompt.trim(),
        model: model || 'gpt-4o-mini',
        pricingPercentage: parseFloat(pricingPercentage) || 0.00,
        isPublic: isPublic === 'true' || isPublic === true,
        voiceEnabled: voiceEnabled === 'true' || voiceEnabled === true,
        labels: parsedLabels
      }, knowledgeBaseFile, imageFile);

      res.json(result);

    } catch (error) {
      console.error('Create expert error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  async listExperts(req, res) {
    try {
      const userId = req.user?.user_id;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      const result = await expertService.listUserExperts(userId);
      res.json(result);
    } catch (error) {
      console.error('List experts error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to list experts',
        message: error.message
      });
    }
  }

  async getExpert(req, res) {
    try {
      const { expertId } = req.params;
      const userId = req.user?.user_id;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      const result = await expertService.getExpert(expertId, userId);
      
      if (!result.success) {
        return res.status(404).json(result);
      }

      res.json(result);
    } catch (error) {
      console.error('Get expert error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get expert',
        message: error.message
      });
    }
  }

  async updateExpert(req, res) {
    try {
      const { expertId } = req.params;
      const { name, description, systemPrompt, model, pricingPercentage, isPublic, voiceEnabled, labels } = req.body;
      const userId = req.user?.user_id;
      const knowledgeBaseFile = req.files?.file?.[0];
      const imageFile = req.files?.image?.[0];

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      // Parse labels if provided
      let parsedLabels = [];
      if (labels) {
        try {
          parsedLabels = typeof labels === 'string' ? JSON.parse(labels) : labels;
        } catch (error) {
          return res.status(400).json({
            success: false,
            error: 'Invalid labels format'
          });
        }
      }

      // Validate required fields if provided
      const updateData = {};
      if (name !== undefined) updateData.name = name.trim();
      if (description !== undefined) updateData.description = description?.trim() || '';
      if (systemPrompt !== undefined) updateData.systemPrompt = systemPrompt.trim();
      if (model !== undefined) updateData.model = model;
      if (pricingPercentage !== undefined) updateData.pricingPercentage = parseFloat(pricingPercentage);
      if (isPublic !== undefined) updateData.isPublic = isPublic === 'true' || isPublic === true;
      if (voiceEnabled !== undefined) updateData.voiceEnabled = voiceEnabled === 'true' || voiceEnabled === true;
      if (labels !== undefined) updateData.labels = parsedLabels;

      // Validate the update data
      const validationErrors = expertService.validateExpertUpdateData(updateData);

      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: validationErrors
        });
      }

      // Process expert update
      const result = await expertService.updateExpert(expertId, userId, updateData, knowledgeBaseFile, imageFile);

      if (!result.success) {
        return res.status(404).json(result);
      }

      res.json(result);

    } catch (error) {
      console.error('Update expert error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  async getAvailableModels(req, res) {
    try {
      const result = expertService.getAvailableModels();
      res.json(result);
    } catch (error) {
      console.error('Get available models error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  async getModelPricing(req, res) {
    try {
      const { model } = req.params;
      const pricing = expertService.getModelPricing(model);
      
      if (!pricing) {
        return res.status(404).json({
          success: false,
          error: 'Model not found'
        });
      }

      res.json({
        success: true,
        model,
        pricing
      });
    } catch (error) {
      console.error('Get model pricing error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  async calculateCost(req, res) {
    try {
      const { model, inputTokens, outputTokens, pricingPercentage = 0 } = req.body;

      if (!model || inputTokens === undefined || outputTokens === undefined) {
        return res.status(400).json({
          success: false,
          error: 'Model, inputTokens, and outputTokens are required'
        });
      }

      if (!expertService.isValidModel(model)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid model specified'
        });
      }

      // Calculate cost breakdown in both currencies
      const costIDR = expertService.calculateExpertTokenCost(model, inputTokens, outputTokens, pricingPercentage, 'IDR');
      const costUSD = expertService.calculateExpertTokenCost(model, inputTokens, outputTokens, pricingPercentage, 'USD');

      res.json({
        success: true,
        model,
        inputTokens,
        outputTokens,
        pricingPercentage,
        costBreakdown: {
          IDR: costIDR,
          USD: costUSD
        }
      });
    } catch (error) {
      console.error('Calculate cost error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  async getExpertStats(req, res) {
    try {
      const { expertId } = req.params;
      const userId = req.user?.user_id;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      const result = await expertService.getExpertStats(expertId, userId);
      
      if (!result.success) {
        return res.status(404).json(result);
      }

      res.json(result);
    } catch (error) {
      console.error('Get expert stats error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get expert statistics',
        message: error.message
      });
    }
  }

  // Get public experts for marketplace (no authentication required)
  async getPublicExperts(req, res) {
    try {
      const result = await expertService.getPublicExperts();
      res.json(result);
    } catch (error) {
      console.error('Get public experts error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get public experts',
        message: error.message
      });
    }
  }
}

module.exports = new ExpertController();