// Run All Database Schema Enhancement Migrations
// Created: 2025-08-14
// Description: Execute all database schema enhancement migrations in sequence

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Import all migration modules
const migration002 = require('./002_add_expert_enhancement_columns');
const migration003 = require('./003_create_reviews_ratings_tables');
const migration004 = require('./004_create_expert_sharing_system');
const migration005 = require('./005_create_ai_generation_tracking');

async function runAllMigrations() {
  console.log('🚀 Starting database schema enhancement migrations...\n');
  
  const migrations = [
    { name: '002 - Add Expert Enhancement Columns', module: migration002 },
    { name: '003 - Create Reviews and Ratings Tables', module: migration003 },
    { name: '004 - Create Expert Sharing System', module: migration004 },
    { name: '005 - Create AI Generation Tracking', module: migration005 }
  ];
  
  try {
    for (const migration of migrations) {
      console.log(`📋 Running ${migration.name}...`);
      await migration.module.up();
      console.log(`✅ ${migration.name} completed\n`);
    }
    
    console.log('🎉 All database schema enhancement migrations completed successfully!');
    console.log('\n📊 Summary of changes:');
    console.log('- Added expert enhancement columns (first_message, voice_enabled, total_chats, etc.)');
    console.log('- Created reviews table with rating validation and automatic triggers');
    console.log('- Implemented expert sharing system with privacy controls');
    console.log('- Added AI generation tracking and multimedia message support');
    console.log('- Created stored procedures and views for enhanced functionality');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('\n🔄 You may need to run individual migrations or check database state');
    process.exit(1);
  }
}

async function rollbackAllMigrations() {
  console.log('🔄 Rolling back all database schema enhancement migrations...\n');
  
  const migrations = [
    { name: '005 - AI Generation Tracking', module: migration005 },
    { name: '004 - Expert Sharing System', module: migration004 },
    { name: '003 - Reviews and Ratings Tables', module: migration003 },
    { name: '002 - Expert Enhancement Columns', module: migration002 }
  ];
  
  try {
    for (const migration of migrations) {
      console.log(`📋 Rolling back ${migration.name}...`);
      await migration.module.down();
      console.log(`✅ ${migration.name} rollback completed\n`);
    }
    
    console.log('🎉 All migrations rolled back successfully!');
    
  } catch (error) {
    console.error('❌ Rollback failed:', error.message);
    process.exit(1);
  }
}

// Command line interface
const command = process.argv[2];

if (command === 'rollback') {
  rollbackAllMigrations().then(() => {
    console.log('Rollback completed');
    process.exit(0);
  }).catch(err => {
    console.error('Rollback failed:', err);
    process.exit(1);
  });
} else {
  runAllMigrations().then(() => {
    console.log('All migrations completed');
    process.exit(0);
  }).catch(err => {
    console.error('Migrations failed:', err);
    process.exit(1);
  });
}

module.exports = { runAllMigrations, rollbackAllMigrations };