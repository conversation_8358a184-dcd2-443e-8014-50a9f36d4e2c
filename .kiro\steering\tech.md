# Technology Stack

## Architecture
- **Frontend**: Next.js 15 with React 19, TypeScript
- **Backend**: Node.js with Express.js
- **Database**: MySQL/MariaDB
- **AI Integration**: OpenAI API (GPT models, DALL-E, Whisper, TTS)
- **Authentication**: JWT tokens
- **File Storage**: Local uploads directory

## Frontend Stack
- **Framework**: Next.js 15 with App Router
- **UI Library**: Radix UI components
- **Styling**: Tailwind CSS v4
- **State Management**: TanStack React Query
- **Forms**: React Hook Form with Zod validation
- **HTTP Client**: Axios
- **Icons**: Lucide React

## Backend Stack
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL2 driver
- **Authentication**: bcryptjs for password hashing
- **File Upload**: Multer
- **API Documentation**: Swagger (swagger-jsdoc, swagger-ui-express)
- **Environment**: dotenv
- **CORS**: cors middleware

## Development Tools
- **Frontend Dev**: Turbopack for fast development
- **Backend Dev**: Nodemon for auto-restart
- **Linting**: ESLint with Next.js config
- **Type Checking**: TypeScript

## Common Commands

### Frontend (fe/ directory)
```bash
npm run dev          # Start development server with Turbopack
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Backend (be/ directory)
```bash
npm run dev          # Start with nodemon (auto-restart)
npm start            # Start production server
npm run logs         # View application logs
npm run logs:today   # View today's logs
npm run logs:stats   # View log statistics
npm run logs:clean   # Clean old logs
```

## API Integration
- OpenAI Assistants API for AI expert functionality
- OpenAI DALL-E for image generation
- OpenAI Whisper for speech-to-text
- OpenAI TTS for text-to-speech
- Real-time streaming responses for chat

## Database Schema
- Users, experts, chat sessions, messages
- Transaction and balance tracking
- Affiliate and commission systems
- File and knowledge base management