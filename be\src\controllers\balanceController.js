const BalanceService = require('../services/balanceService');
const CurrencyUtils = require('../utils/currencyUtils');

class BalanceController {
  
  /**
   * Get user's balance information
   */
  static async getBalance(req, res) {
    try {
      const userId = req.user.user_id;
      const balance = await BalanceService.getUserBalance(userId);
      
      res.json({
        success: true,
        data: balance
      });
    } catch (error) {
      console.error('Error in getBalance:', error);
      res.status(500).json({
        success: false,
        message: 'Error retrieving balance',
        error: error.message
      });
    }
  }

  /**
   * Get balance summary with transaction history
   */
  static async getBalanceSummary(req, res) {
    try {
      console.log('🔍 Debug - Full req.user object:', JSON.stringify(req.user, null, 2));
      console.log('🔍 Debug - req.user type:', typeof req.user);
      console.log('🔍 Debug - req.user.user_id:', req.user?.user_id);
      console.log('🔍 Debug - req.user.user_id type:', typeof req.user?.user_id);
      
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
      }
      
      const userId = req.user.user_id;
      console.log('🔍 Debug - userId extracted:', userId, 'type:', typeof userId);
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          message: 'User ID not found in request'
        });
      }
      
      const summary = await BalanceService.getBalanceSummary(userId);
      
      res.json({
        success: true,
        data: summary
      });
    } catch (error) {
      console.error('Error in getBalanceSummary:', error);
      res.status(500).json({
        success: false,
        message: 'Error retrieving balance summary',
        error: error.message
      });
    }
  }

  /**
   * Get point transaction history
   */
  static async getPointHistory(req, res) {
    try {
      const userId = req.user.user_id;
      const { limit = 50, offset = 0 } = req.query;
      
      const history = await BalanceService.getPointHistory(
        userId, 
        parseInt(limit), 
        parseInt(offset)
      );
      
      res.json({
        success: true,
        data: history
      });
    } catch (error) {
      console.error('Error in getPointHistory:', error);
      res.status(500).json({
        success: false,
        message: 'Error retrieving point history',
        error: error.message
      });
    }
  }

  /**
   * Get credit transaction history
   */
  static async getCreditHistory(req, res) {
    try {
      const userId = req.user.user_id;
      const { limit = 50, offset = 0 } = req.query;
      
      const history = await BalanceService.getCreditHistory(
        userId, 
        parseInt(limit), 
        parseInt(offset)
      );
      
      res.json({
        success: true,
        data: history
      });
    } catch (error) {
      console.error('Error in getCreditHistory:', error);
      res.status(500).json({
        success: false,
        message: 'Error retrieving credit history',
        error: error.message
      });
    }
  }

  /**
   * Check if user can afford a specific amount
   */
  static async checkAffordability(req, res) {
    try {
      const userId = req.user.user_id;
      const { amount } = req.body;
      
      if (!amount || amount <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Valid amount is required'
        });
      }
      
      const affordability = await BalanceService.canAfford(userId, parseFloat(amount));
      
      res.json({
        success: true,
        data: affordability
      });
    } catch (error) {
      console.error('Error in checkAffordability:', error);
      res.status(500).json({
        success: false,
        message: 'Error checking affordability',
        error: error.message
      });
    }
  }

  /**
   * Process top-up (add credits) - Admin or payment gateway
   */
  static async processTopUp(req, res) {
    try {
      const { userId, amount, paymentMethod, paymentReference } = req.body;
      
      if (!userId || !amount || !paymentMethod || !paymentReference) {
        return res.status(400).json({
          success: false,
          message: 'User ID, amount, payment method, and payment reference are required'
        });
      }
      
      if (amount <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Amount must be positive'
        });
      }
      
      const result = await BalanceService.processTopUp(
        userId, 
        parseFloat(amount), 
        paymentMethod, 
        paymentReference
      );
      
      res.json({
        success: true,
        message: 'Top-up processed successfully',
        data: result
      });
    } catch (error) {
      console.error('Error in processTopUp:', error);
      res.status(500).json({
        success: false,
        message: 'Error processing top-up',
        error: error.message
      });
    }
  }

  /**
   * Add points to user - Admin only
   */
  static async addPoints(req, res) {
    try {
      const { userId, amount, description, referenceId, referenceType, expiresAt } = req.body;
      const createdBy = req.user.user_id; // Admin who is adding points
      
      if (!userId || !amount || !description) {
        return res.status(400).json({
          success: false,
          message: 'User ID, amount, and description are required'
        });
      }
      
      if (amount <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Amount must be positive'
        });
      }
      
      // Parse expiry date if provided
      let expiryDate = null;
      if (expiresAt) {
        expiryDate = new Date(expiresAt);
        if (isNaN(expiryDate.getTime())) {
          return res.status(400).json({
            success: false,
            message: 'Invalid expiry date format'
          });
        }
      }
      
      const result = await BalanceService.addPoints(
        userId, 
        parseFloat(amount), 
        description,
        referenceId,
        referenceType,
        expiryDate,
        createdBy
      );
      
      res.json({
        success: true,
        message: 'Points added successfully',
        data: result
      });
    } catch (error) {
      console.error('Error in addPoints:', error);
      res.status(500).json({
        success: false,
        message: 'Error adding points',
        error: error.message
      });
    }
  }

  /**
   * Get balance information for cost estimation
   */
  static async getBalanceForCostEstimation(req, res) {
    try {
      const userId = req.user.user_id;
      const { estimatedCost } = req.query;
      
      const balance = await BalanceService.getUserBalance(userId);
      
      let affordabilityInfo = null;
      if (estimatedCost && estimatedCost > 0) {
        affordabilityInfo = await BalanceService.canAfford(userId, parseFloat(estimatedCost));
      }
      
      res.json({
        success: true,
        data: {
          balance,
          affordabilityInfo,
          costEstimation: estimatedCost ? {
            estimatedCost: parseFloat(estimatedCost),
            formattedCost: CurrencyUtils.formatIDR(estimatedCost),
            breakdown: affordabilityInfo ? CurrencyUtils.formatBalanceBreakdown(
              affordabilityInfo.pointsToUse,
              affordabilityInfo.creditsToUse,
              estimatedCost
            ) : null
          } : null
        }
      });
    } catch (error) {
      console.error('Error in getBalanceForCostEstimation:', error);
      res.status(500).json({
        success: false,
        message: 'Error retrieving balance for cost estimation',
        error: error.message
      });
    }
  }
}

module.exports = BalanceController;
