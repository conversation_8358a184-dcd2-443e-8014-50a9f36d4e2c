const expertService = require('../services/expertService');

class StreamingCostCalculator {
  constructor() {
    this.activeCosts = new Map(); // Track costs for active streams
  }

  // Initialize cost tracking for a stream
  initializeStream(streamId, model, pricingPercentage) {
    this.activeCosts.set(streamId, {
      model,
      pricingPercentage,
      promptTokens: 0,
      completionTokens: 0,
      estimatedCost: 0,
      startTime: Date.now()
    });
  }

  // Update token counts and calculate real-time cost
  updateTokens(streamId, promptTokens = 0, completionTokens = 0) {
    const streamCost = this.activeCosts.get(streamId);
    if (!streamCost) {
      return null;
    }

    streamCost.promptTokens = promptTokens;
    streamCost.completionTokens = completionTokens;

    // Calculate current cost
    const costResult = expertService.calculateExpertTokenCost(
      streamCost.model,
      promptTokens,
      completionTokens,
      streamCost.pricingPercentage,
      'IDR'
    );

    streamCost.estimatedCost = costResult.totalPrice;
    streamCost.lastUpdate = Date.now();

    return {
      streamId,
      promptTokens,
      completionTokens,
      totalTokens: promptTokens + completionTokens,
      estimatedCost: costResult.totalPrice,
      costBreakdown: {
        basePrice: costResult.basePrice,
        platformCommission: costResult.platformCommission,
        expertCommission: costResult.expertCommission,
        totalPrice: costResult.totalPrice
      }
    };
  }

  // Estimate cost based on response length (for real-time updates)
  estimateCostFromResponse(streamId, responseLength) {
    const streamCost = this.activeCosts.get(streamId);
    if (!streamCost) {
      return null;
    }

    // Rough estimation: 1 token ≈ 4 characters
    const estimatedCompletionTokens = Math.ceil(responseLength / 4);
    
    return this.updateTokens(streamId, streamCost.promptTokens, estimatedCompletionTokens);
  }

  // Get current cost for a stream
  getCurrentCost(streamId) {
    const streamCost = this.activeCosts.get(streamId);
    if (!streamCost) {
      return null;
    }

    return {
      streamId,
      promptTokens: streamCost.promptTokens,
      completionTokens: streamCost.completionTokens,
      totalTokens: streamCost.promptTokens + streamCost.completionTokens,
      estimatedCost: streamCost.estimatedCost,
      model: streamCost.model,
      pricingPercentage: streamCost.pricingPercentage,
      duration: Date.now() - streamCost.startTime
    };
  }

  // Finalize cost calculation and clean up
  finalizeStream(streamId, finalTokens = null) {
    const streamCost = this.activeCosts.get(streamId);
    if (!streamCost) {
      return null;
    }

    let finalCost = streamCost.estimatedCost;
    
    if (finalTokens) {
      const costResult = expertService.calculateExpertTokenCost(
        streamCost.model,
        finalTokens.prompt_tokens || streamCost.promptTokens,
        finalTokens.completion_tokens || streamCost.completionTokens,
        streamCost.pricingPercentage,
        'IDR'
      );
      finalCost = costResult.totalPrice;
    }

    const result = {
      streamId,
      finalCost,
      duration: Date.now() - streamCost.startTime,
      totalTokens: (finalTokens?.total_tokens) || (streamCost.promptTokens + streamCost.completionTokens)
    };

    // Clean up
    this.activeCosts.delete(streamId);

    return result;
  }

  // Clean up expired streams (older than 1 hour)
  cleanupExpiredStreams() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    for (const [streamId, streamCost] of this.activeCosts) {
      if (now - streamCost.startTime > oneHour) {
        this.activeCosts.delete(streamId);
      }
    }
  }

  // Get all active streams (for monitoring)
  getActiveStreams() {
    return Array.from(this.activeCosts.entries()).map(([streamId, cost]) => ({
      streamId,
      ...cost,
      duration: Date.now() - cost.startTime
    }));
  }
}

// Create singleton instance
const streamingCostCalculator = new StreamingCostCalculator();

// Clean up expired streams every 30 minutes
setInterval(() => {
  streamingCostCalculator.cleanupExpiredStreams();
}, 30 * 60 * 1000);

module.exports = streamingCostCalculator;