"use client";

import React, { useState } from "react";
import Image from "next/image";
import { api } from "@/lib/api";
import { asset } from "@/lib/utils";

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  voiceEnabled?: boolean;
  pricingPercentage: number;
  isPublic: boolean;
  labels: string[];
  createdAt: string;
  updatedAt: string;
}

interface EditExpertProps {
  expert: Expert;
  onExpertUpdated?: (expert: any) => void;
  onCancel?: () => void;
}

const EditExpert: React.FC<EditExpertProps> = ({
  expert,
  onExpertUpdated,
  onCancel,
}) => {
  const [formData, setFormData] = useState({
    name: expert.name,
    description: expert.description || "",
    systemPrompt: expert.systemPrompt,
    model: expert.model,
    pricingPercentage: expert.pricingPercentage.toString(),
    isPublic: expert.isPublic || false,
    voiceEnabled: expert.voiceEnabled || false,
  });
  const [labels, setLabels] = useState<string[]>(expert.labels || []);
  const [currentLabel, setCurrentLabel] = useState("");
  const [knowledgeBaseFile, setKnowledgeBaseFile] = useState<File | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const models = [
    { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo" },
    { value: "gpt-4", label: "GPT-4" },
    { value: "gpt-4-turbo", label: "GPT-4 Turbo" },
    { value: "gpt-4o", label: "GPT-4o" },
    { value: "gpt-4o-mini", label: "GPT-4o Mini" },
  ];

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleAddLabel = () => {
    if (
      currentLabel.trim() &&
      labels.length < 5 &&
      !labels.includes(currentLabel.trim())
    ) {
      setLabels((prev) => [...prev, currentLabel.trim()]);
      setCurrentLabel("");
    }
  };

  const handleRemoveLabel = (indexToRemove: number) => {
    setLabels((prev) => prev.filter((_, index) => index !== indexToRemove));
  };

  const handleLabelKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddLabel();
    }
  };

  const handleKnowledgeBaseFileChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Validate file type for knowledge base
      const validTypes = [
        "application/pdf",
        "text/plain",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/msword",
        "text/markdown",
        "application/json",
      ];
      const validExtensions = [".pdf", ".txt", ".docx", ".doc", ".md", ".json"];
      const fileExtension = selectedFile.name
        .toLowerCase()
        .substring(selectedFile.name.lastIndexOf("."));

      if (
        validTypes.includes(selectedFile.type) ||
        validExtensions.includes(fileExtension)
      ) {
        setKnowledgeBaseFile(selectedFile);
        setError(null);
      } else {
        setError(
          "Invalid knowledge base file type. Supported formats: PDF, TXT, DOCX, DOC, MD, JSON"
        );
        setKnowledgeBaseFile(null);
      }
    }
  };

  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Validate file type for images
      const validImageTypes = [
        "image/png",
        "image/jpeg",
        "image/jpg",
        "image/gif",
        "image/webp",
      ];
      const validImageExtensions = [".png", ".jpg", ".jpeg", ".gif", ".webp"];
      const fileExtension = selectedFile.name
        .toLowerCase()
        .substring(selectedFile.name.lastIndexOf("."));

      // Check file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes

      if (selectedFile.size > maxSize) {
        setError("Image file size must be less than 10MB");
        setImageFile(null);
        return;
      }

      if (
        validImageTypes.includes(selectedFile.type) ||
        validImageExtensions.includes(fileExtension)
      ) {
        setImageFile(selectedFile);
        setError(null);
      } else {
        setError(
          "Invalid image file type. Supported formats: PNG, JPG, JPEG, GIF, WEBP"
        );
        setImageFile(null);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name.trim() || !formData.systemPrompt.trim()) {
      setError("Name and system prompt are required");
      return;
    }

    // Validate pricing percentage
    const pricingPercent = parseFloat(formData.pricingPercentage);
    if (isNaN(pricingPercent) || pricingPercent < 0 || pricingPercent > 100) {
      setError("Pricing percentage must be between 0 and 100");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await api.updateExpert(
        expert.id,
        {
          name: formData.name.trim(),
          description: formData.description.trim(),
          systemPrompt: formData.systemPrompt.trim(),
          model: formData.model,
          pricingPercentage: pricingPercent,
          isPublic: formData.isPublic,
          voiceEnabled: formData.voiceEnabled,
          labels: labels,
        },
        knowledgeBaseFile,
        imageFile
      );

      if (result.success) {
        onExpertUpdated?.(result.expert);
      } else {
        setError(result.error || "Failed to update expert");
      }
    } catch (err: any) {
      setError(err.message || "Failed to update expert");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Edit AI Expert</h2>

      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name */}
        <div>
          <label
            htmlFor="name"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Expert Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter expert name"
          />
        </div>

        {/* Description */}
        <div>
          <label
            htmlFor="description"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Describe what this expert does"
          />
        </div>

        {/* System Prompt */}
        <div>
          <label
            htmlFor="systemPrompt"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            System Prompt *
          </label>
          <textarea
            id="systemPrompt"
            name="systemPrompt"
            value={formData.systemPrompt}
            onChange={handleInputChange}
            required
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Define the expert's behavior and knowledge"
          />
        </div>

        {/* Model and Pricing */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label
              htmlFor="model"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              AI Model
            </label>
            <select
              id="model"
              name="model"
              value={formData.model}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {models.map((model) => (
                <option key={model.value} value={model.value}>
                  {model.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label
              htmlFor="pricingPercentage"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Pricing Percentage (%)
            </label>
            <input
              type="number"
              id="pricingPercentage"
              name="pricingPercentage"
              value={formData.pricingPercentage}
              onChange={handleInputChange}
              min="0"
              max="100"
              step="0.01"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="0.00"
            />
          </div>
        </div>

        {/* Public Toggle */}
        <div>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              name="isPublic"
              checked={formData.isPublic}
              onChange={handleInputChange}
              className="rounded border-gray-300 focus:ring-2 focus:ring-blue-500"
            />
            <span className="text-sm font-medium text-gray-700">
              Make this expert public
            </span>
          </label>
          <p className="text-xs text-gray-500 mt-1">
            Public experts can be discovered and used by other users
          </p>
        </div>

        {/* Voice Settings */}
        <div>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              name="voiceEnabled"
              checked={formData.voiceEnabled}
              onChange={handleInputChange}
              className="rounded border-gray-300 focus:ring-2 focus:ring-blue-500"
            />
            <span className="text-sm font-medium text-gray-700">
              Enable voice features (Text-to-Speech)
            </span>
          </label>
          <p className="text-xs text-gray-500 mt-1">
            {formData.voiceEnabled ? 'Users can listen to AI responses with voice synthesis' : 'Voice features are disabled for this expert'}
          </p>
        </div>

        {/* File Uploads */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label
              htmlFor="knowledgeBase"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Knowledge Base File (Optional)
            </label>
            <input
              type="file"
              id="knowledgeBase"
              onChange={handleKnowledgeBaseFileChange}
              accept=".pdf,.txt,.docx,.doc,.md,.json"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              Upload a new file to update the knowledge base. Supported: PDF,
              TXT, DOCX, DOC, MD, JSON
            </p>
          </div>

          <div>
            <label
              htmlFor="image"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Expert Image (Optional)
            </label>
            <input
              type="file"
              id="image"
              onChange={handleImageFileChange}
              accept="image/*"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              Upload a new image to replace the current one. Max 10MB.
              Supported: PNG, JPG, JPEG, GIF, WEBP
            </p>
            {expert.imageUrl && (
              <div className="mt-2">
                <p className="text-xs text-gray-600">Current image:</p>
                <Image
                  src={asset(expert.imageUrl)}
                  alt="Current expert image"
                  width={64}
                  height={64}
                  className="w-16 h-16 object-cover rounded border mt-1"
                />
              </div>
            )}
          </div>
        </div>

        {/* Labels */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Labels (Optional)
          </label>

          <div className="space-y-3">
            <div className="flex space-x-2">
              <input
                type="text"
                value={currentLabel}
                onChange={(e) => setCurrentLabel(e.target.value)}
                onKeyPress={handleLabelKeyPress}
                maxLength={50}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Add a label (max 50 characters)"
              />
              <button
                type="button"
                onClick={handleAddLabel}
                disabled={
                  !currentLabel.trim() ||
                  labels.length >= 5 ||
                  labels.includes(currentLabel.trim())
                }
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add
              </button>
            </div>

            {labels.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {labels.map((label, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                  >
                    {label}
                    <button
                      type="button"
                      onClick={() => handleRemoveLabel(index)}
                      className="ml-2 text-blue-600 hover:text-blue-800 focus:outline-none"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}

            <p className="text-xs text-gray-500">
              {labels.length}/5 labels used. Labels help users discover your
              expert.
            </p>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isLoading}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? "Updating..." : "Update Expert"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditExpert;
