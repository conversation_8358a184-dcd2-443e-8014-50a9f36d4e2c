// Simple logger utility
const logger = {
  info: (message, data) => {
    console.log(`[INFO] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  },
  error: (message, data) => {
    console.error(`[ERROR] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  },
  debug: (message, data) => {
    console.log(`[DEBUG] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  }
};
const chatService = require('../services/chatService');
const expertService = require('../services/expertService');
const ChatSessionService = require('../services/chatSessionService');
const streamingCostCalculator = require('../utils/streamingCostCalculator');

class ChatSocketHandler {
  constructor() {
    this.activeStreams = new Map(); // Track active streaming sessions
  }

  // Initialize chat socket handlers
  initialize(io) {
    io.on('connection', (socket) => {
      // Handle starting a chat stream
      socket.on('start_chat_stream', async (data) => {
        try {
          await this.handleChatStream(socket, data);
        } catch (error) {
          logger.error('Chat stream error', {
            socketId: socket.id,
            userId: socket.userId,
            error: error.message
          });
          socket.emit('chat_error', {
            message: 'Failed to start chat stream',
            error: error.message
          });
        }
      });

      // Handle stopping a chat stream
      socket.on('stop_chat_stream', () => {
        this.stopChatStream(socket);
      });

      // Handle voice message
      socket.on('voice_message', async (data) => {
        try {
          await this.handleVoiceMessage(socket, data);
        } catch (error) {
          logger.error('Voice message error', {
            socketId: socket.id,
            userId: socket.userId,
            error: error.message
          });
          socket.emit('voice_error', {
            message: 'Failed to process voice message',
            error: error.message
          });
        }
      });

      // Clean up on disconnect
      socket.on('disconnect', () => {
        this.stopChatStream(socket);
      });
    });
  }

  // Handle chat streaming
  async handleChatStream(socket, data) {
    const { message, expertId, sessionId } = data;
    
    if (!message || !expertId) {
      throw new Error('Message and expertId are required');
    }

    // Validate user is in correct chat room
    if (!socket.currentChatRoom || socket.currentExpertId !== expertId) {
      throw new Error('User not in correct chat room');
    }

    logger.info('Starting chat stream', {
      socketId: socket.id,
      userId: socket.userId,
      expertId,
      sessionId,
      messageLength: message.length
    });

    // Emit typing indicator
    socket.to(socket.currentChatRoom).emit('typing_start', {
      userId: socket.userId,
      expertId
    });

    // Store stream reference
    const streamId = `${socket.userId}_${expertId}_${Date.now()}`;
    this.activeStreams.set(socket.id, {
      streamId,
      expertId,
      sessionId,
      startTime: Date.now(),
      userId: socket.userId
    });

    // Emit stream started event
    socket.emit('stream_started', {
      streamId,
      expertId,
      sessionId
    });

    try {
      // Get or create chat session
      let currentSessionId = sessionId;
      let threadId = null;
      
      if (sessionId) {
        // Get existing session
        const sessionResult = await ChatSessionService.getSessionById(sessionId, socket.userId);
        if (sessionResult.success) {
          threadId = sessionResult.session.thread_id;
        }
      } else {
        // Create new session
        const sessionResult = await ChatSessionService.createOrGetSession(socket.userId, expertId);
        if (sessionResult.success) {
          currentSessionId = sessionResult.session.id;
          threadId = sessionResult.session.thread_id;
        } else {
          throw new Error('Failed to create chat session');
        }
      }

      // Get expert context
      const expertResult = await expertService.getExpert(expertId, socket.userId);
      if (!expertResult.success) {
        throw new Error('Expert not found');
      }

      const expertContext = {
        assistantId: expertResult.expert.assistantId,
        model: expertResult.expert.model,
        name: expertResult.expert.name,
        pricingPercentage: expertResult.expert.pricingPercentage
      };

      // Save user message to database first
      const saveUserMessageResult = await ChatSessionService.saveMessage(
        currentSessionId,
        threadId,
        'user',
        message,
        0, // tokens will be calculated by the AI service
        0  // cost will be calculated based on expert pricing
      );

      if (!saveUserMessageResult.success) {
        throw new Error('Failed to save user message');
      }

      // Process chat with streaming
      const result = await chatService.processChatStreaming(
        message,
        threadId.startsWith('temp_') ? null : threadId,
        socket.userId,
        expertContext,
        socket,
        streamId
      );

      if (result.success) {
        // Update thread ID if it was a new thread
        if (result.isNewThread && threadId.startsWith('temp_')) {
          await ChatSessionService.updateThreadId(currentSessionId, result.threadId);
        }

        // Save AI response to database
        const costDetails = {
          basePrice: result.costBreakdown?.basePrice || 0,
          expertCommission: result.costBreakdown?.expertCommission || 0,
          inputTokens: result.usage?.prompt_tokens || 0,
          outputTokens: result.usage?.completion_tokens || 0
        };

        await ChatSessionService.saveMessage(
          currentSessionId,
          result.threadId,
          'assistant',
          result.response,
          result.tokensUsed || 0,
          result.cost || 0,
          costDetails,
          result.balanceUsage
        );

        logger.info('Chat stream completed successfully', {
          socketId: socket.id,
          userId: socket.userId,
          streamId,
          tokensUsed: result.tokensUsed,
          cost: result.cost
        });
      }

    } catch (error) {
      logger.error('Chat stream error', {
        socketId: socket.id,
        userId: socket.userId,
        streamId,
        error: error.message
      });

      socket.emit('stream_error', {
        streamId,
        error: error.message || 'Failed to process chat stream'
      });
    } finally {
      // Clean up
      this.activeStreams.delete(socket.id);
      socket.to(socket.currentChatRoom).emit('typing_stop', {
        userId: socket.userId,
        expertId
      });
    }
  }

  // Handle voice message processing
  async handleVoiceMessage(socket, data) {
    const { audioData, expertId, sessionId } = data;
    
    if (!audioData || !expertId) {
      throw new Error('Audio data and expertId are required');
    }

    logger.info('Processing voice message', {
      socketId: socket.id,
      userId: socket.userId,
      expertId,
      sessionId,
      audioDataSize: audioData.length
    });

    // Emit voice processing status
    socket.emit('voice_processing', {
      status: 'transcribing',
      expertId
    });

    try {
      // Convert base64 audio data to buffer
      const audioBuffer = Buffer.from(audioData, 'base64');
      
      // Create a temporary file for OpenAI Whisper
      const fs = require('fs');
      const path = require('path');
      const tempDir = path.join(__dirname, '../../temp');
      
      // Ensure temp directory exists
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      const tempFilePath = path.join(tempDir, `voice_${socket.userId}_${Date.now()}.webm`);
      fs.writeFileSync(tempFilePath, audioBuffer);

      // Transcribe using OpenAI Whisper
      const openai = require('../config/openai');
      const transcription = await openai.audio.transcriptions.create({
        file: fs.createReadStream(tempFilePath),
        model: 'whisper-1',
      });

      // Clean up temp file
      fs.unlinkSync(tempFilePath);

      const transcript = transcription.text;

      logger.info('Voice transcription completed', {
        socketId: socket.id,
        userId: socket.userId,
        transcript: transcript.substring(0, 100) + '...'
      });

      socket.emit('voice_transcribed', {
        transcript,
        expertId
      });

      // Start chat stream with transcribed text
      await this.handleChatStream(socket, {
        message: transcript,
        expertId,
        sessionId
      });

    } catch (error) {
      logger.error('Voice processing error', {
        socketId: socket.id,
        userId: socket.userId,
        error: error.message
      });

      socket.emit('voice_error', {
        error: error.message || 'Failed to process voice message',
        expertId
      });
    }
  }

  // Stop active chat stream
  stopChatStream(socket) {
    const activeStream = this.activeStreams.get(socket.id);
    if (activeStream) {
      logger.info('Stopping chat stream', {
        socketId: socket.id,
        userId: socket.userId,
        streamId: activeStream.streamId
      });

      socket.emit('stream_stopped', {
        streamId: activeStream.streamId,
        reason: 'user_requested'
      });

      this.activeStreams.delete(socket.id);
    }
  }

  // Get active streams count (for monitoring)
  getActiveStreamsCount() {
    return this.activeStreams.size;
  }

  // Get active streams for a user
  getUserActiveStreams(userId) {
    const userStreams = [];
    for (const [socketId, stream] of this.activeStreams) {
      if (stream.userId === userId) {
        userStreams.push(stream);
      }
    }
    return userStreams;
  }
}

// Create singleton instance
const chatSocketHandler = new ChatSocketHandler();

module.exports = chatSocketHandler;