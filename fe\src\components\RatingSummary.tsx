"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import StarRating from "@/components/ui/star-rating";
import { api } from "@/lib/api";

interface RatingStats {
  total_reviews: number;
  average_rating: string;
  rating_distribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

interface RatingSummaryProps {
  expertId: number;
  className?: string;
}

const RatingSummary: React.FC<RatingSummaryProps> = ({
  expertId,
  className
}) => {
  const [stats, setStats] = useState<RatingStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadStats = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const result = await api.getExpertRatingStats(expertId);
        
        if (result.success) {
          setStats(result.stats);
        } else {
          setError(result.error || "Failed to load rating statistics");
        }
      } catch (err: any) {
        setError(err.message || "Failed to load rating statistics");
      } finally {
        setIsLoading(false);
      }
    };

    loadStats();
  }, [expertId]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg">Rating Summary</CardTitle>
        </CardHeader>
        <CardContent className="animate-pulse">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-16 h-8 bg-gray-200 rounded"></div>
              <div className="w-32 h-6 bg-gray-200 rounded"></div>
            </div>
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="w-8 h-4 bg-gray-200 rounded"></div>
                <div className="flex-1 h-2 bg-gray-200 rounded"></div>
                <div className="w-8 h-4 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !stats) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center text-gray-500">
          {error || "No rating data available"}
        </CardContent>
      </Card>
    );
  }

  if (stats.total_reviews === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg">Rating Summary</CardTitle>
        </CardHeader>
        <CardContent className="text-center text-gray-500">
          <p>No reviews yet</p>
        </CardContent>
      </Card>
    );
  }

  const averageRating = parseFloat(stats.average_rating);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">Rating Summary</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900">
              {averageRating.toFixed(1)}
            </div>
            <StarRating rating={averageRating} size="sm" />
            <div className="text-sm text-gray-500 mt-1">
              {stats.total_reviews} review{stats.total_reviews !== 1 ? 's' : ''}
            </div>
          </div>
          
          <div className="flex-1 space-y-2">
            {[5, 4, 3, 2, 1].map((rating) => {
              const count = stats.rating_distribution[rating as keyof typeof stats.rating_distribution];
              const percentage = stats.total_reviews > 0 ? (count / stats.total_reviews) * 100 : 0;
              
              return (
                <div key={rating} className="flex items-center gap-2 text-sm">
                  <div className="flex items-center gap-1 w-12">
                    <span>{rating}</span>
                    <StarRating rating={1} maxRating={1} size="sm" />
                  </div>
                  <Progress 
                    value={percentage} 
                    className="flex-1 h-2"
                  />
                  <span className="w-8 text-right text-gray-600">
                    {count}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RatingSummary;