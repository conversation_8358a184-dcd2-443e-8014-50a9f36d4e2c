// AnalyticsService.js - Service for share analytics and tracking
const mysql = require('mysql2/promise');
const dbConfig = require('../config/database');

class AnalyticsService {
  constructor() {
    this.pool = mysql.createPool(dbConfig);
  }

  /**
   * Log an analytics action
   * @param {string} shareToken - Share token
   * @param {number|null} userId - User ID (null for anonymous)
   * @param {string} actionType - Type of action (view, consent, login, register, chat_start, chat_message)
   * @param {Object} metadata - Additional metadata
   * @param {string} sessionId - Browser session ID
   * @param {string} ipAddress - User IP address
   * @param {string} userAgent - Browser user agent
   * @param {string} referer - Referring page URL
   * @returns {Promise<number>} Analytics record ID
   */
  async logAction(shareToken, userId = null, actionType, metadata = {}, sessionId = null, ipAddress = null, userAgent = null, referer = null) {
    const connection = await this.pool.getConnection();
    
    try {
      // Get share information
      const [shareRows] = await connection.execute(
        'SELECT expert_id, shared_by_user_id FROM expert_shares WHERE share_token = ?',
        [shareToken]
      );
      
      if (shareRows.length === 0) {
        throw new Error('Share not found');
      }
      
      const { expert_id, shared_by_user_id } = shareRows[0];
      
      // Insert analytics record
      const [result] = await connection.execute(
        `INSERT INTO share_analytics 
         (share_token, user_id, action_type, expert_id, shared_by_user_id, session_id, ip_address, user_agent, referer, metadata)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          shareToken, 
          userId, 
          actionType, 
          expert_id, 
          shared_by_user_id, 
          sessionId, 
          ipAddress, 
          userAgent, 
          referer, 
          JSON.stringify(metadata)
        ]
      );
      
      return result.insertId;
      
    } finally {
      connection.release();
    }
  }

  /**
   * Get analytics data for a specific share
   * @param {string} shareToken - Share token
   * @param {number} sharedByUserId - User who created the share (for authorization)
   * @param {number} days - Number of days to look back (default: 30)
   * @returns {Promise<Object>} Analytics data
   */
  async getShareAnalytics(shareToken, sharedByUserId, days = 30) {
    const connection = await this.pool.getConnection();
    
    try {
      // Verify ownership
      const [ownerCheck] = await connection.execute(
        'SELECT id FROM expert_shares WHERE share_token = ? AND shared_by_user_id = ?',
        [shareToken, sharedByUserId]
      );
      
      if (ownerCheck.length === 0) {
        throw new Error('Share not found or access denied');
      }
      
      const dateFilter = `DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL ${days} DAY)`;
      
      // Get action counts by type
      const [actionCounts] = await connection.execute(
        `SELECT 
           action_type,
           COUNT(*) as count,
           COUNT(DISTINCT user_id) as unique_users,
           COUNT(DISTINCT session_id) as unique_sessions
         FROM share_analytics 
         WHERE share_token = ? AND ${dateFilter}
         GROUP BY action_type`,
        [shareToken]
      );
      
      // Get daily breakdown
      const [dailyBreakdown] = await connection.execute(
        `SELECT 
           DATE(created_at) as date,
           action_type,
           COUNT(*) as count,
           COUNT(DISTINCT user_id) as unique_users
         FROM share_analytics 
         WHERE share_token = ? AND ${dateFilter}
         GROUP BY DATE(created_at), action_type
         ORDER BY date DESC`,
        [shareToken]
      );
      
      // Get user journey (conversion funnel)
      const [userJourney] = await connection.execute(
        `SELECT 
           session_id,
           user_id,
           GROUP_CONCAT(action_type ORDER BY created_at) as journey,
           MIN(created_at) as first_action,
           MAX(created_at) as last_action,
           COUNT(*) as total_actions
         FROM share_analytics 
         WHERE share_token = ? AND ${dateFilter} AND session_id IS NOT NULL
         GROUP BY session_id, user_id
         ORDER BY first_action DESC`,
        [shareToken]
      );
      
      // Get referrer data
      const [referrerData] = await connection.execute(
        `SELECT 
           COALESCE(referer, 'Direct') as referrer,
           COUNT(*) as count,
           COUNT(DISTINCT user_id) as unique_users
         FROM share_analytics 
         WHERE share_token = ? AND ${dateFilter} AND action_type = 'view'
         GROUP BY referer
         ORDER BY count DESC
         LIMIT 10`,
        [shareToken]
      );
      
      // Calculate conversion metrics
      const totalViews = actionCounts.find(a => a.action_type === 'view')?.count || 0;
      const totalChatStarts = actionCounts.find(a => a.action_type === 'chat_start')?.count || 0;
      const totalConsents = actionCounts.find(a => a.action_type === 'consent')?.count || 0;
      
      return {
        summary: {
          totalViews,
          totalChatStarts,
          totalConsents,
          conversionRate: totalViews > 0 ? ((totalChatStarts / totalViews) * 100).toFixed(2) : 0,
          consentRate: totalViews > 0 ? ((totalConsents / totalViews) * 100).toFixed(2) : 0
        },
        actionCounts: actionCounts.reduce((acc, item) => {
          acc[item.action_type] = {
            count: item.count,
            uniqueUsers: item.unique_users,
            uniqueSessions: item.unique_sessions
          };
          return acc;
        }, {}),
        dailyBreakdown,
        userJourney,
        referrerData,
        period: {
          days,
          startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0]
        }
      };
      
    } finally {
      connection.release();
    }
  }

  /**
   * Get analytics summary for all shares by a user
   * @param {number} sharedByUserId - User who created the shares
   * @param {number} days - Number of days to look back (default: 30)
   * @returns {Promise<Object>} Aggregated analytics data
   */
  async getUserSharesAnalytics(sharedByUserId, days = 30) {
    const connection = await this.pool.getConnection();
    
    try {
      const dateFilter = `DATE(sa.created_at) >= DATE_SUB(CURDATE(), INTERVAL ${days} DAY)`;
      
      // Get overall stats
      const [overallStats] = await connection.execute(
        `SELECT 
           COUNT(DISTINCT sa.share_token) as total_shares,
           COUNT(CASE WHEN sa.action_type = 'view' THEN 1 END) as total_views,
           COUNT(CASE WHEN sa.action_type = 'chat_start' THEN 1 END) as total_conversions,
           COUNT(CASE WHEN sa.action_type = 'consent' THEN 1 END) as total_consents,
           COUNT(DISTINCT sa.user_id) as unique_users,
           COUNT(DISTINCT sa.session_id) as unique_sessions
         FROM share_analytics sa
         WHERE sa.shared_by_user_id = ? AND ${dateFilter}`,
        [sharedByUserId]
      );
      
      // Get top performing shares
      const [topShares] = await connection.execute(
        `SELECT 
           sa.share_token,
           e.name as expert_name,
           COUNT(CASE WHEN sa.action_type = 'view' THEN 1 END) as views,
           COUNT(CASE WHEN sa.action_type = 'chat_start' THEN 1 END) as conversions,
           COUNT(DISTINCT sa.user_id) as unique_users
         FROM share_analytics sa
         JOIN expert_shares es ON sa.share_token = es.share_token
         JOIN experts e ON es.expert_id = e.id
         WHERE sa.shared_by_user_id = ? AND ${dateFilter}
         GROUP BY sa.share_token, e.name
         ORDER BY conversions DESC, views DESC
         LIMIT 10`,
        [sharedByUserId]
      );
      
      // Get daily trends
      const [dailyTrends] = await connection.execute(
        `SELECT 
           DATE(sa.created_at) as date,
           COUNT(CASE WHEN sa.action_type = 'view' THEN 1 END) as views,
           COUNT(CASE WHEN sa.action_type = 'chat_start' THEN 1 END) as conversions,
           COUNT(DISTINCT sa.user_id) as unique_users
         FROM share_analytics sa
         WHERE sa.shared_by_user_id = ? AND ${dateFilter}
         GROUP BY DATE(sa.created_at)
         ORDER BY date DESC`,
        [sharedByUserId]
      );
      
      const stats = overallStats[0];
      
      return {
        summary: {
          totalShares: stats.total_shares,
          totalViews: stats.total_views,
          totalConversions: stats.total_conversions,
          totalConsents: stats.total_consents,
          uniqueUsers: stats.unique_users,
          uniqueSessions: stats.unique_sessions,
          conversionRate: stats.total_views > 0 ? ((stats.total_conversions / stats.total_views) * 100).toFixed(2) : 0,
          consentRate: stats.total_views > 0 ? ((stats.total_consents / stats.total_views) * 100).toFixed(2) : 0
        },
        topShares: topShares.map(share => ({
          shareToken: share.share_token,
          expertName: share.expert_name,
          views: share.views,
          conversions: share.conversions,
          uniqueUsers: share.unique_users,
          conversionRate: share.views > 0 ? ((share.conversions / share.views) * 100).toFixed(2) : 0
        })),
        dailyTrends,
        period: {
          days,
          startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0]
        }
      };
      
    } finally {
      connection.release();
    }
  }

  /**
   * Get real-time analytics for monitoring dashboard
   * @param {number} sharedByUserId - User who created the shares
   * @param {number} hours - Number of hours to look back (default: 24)
   * @returns {Promise<Object>} Real-time analytics data
   */
  async getRealTimeAnalytics(sharedByUserId, hours = 24) {
    const connection = await this.pool.getConnection();
    
    try {
      const timeFilter = `sa.created_at >= DATE_SUB(NOW(), INTERVAL ${hours} HOUR)`;
      
      // Get recent activity
      const [recentActivity] = await connection.execute(
        `SELECT 
           sa.share_token,
           sa.action_type,
           sa.created_at,
           sa.user_id,
           u.username,
           e.name as expert_name
         FROM share_analytics sa
         LEFT JOIN user u ON sa.user_id = u.user_id
         JOIN expert_shares es ON sa.share_token = es.share_token
         JOIN experts e ON es.expert_id = e.id
         WHERE sa.shared_by_user_id = ? AND ${timeFilter}
         ORDER BY sa.created_at DESC
         LIMIT 50`,
        [sharedByUserId]
      );
      
      // Get active sessions
      const [activeSessions] = await connection.execute(
        `SELECT 
           sal.share_token,
           sal.user_id,
           u.username,
           e.name as expert_name,
           sal.access_started_at,
           sal.last_activity_at,
           sal.message_count,
           sal.consent_given
         FROM share_access_logs sal
         JOIN user u ON sal.user_id = u.user_id
         JOIN experts e ON sal.expert_id = e.id
         WHERE sal.shared_by_user_id = ? 
           AND sal.last_activity_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
         ORDER BY sal.last_activity_at DESC`,
        [sharedByUserId]
      );
      
      // Get hourly stats
      const [hourlyStats] = await connection.execute(
        `SELECT 
           HOUR(sa.created_at) as hour,
           COUNT(CASE WHEN sa.action_type = 'view' THEN 1 END) as views,
           COUNT(CASE WHEN sa.action_type = 'chat_start' THEN 1 END) as conversions,
           COUNT(DISTINCT sa.user_id) as unique_users
         FROM share_analytics sa
         WHERE sa.shared_by_user_id = ? AND ${timeFilter}
         GROUP BY HOUR(sa.created_at)
         ORDER BY hour`,
        [sharedByUserId]
      );
      
      return {
        recentActivity: recentActivity.map(activity => ({
          shareToken: activity.share_token,
          actionType: activity.action_type,
          timestamp: activity.created_at,
          user: {
            id: activity.user_id,
            username: activity.username || 'Anonymous'
          },
          expert: {
            name: activity.expert_name
          }
        })),
        activeSessions: activeSessions.map(session => ({
          shareToken: session.share_token,
          user: {
            id: session.user_id,
            username: session.username
          },
          expert: {
            name: session.expert_name
          },
          startedAt: session.access_started_at,
          lastActivity: session.last_activity_at,
          messageCount: session.message_count,
          consentGiven: session.consent_given
        })),
        hourlyStats,
        period: {
          hours,
          startTime: new Date(Date.now() - hours * 60 * 60 * 1000).toISOString(),
          endTime: new Date().toISOString()
        }
      };
      
    } finally {
      connection.release();
    }
  }

  /**
   * Clean up old analytics data
   * @param {number} retentionDays - Number of days to retain data (default: 90)
   * @returns {Promise<Object>} Cleanup results
   */
  async cleanupOldData(retentionDays = 90) {
    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
      
      // Delete old analytics records
      const [analyticsResult] = await connection.execute(
        'DELETE FROM share_analytics WHERE created_at < ?',
        [cutoffDate]
      );
      
      // Delete old access logs
      const [accessLogsResult] = await connection.execute(
        'DELETE FROM share_access_logs WHERE access_started_at < ?',
        [cutoffDate]
      );
      
      await connection.commit();
      
      return {
        deletedAnalyticsRecords: analyticsResult.affectedRows,
        deletedAccessLogs: accessLogsResult.affectedRows,
        cutoffDate: cutoffDate.toISOString(),
        retentionDays
      };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Generate analytics report
   * @param {number} sharedByUserId - User who created the shares
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @returns {Promise<Object>} Comprehensive analytics report
   */
  async generateReport(sharedByUserId, startDate, endDate) {
    const connection = await this.pool.getConnection();
    
    try {
      const dateFilter = `DATE(sa.created_at) BETWEEN ? AND ?`;
      
      // Get comprehensive stats
      const [reportData] = await connection.execute(
        `SELECT 
           sa.share_token,
           e.name as expert_name,
           es.created_at as share_created_at,
           COUNT(CASE WHEN sa.action_type = 'view' THEN 1 END) as total_views,
           COUNT(CASE WHEN sa.action_type = 'chat_start' THEN 1 END) as total_conversions,
           COUNT(CASE WHEN sa.action_type = 'consent' THEN 1 END) as total_consents,
           COUNT(DISTINCT sa.user_id) as unique_users,
           COUNT(DISTINCT sa.session_id) as unique_sessions,
           MIN(sa.created_at) as first_activity,
           MAX(sa.created_at) as last_activity
         FROM share_analytics sa
         JOIN expert_shares es ON sa.share_token = es.share_token
         JOIN experts e ON es.expert_id = e.id
         WHERE sa.shared_by_user_id = ? AND ${dateFilter}
         GROUP BY sa.share_token, e.name, es.created_at
         ORDER BY total_conversions DESC, total_views DESC`,
        [sharedByUserId, startDate, endDate]
      );
      
      return {
        reportPeriod: {
          startDate,
          endDate,
          generatedAt: new Date().toISOString()
        },
        shares: reportData.map(share => ({
          shareToken: share.share_token,
          expertName: share.expert_name,
          shareCreatedAt: share.share_created_at,
          metrics: {
            totalViews: share.total_views,
            totalConversions: share.total_conversions,
            totalConsents: share.total_consents,
            uniqueUsers: share.unique_users,
            uniqueSessions: share.unique_sessions,
            conversionRate: share.total_views > 0 ? ((share.total_conversions / share.total_views) * 100).toFixed(2) : 0,
            consentRate: share.total_views > 0 ? ((share.total_consents / share.total_views) * 100).toFixed(2) : 0
          },
          activity: {
            firstActivity: share.first_activity,
            lastActivity: share.last_activity
          }
        })),
        summary: {
          totalShares: reportData.length,
          totalViews: reportData.reduce((sum, share) => sum + share.total_views, 0),
          totalConversions: reportData.reduce((sum, share) => sum + share.total_conversions, 0),
          totalConsents: reportData.reduce((sum, share) => sum + share.total_consents, 0),
          totalUniqueUsers: new Set(reportData.flatMap(share => share.unique_users)).size
        }
      };
      
    } finally {
      connection.release();
    }
  }
}

module.exports = new AnalyticsService();