'use client';

import { useState, useCallback } from 'react';

export interface Toast {
  id: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  duration?: number;
}

interface ToastState {
  toasts: Toast[];
}

const toastState: ToastState = {
  toasts: []
};

let listeners: Array<(state: ToastState) => void> = [];

function emitChange() {
  listeners.forEach(listener => listener(toastState));
}

function addToast(toast: Omit<Toast, 'id'>) {
  const id = Math.random().toString(36).substr(2, 9);
  const newToast: Toast = {
    id,
    duration: 5000,
    ...toast
  };
  
  toastState.toasts.push(newToast);
  emitChange();
  
  // Auto remove toast after duration
  if (newToast.duration && newToast.duration > 0) {
    setTimeout(() => {
      removeToast(id);
    }, newToast.duration);
  }
  
  return id;
}

function removeToast(id: string) {
  const index = toastState.toasts.findIndex(toast => toast.id === id);
  if (index > -1) {
    toastState.toasts.splice(index, 1);
    emitChange();
  }
}

export function useToast() {
  const [state, setState] = useState<ToastState>(toastState);
  
  const subscribe = useCallback((listener: (state: ToastState) => void) => {
    listeners.push(listener);
    return () => {
      listeners = listeners.filter(l => l !== listener);
    };
  }, []);
  
  const toast = useCallback((props: Omit<Toast, 'id'>) => {
    return addToast(props);
  }, []);
  
  const dismiss = useCallback((id: string) => {
    removeToast(id);
  }, []);
  
  // Subscribe to state changes
  useState(() => {
    const unsubscribe = subscribe(setState);
    return unsubscribe;
  });
  
  return {
    toast,
    dismiss,
    toasts: state.toasts
  };
}

// Toast function for direct usage
export const toast = (props: Omit<Toast, 'id'>) => {
  return addToast(props);
};