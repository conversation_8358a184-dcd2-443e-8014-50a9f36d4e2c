const fs = require('fs');
const path = require('path');

// Log viewer utility for development
class LogViewer {
  constructor() {
    this.logsDir = path.join(__dirname, '../logs');
  }

  // Get list of available log files
  getLogFiles() {
    try {
      if (!fs.existsSync(this.logsDir)) {
        return [];
      }
      
      const files = fs.readdirSync(this.logsDir);
      return files.filter(file => file.endsWith('.log')).sort();
    } catch (error) {
      console.error('Error reading log directory:', error);
      return [];
    }
  }

  // Read specific log file
  readLogFile(filename, lines = 100) {
    try {
      const filePath = path.join(this.logsDir, filename);
      
      if (!fs.existsSync(filePath)) {
        return { error: 'Log file not found' };
      }
      
      const content = fs.readFileSync(filePath, 'utf8');
      const logLines = content.split('\n').filter(line => line.trim());
      
      // Return last N lines
      const lastLines = logLines.slice(-lines);
      
      return {
        filename,
        totalLines: logLines.length,
        lines: lastLines,
        lastModified: fs.statSync(filePath).mtime
      };
    } catch (error) {
      console.error('Error reading log file:', error);
      return { error: error.message };
    }
  }

  // Get logs by type and date
  getLogsByTypeAndDate(type, date) {
    try {
      // Sekarang semua log ada di api-logs
      const filename = `api-logs-${date}.log`;
      return this.readLogFile(filename);
    } catch (error) {
      return { error: error.message };
    }
  }

  // Get today's logs by type
  getTodaysLogs(type = 'api-logs') {
    const today = new Date().toISOString().split('T')[0];
    return this.getLogsByTypeAndDate(type, today);
  }

  // Get log statistics
  getLogStats() {
    try {
      const files = this.getLogFiles();
      const stats = {
        totalFiles: files.length,
        byType: {},
        byDate: {},
        totalSize: 0
      };

      files.forEach(file => {
        const filePath = path.join(this.logsDir, file);
        const fileStat = fs.statSync(filePath);
        stats.totalSize += fileStat.size;

        // Parse file name (type-date.log)
        const [type, dateWithExt] = file.split('-');
        const date = dateWithExt.replace('.log', '');

        if (!stats.byType[type]) {
          stats.byType[type] = { count: 0, size: 0 };
        }
        stats.byType[type].count++;
        stats.byType[type].size += fileStat.size;

        if (!stats.byDate[date]) {
          stats.byDate[date] = { count: 0, size: 0 };
        }
        stats.byDate[date].count++;
        stats.byDate[date].size += fileStat.size;
      });

      return stats;
    } catch (error) {
      return { error: error.message };
    }
  }

  // Clean old logs (older than specified days)
  cleanOldLogs(daysToKeep = 30) {
    try {
      const files = this.getLogFiles();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      let deletedCount = 0;
      let deletedSize = 0;

      files.forEach(file => {
        const filePath = path.join(this.logsDir, file);
        const fileStat = fs.statSync(filePath);

        if (fileStat.mtime < cutoffDate) {
          deletedSize += fileStat.size;
          fs.unlinkSync(filePath);
          deletedCount++;
          console.log(`Deleted old log file: ${file}`);
        }
      });

      return {
        deletedCount,
        deletedSize,
        message: `Deleted ${deletedCount} log files (${this.formatBytes(deletedSize)})`
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  // Format bytes to human readable
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Search logs for specific terms
  searchLogs(searchTerm, logType = null, date = null) {
    try {
      let filesToSearch = this.getLogFiles();

      // Filter by type and date if specified
      if (logType || date) {
        filesToSearch = filesToSearch.filter(file => {
          const [type, dateWithExt] = file.split('-');
          const fileDate = dateWithExt.replace('.log', '');
          
          if (logType && type !== logType) return false;
          if (date && fileDate !== date) return false;
          
          return true;
        });
      }

      const results = [];

      filesToSearch.forEach(file => {
        const filePath = path.join(this.logsDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');

        lines.forEach((line, index) => {
          if (line.toLowerCase().includes(searchTerm.toLowerCase())) {
            results.push({
              file,
              lineNumber: index + 1,
              content: line.trim()
            });
          }
        });
      });

      return {
        searchTerm,
        totalMatches: results.length,
        results: results.slice(0, 100) // Limit to first 100 matches
      };
    } catch (error) {
      return { error: error.message };
    }
  }
}

module.exports = LogViewer;
