// Migration: Add reviews and rating system
// Created: 2025-08-14
// Description: Add reviews table and rating columns to experts table

const mysql = require('mysql2/promise');
require('dotenv').config();

async function getConnection() {
  return mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'aitrainerhub'
  });
}

async function up() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    // 1. Add rating columns to experts table (check if they exist first)
    console.log('Adding rating columns to experts table...');
    
    // Check if columns exist and add them one by one
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'experts'
    `);
    
    const existingColumns = columns.map(col => col.COLUMN_NAME);
    
    if (!existingColumns.includes('total_chats')) {
      await connection.execute('ALTER TABLE experts ADD COLUMN total_chats INT DEFAULT 0 AFTER labels');
      console.log('Added total_chats column');
    }
    
    if (!existingColumns.includes('total_revenue')) {
      await connection.execute('ALTER TABLE experts ADD COLUMN total_revenue DECIMAL(10,2) DEFAULT 0 AFTER total_chats');
      console.log('Added total_revenue column');
    }
    
    if (!existingColumns.includes('average_rating')) {
      await connection.execute('ALTER TABLE experts ADD COLUMN average_rating DECIMAL(3,2) DEFAULT 0 AFTER total_revenue');
      console.log('Added average_rating column');
    }
    
    if (!existingColumns.includes('total_reviews')) {
      await connection.execute('ALTER TABLE experts ADD COLUMN total_reviews INT DEFAULT 0 AFTER average_rating');
      console.log('Added total_reviews column');
    }
    
    // 2. Create reviews table (check if it exists first)
    console.log('Creating reviews table...');
    
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'reviews'
    `);
    
    if (tables.length === 0) {
      await connection.execute(`
        CREATE TABLE reviews (
          id INT PRIMARY KEY AUTO_INCREMENT,
          user_id INT NOT NULL,
          expert_id INT NOT NULL,
          rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
          review_text TEXT,
          is_verified BOOLEAN DEFAULT FALSE,
          is_hidden BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
          FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
          UNIQUE KEY unique_user_expert_review (user_id, expert_id),
          INDEX idx_expert_reviews (expert_id, created_at DESC),
          INDEX idx_rating (rating),
          INDEX idx_user_reviews (user_id, created_at DESC)
        )
      `);
      console.log('Created reviews table');
    } else {
      console.log('Reviews table already exists');
    }
    
    // 3. Create trigger to update expert ratings automatically
    console.log('Creating rating update trigger...');
    
    // Check if triggers exist first
    const [triggers] = await connection.execute(`
      SELECT TRIGGER_NAME 
      FROM INFORMATION_SCHEMA.TRIGGERS 
      WHERE TRIGGER_SCHEMA = DATABASE() 
      AND TRIGGER_NAME IN ('update_expert_rating_after_review_insert', 'update_expert_rating_after_review_update', 'update_expert_rating_after_review_delete')
    `);
    
    const existingTriggers = triggers.map(t => t.TRIGGER_NAME);
    
    if (!existingTriggers.includes('update_expert_rating_after_review_insert')) {
      await connection.query(`
        CREATE TRIGGER update_expert_rating_after_review_insert
        AFTER INSERT ON reviews
        FOR EACH ROW
        BEGIN
          UPDATE experts 
          SET 
            average_rating = (
              SELECT AVG(rating) 
              FROM reviews 
              WHERE expert_id = NEW.expert_id AND is_hidden = FALSE
            ),
            total_reviews = (
              SELECT COUNT(*) 
              FROM reviews 
              WHERE expert_id = NEW.expert_id AND is_hidden = FALSE
            )
          WHERE id = NEW.expert_id;
        END
      `);
      console.log('Created insert trigger');
    }
    
    if (!existingTriggers.includes('update_expert_rating_after_review_update')) {
      await connection.query(`
        CREATE TRIGGER update_expert_rating_after_review_update
        AFTER UPDATE ON reviews
        FOR EACH ROW
        BEGIN
          UPDATE experts 
          SET 
            average_rating = (
              SELECT AVG(rating) 
              FROM reviews 
              WHERE expert_id = NEW.expert_id AND is_hidden = FALSE
            ),
            total_reviews = (
              SELECT COUNT(*) 
              FROM reviews 
              WHERE expert_id = NEW.expert_id AND is_hidden = FALSE
            )
          WHERE id = NEW.expert_id;
        END
      `);
      console.log('Created update trigger');
    }
    
    if (!existingTriggers.includes('update_expert_rating_after_review_delete')) {
      await connection.query(`
        CREATE TRIGGER update_expert_rating_after_review_delete
        AFTER DELETE ON reviews
        FOR EACH ROW
        BEGIN
          UPDATE experts 
          SET 
            average_rating = COALESCE((
              SELECT AVG(rating) 
              FROM reviews 
              WHERE expert_id = OLD.expert_id AND is_hidden = FALSE
            ), 0),
            total_reviews = (
              SELECT COUNT(*) 
              FROM reviews 
              WHERE expert_id = OLD.expert_id AND is_hidden = FALSE
            )
          WHERE id = OLD.expert_id;
        END
      `);
      console.log('Created delete trigger');
    }
    
    await connection.commit();
    console.log('✅ Reviews system migration completed successfully');
    
  } catch (error) {
    await connection.rollback();
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

async function down() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    // Drop triggers
    await connection.query('DROP TRIGGER IF EXISTS update_expert_rating_after_review_insert');
    await connection.query('DROP TRIGGER IF EXISTS update_expert_rating_after_review_update');
    await connection.query('DROP TRIGGER IF EXISTS update_expert_rating_after_review_delete');
    
    // Drop reviews table
    await connection.execute('DROP TABLE IF EXISTS reviews');
    
    // Remove rating columns from experts table
    await connection.execute(`
      ALTER TABLE experts 
      DROP COLUMN total_chats,
      DROP COLUMN total_revenue,
      DROP COLUMN average_rating,
      DROP COLUMN total_reviews
    `);
    
    await connection.commit();
    console.log('✅ Reviews system rollback completed');
    
  } catch (error) {
    await connection.rollback();
    console.error('❌ Rollback failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run migration
if (require.main === module) {
  up().then(() => {
    console.log('Migration completed');
    process.exit(0);
  }).catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
}

module.exports = { up, down };