const express = require('express');
const router = express.Router();
const userController = require('../../controllers/userController');

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management and authentication
 */

// Register
/**
 * @swagger
 * /api/users/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - phone
 *               - password
 *             properties:
 *               name:
 *                 type: string
 *                 description: User's full name
 *                 example: "<PERSON>"
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *                 example: "<EMAIL>"
 *               phone:
 *                 type: string
 *                 description: User's phone number
 *                 example: "+1234567890"
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 description: User's password (minimum 6 characters)
 *                 example: "password123"
 *               referralCode:
 *                 type: string
 *                 description: Optional referral code from another user
 *                 example: "ABC123"
 *               affiliateVisitorId:
 *                 type: string
 *                 description: Optional affiliate visitor ID managed by frontend
 *                 example: "visitor-uuid-123"
 *     responses:
 *       201:
 *         description: Registration successful, OTP sent
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 user:
 *                   type: object
 *                 verification:
 *                   type: object
 *                   properties:
 *                     whatsappLink:
 *                       type: string
 *                     message:
 *                       type: string
 *       400:
 *         description: Invalid input or registration failed
 */
router.post('/register', userController.register);

// Verify OTP
/**
 * @swagger
 * /api/users/verify-otp:
 *   post:
 *     summary: Verify OTP for user registration
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               otp:
 *                 type: string
 *     responses:
 *       200:
 *         description: OTP verified successfully
 *       400:
 *         description: Invalid OTP or email
 */
router.post('/verify-otp', userController.verifyOTP);

// Login
/**
 * @swagger
 * /api/users/login:
 *   post:
 *     summary: Login user
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', userController.login);

// Resend OTP
/**
 * @swagger
 * /api/users/resend-otp:
 *   post:
 *     summary: Resend OTP to user email
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *     responses:
 *       200:
 *         description: OTP resent successfully
 *       400:
 *         description: Invalid email
 */
router.post('/resend-otp', userController.resendOTP);

// Forgot Password
/**
 * @swagger
 * /api/users/forgot-password:
 *   post:
 *     summary: Request password reset
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password reset email sent
 *       400:
 *         description: Invalid email
 */
router.post('/forgot-password', userController.forgotPassword);

// Reset Password
/**
 * @swagger
 * /api/users/reset-password:
 *   post:
 *     summary: Reset user password
 *     tags: [Users]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               otp:
 *                 type: string
 *               newPassword:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password reset successful
 *       400:
 *         description: Invalid OTP or email
 */
router.post('/reset-password', userController.resetPassword);

module.exports = router;
