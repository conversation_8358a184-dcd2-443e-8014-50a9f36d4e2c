'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Share2, Copy, Check, BarChart3 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { api } from '@/lib/api';

interface Expert {
  id: number;
  name: string;
  description: string;
  imageUrl?: string;
  voiceEnabled?: boolean;
  labels: string[];
}

interface ShareCreatorProps {
  expert?: Expert;
  onShareCreated?: (shareData: any) => void;
  onCancel?: () => void;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  showTrigger?: boolean;
}

interface ShareFormData {
  monitorEnabled: boolean;
}

export default function ShareCreator({ 
  expert, 
  onShareCreated, 
  onCancel,
  isOpen: controlledIsOpen, 
  onOpenChange: controlledOnOpenChange,
  showTrigger = true 
}: ShareCreatorProps) {
  const { toast } = useToast();
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  
  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;
  const setIsOpen = controlledOnOpenChange || setInternalIsOpen;
  const [loading, setLoading] = useState(false);
  const [shareData, setShareData] = useState<any>(null);
  const [copied, setCopied] = useState(false);
  const [formData, setFormData] = useState<ShareFormData>({
    monitorEnabled: true
  });

  const handleCreateShare = async () => {
    setLoading(true);
    try {
      if (!expert?.id) {
        throw new Error('Expert ID is required');
      }
      
      const response = await api.post(`/sharing/experts/${expert.id}/share`, {
        body: {
          monitorEnabled: formData.monitorEnabled
        },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setShareData(response.data.data);
        onShareCreated?.(response.data.data);
        toast({
          title: "Share Created",
          description: "Your expert share link has been created successfully."
        });
      }
    } catch (err: any) {
      console.error('Error creating share:', err);
      toast({
        title: "Error",
        description: err.response?.data?.message || "Failed to create share link. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      toast({
        title: "Copied!",
        description: "Share link copied to clipboard."
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
      toast({
        title: "Error",
        description: "Failed to copy link. Please copy manually.",
        variant: "destructive"
      });
    }
  };

  const getShareUrl = () => {
    if (!shareData) return '';
    return `${window.location.origin}/shared/${shareData.shareToken}`;
  };

  const resetForm = () => {
    setShareData(null);
    setFormData({
      monitorEnabled: true
    });
    setCopied(false);
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      resetForm();
      onCancel?.();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      {showTrigger && (
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="flex items-center space-x-2">
            <Share2 className="h-4 w-4" />
            <span>Share Expert</span>
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Share2 className="h-5 w-5" />
            <span>Share {expert?.name || 'Expert'}</span>
          </DialogTitle>
          <DialogDescription>
            Create a shareable link for your AI expert. You can track clicks and conversions.
          </DialogDescription>
        </DialogHeader>

        {!shareData ? (
          <div className="space-y-6">
            {/* Expert Preview */}
            {expert && (
              <Card className="bg-gray-50">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600">🤖</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{expert.name}</h4>
                      <p className="text-sm text-gray-600 truncate">{expert.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Share Settings */}
            <div className="space-y-4">
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="monitoring"
                  checked={formData.monitorEnabled}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({ ...prev, monitorEnabled: checked as boolean }))
                  }
                />
                <div className="space-y-1">
                  <Label htmlFor="monitoring" className="text-sm font-medium cursor-pointer">
                    Enable Analytics Monitoring
                  </Label>
                  <p className="text-xs text-gray-500">
                    Track clicks, conversions, and user interactions. Users will be asked for consent.
                  </p>
                </div>
              </div>


            </div>

            <Button
              onClick={handleCreateShare}
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Creating...' : 'Create Share Link'}
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Success Message */}
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Check className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Share Link Created!</h3>
              <p className="text-sm text-gray-600">
                Your expert is now ready to be shared. Copy the link below.
              </p>
            </div>

            {/* Share Link */}
            <div className="space-y-2">
              <Label>Share Link</Label>
              <div className="flex space-x-2">
                <Input
                  value={getShareUrl()}
                  readOnly
                  className="flex-1"
                />
                <Button
                  onClick={() => copyToClipboard(getShareUrl())}
                  variant="outline"
                  size="sm"
                  className="px-3"
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* Share Info */}
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Type:</span>
                    <p className="font-medium capitalize">{shareData.shareType}</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Monitoring:</span>
                    <p className="font-medium">{shareData.monitorEnabled ? 'Enabled' : 'Disabled'}</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Token:</span>
                    <p className="font-mono text-xs">{shareData.shareToken}</p>
                  </div>
                  <div>
                    <span className="text-gray-600">Created:</span>
                    <p className="font-medium">{new Date(shareData.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="flex space-x-2">
              <Button
                onClick={() => copyToClipboard(getShareUrl())}
                className="flex-1"
                variant={copied ? "outline" : "default"}
              >
                {copied ? (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Link
                  </>
                )}
              </Button>
              <Button
                onClick={() => window.open(`/dashboard/shares/${shareData.shareToken}/analytics`, '_blank')}
                variant="outline"
                className="flex-1"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                Analytics
              </Button>
            </div>

            <Button
              onClick={resetForm}
              variant="outline"
              className="w-full"
            >
              Create Another Share
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}