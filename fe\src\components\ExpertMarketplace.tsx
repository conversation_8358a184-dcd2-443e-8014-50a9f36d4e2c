"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { api } from "@/lib/api";
import { asset } from "@/lib/utils";
import StarRating from "@/components/ui/star-rating";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  pricingPercentage: number;
  isPublic: boolean;
  voiceEnabled?: boolean;
  labels: string[];
  totalChats?: number;
  totalRevenue?: number;
  averageRating?: number;
  totalReviews?: number;
  createdAt: string;
  updatedAt: string;
}

const ExpertMarketplace: React.FC = () => {
  const [experts, setExperts] = useState<Expert[]>([]);
  const [filteredExperts, setFilteredExperts] = useState<Expert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("name");
  const [filterByRating, setFilterByRating] = useState("all");

  const loadExperts = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await api.getPublicExperts();

      if (result.success) {
        setExperts(result.experts);
        setFilteredExperts(result.experts);
      } else {
        setError(result.error || "Failed to load experts");
      }
    } catch (err: any) {
      setError(err.message || "Failed to load experts");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadExperts();
  }, []);

  // Filter and sort experts
  useEffect(() => {
    let filtered = [...experts];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(expert =>
        expert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        expert.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        expert.labels.some(label => label.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply rating filter
    if (filterByRating !== "all") {
      const minRating = parseFloat(filterByRating);
      filtered = filtered.filter(expert =>
        expert.averageRating && expert.averageRating >= minRating
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "rating":
          return (b.averageRating || 0) - (a.averageRating || 0);
        case "reviews":
          return (b.totalReviews || 0) - (a.totalReviews || 0);
        case "chats":
          return (b.totalChats || 0) - (a.totalChats || 0);
        case "newest":
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case "name":
        default:
          return a.name.localeCompare(b.name);
      }
    });

    setFilteredExperts(filtered);
  }, [experts, searchTerm, sortBy, filterByRating]);

  const getExpertIcon = (labels: string[]) => {
    if (labels.includes("business") || labels.includes("marketing"))
      return "💼";
    if (labels.includes("code") || labels.includes("programming")) return "💻";
    if (labels.includes("creative") || labels.includes("design")) return "🎨";
    if (labels.includes("education") || labels.includes("learning"))
      return "📚";
    if (labels.includes("health") || labels.includes("medical")) return "🏥";
    if (labels.includes("finance") || labels.includes("money")) return "💰";
    return "🤖";
  };

  const truncateDescription = (
    description: string,
    maxLength: number = 120
  ) => {
    if (description.length <= maxLength) return description;
    return description.substring(0, maxLength) + "...";
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, index) => (
          <div
            key={index}
            className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 animate-pulse"
          >
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-16 h-16 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
            <div className="space-y-2 mb-4">
              <div className="h-3 bg-gray-200 rounded"></div>
              <div className="h-3 bg-gray-200 rounded"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
            <div className="h-10 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 max-w-md mx-auto">
          <div className="text-red-600 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Unable to Load Experts
          </h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={loadExperts}
            className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (experts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🔍</div>
        <h3 className="text-xl font-semibold text-gray-700 mb-2">
          No Public Experts Available
        </h3>
        <p className="text-gray-500">Check back soon for new AI experts!</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filter Controls */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search experts by name, description, or tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Sort By */}
          <div className="w-full lg:w-48">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name (A-Z)</SelectItem>
                <SelectItem value="rating">Highest Rated</SelectItem>
                <SelectItem value="reviews">Most Reviews</SelectItem>
                <SelectItem value="chats">Most Popular</SelectItem>
                <SelectItem value="newest">Newest First</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filter by Rating */}
          <div className="w-full lg:w-48">
            <Select value={filterByRating} onValueChange={setFilterByRating}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by rating" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ratings</SelectItem>
                <SelectItem value="4">4+ Stars</SelectItem>
                <SelectItem value="3">3+ Stars</SelectItem>
                <SelectItem value="2">2+ Stars</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Results Summary */}
        <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
          <span>
            Showing {filteredExperts.length} of {experts.length} experts
          </span>
          {(searchTerm || filterByRating !== "all") && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchTerm("");
                setFilterByRating("all");
              }}
            >
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      {/* No Results */}
      {filteredExperts.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold text-gray-700 mb-2">
            No Experts Found
          </h3>
          <p className="text-gray-500">Try adjusting your search or filters</p>
        </div>
      )}

      {/* Expert Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredExperts.map((expert) => (
          <div
            key={expert.id}
            className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl hover:scale-[1.02] transition-all duration-300 group"
          >
            {/* Expert Header */}
            <div className="flex items-center space-x-4 mb-4">
              <div className="relative">
                {expert.imageUrl ? (
                  <Image
                    src={asset(expert.imageUrl)}
                    alt={expert.name}
                    width={64}
                    height={64}
                    className="w-16 h-16 object-cover rounded-full border-2 border-gray-100"
                  />
                ) : (
                  <div
                    className="w-16 h-16 rounded-full flex items-center justify-center text-2xl text-white shadow-lg"
                    style={{ backgroundColor: "#1E3A8A" }}
                  >
                    {getExpertIcon(expert.labels)}
                  </div>
                )}
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              </div>

              <div className="flex-1">
                <h3 className="font-bold text-lg text-gray-900 group-hover:text-blue-900 transition-colors">
                  {expert.name}
                </h3>
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full font-medium">
                    {expert.model}
                  </span>
                  <span className="text-xs text-green-600 font-medium">
                    ● Online
                  </span>
                </div>
                {/* Rating Display */}
                {expert.averageRating && expert.averageRating > 0 ? (
                  <div className="flex items-center gap-1">
                    <StarRating rating={expert.averageRating} size="sm" />
                    <span className="text-xs text-gray-500">
                      ({expert.totalReviews} review{expert.totalReviews !== 1 ? 's' : ''})
                    </span>
                  </div>
                ) : (
                  <div className="text-xs text-gray-400">No reviews yet</div>
                )}
              </div>
            </div>

            {/* Description */}
            <p className="text-gray-600 text-sm mb-4 leading-relaxed">
              {truncateDescription(expert.description)}
            </p>

            {/* Labels */}
            {expert.labels && expert.labels.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {expert.labels.slice(0, 3).map((label, index) => (
                  <span
                    key={index}
                    className="inline-block px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full font-medium hover:bg-gray-200 transition-colors"
                  >
                    #{label}
                  </span>
                ))}
                {expert.labels.length > 3 && (
                  <span className="inline-block px-3 py-1 text-xs text-gray-500 rounded-full">
                    +{expert.labels.length - 3} more
                  </span>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              <Link
                href={`/expert/${expert.id}`}
                className="block w-full py-3 px-4 text-center font-semibold rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                style={{ backgroundColor: "#1E3A8A" }}
              >
                View Profile
              </Link>

              <Link
                href={`/chat?expertId=${expert.id}`}
                className="block w-full py-3 px-4 text-center font-semibold rounded-xl border-2 transition-all duration-200 hover:shadow-md"
                style={{ borderColor: "#1E3A8A", color: "#1E3A8A" }}
              >
                ⚡ Start Chat
              </Link>
            </div>

            {/* Stats and Pricing Info */}
            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                <span>💰 {expert.pricingPercentage}% of usage</span>
                <span>🕒 Instant response</span>
              </div>
              {expert.totalChats && expert.totalChats > 0 && (
                <div className="text-xs text-gray-400">
                  💬 {expert.totalChats} conversation{expert.totalChats !== 1 ? 's' : ''}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExpertMarketplace;
