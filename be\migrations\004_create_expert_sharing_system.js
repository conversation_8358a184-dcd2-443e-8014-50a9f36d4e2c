// Migration: Create Expert Sharing System Tables
// Created: 2025-08-14
// Description: Implement expert_shares table and add sharing columns to chat_sessions table

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function getConnection() {
  return mysql.createConnection({
    host: process.env.DB_HOST || '127.0.0.1',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'aitrainerhub'
  });
}

async function up() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('Creating expert sharing system...');
    
    // Create expert_shares table
    const createExpertSharesTableSQL = `
      CREATE TABLE IF NOT EXISTS expert_shares (
        id INT PRIMARY KEY AUTO_INCREMENT,
        expert_id INT NOT NULL,
        shared_by_user_id INT NOT NULL,
        share_token VARCHAR(255) UNIQUE NOT NULL,
        monitor_enabled BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
        FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        INDEX idx_share_token (share_token),
        INDEX idx_shared_by (shared_by_user_id),
        INDEX idx_expert_shares (expert_id, is_active),
        INDEX idx_active_shares (is_active, created_at DESC)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.execute(createExpertSharesTableSQL);
    console.log('✅ Created expert_shares table');
    
    // Add sharing columns to chat_sessions table
    console.log('Adding sharing columns to chat_sessions table...');
    
    // Check if columns already exist before adding them
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'chat_sessions' 
      AND COLUMN_NAME IN ('is_shared', 'shared_by_user_id', 'share_token', 'monitor_enabled')
    `, [process.env.DB_NAME || 'aitrainerhub']);
    
    const existingColumns = columns.map(col => col.COLUMN_NAME);
    
    if (!existingColumns.includes('is_shared')) {
      await connection.execute('ALTER TABLE chat_sessions ADD COLUMN is_shared BOOLEAN DEFAULT FALSE');
      console.log('✅ Added is_shared column');
    }
    
    if (!existingColumns.includes('shared_by_user_id')) {
      await connection.execute('ALTER TABLE chat_sessions ADD COLUMN shared_by_user_id INT');
      console.log('✅ Added shared_by_user_id column');
    }
    
    if (!existingColumns.includes('share_token')) {
      await connection.execute('ALTER TABLE chat_sessions ADD COLUMN share_token VARCHAR(255) UNIQUE');
      console.log('✅ Added share_token column');
    }
    
    if (!existingColumns.includes('monitor_enabled')) {
      await connection.execute('ALTER TABLE chat_sessions ADD COLUMN monitor_enabled BOOLEAN DEFAULT FALSE');
      console.log('✅ Added monitor_enabled column');
    }
    
    // Add foreign key constraint for shared_by_user_id if it doesn't exist
    const [constraints] = await connection.execute(`
      SELECT CONSTRAINT_NAME 
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'chat_sessions' 
      AND COLUMN_NAME = 'shared_by_user_id' 
      AND REFERENCED_TABLE_NAME IS NOT NULL
    `, [process.env.DB_NAME || 'aitrainerhub']);
    
    if (constraints.length === 0) {
      await connection.execute(`
        ALTER TABLE chat_sessions 
        ADD FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE SET NULL
      `);
      console.log('✅ Added foreign key constraint for shared_by_user_id');
    }
    
    // Create indexes for sharing functionality
    const indexQueries = [
      'CREATE INDEX IF NOT EXISTS idx_chat_share_token ON chat_sessions(share_token)',
      'CREATE INDEX IF NOT EXISTS idx_chat_shared_sessions ON chat_sessions(is_shared, shared_by_user_id)',
      'CREATE INDEX IF NOT EXISTS idx_chat_monitor_enabled ON chat_sessions(monitor_enabled)'
    ];
    
    for (const indexQuery of indexQueries) {
      try {
        await connection.execute(indexQuery);
      } catch (error) {
        // Index might already exist, continue
        console.log(`Index creation skipped: ${error.message}`);
      }
    }
    console.log('✅ Created sharing indexes');
    
    // Create stored procedure for generating share tokens
    const createShareTokenProcedureSQL = `
      CREATE PROCEDURE IF NOT EXISTS GenerateShareToken(
        IN p_expert_id INT,
        IN p_shared_by_user_id INT,
        IN p_monitor_enabled BOOLEAN,
        OUT p_share_token VARCHAR(255)
      )
      BEGIN
        DECLARE token_exists INT DEFAULT 1;
        DECLARE new_token VARCHAR(255);
        
        WHILE token_exists > 0 DO
          SET new_token = CONCAT(
            'share_',
            SUBSTRING(MD5(CONCAT(p_expert_id, p_shared_by_user_id, UNIX_TIMESTAMP(), RAND())), 1, 16)
          );
          
          SELECT COUNT(*) INTO token_exists 
          FROM expert_shares 
          WHERE share_token = new_token;
        END WHILE;
        
        INSERT INTO expert_shares (expert_id, shared_by_user_id, share_token, monitor_enabled)
        VALUES (p_expert_id, p_shared_by_user_id, new_token, p_monitor_enabled);
        
        SET p_share_token = new_token;
      END
    `;
    
    await connection.query(createShareTokenProcedureSQL);
    console.log('✅ Created share token generation procedure');
    
    await connection.commit();
    console.log('✅ Migration completed successfully');
    
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    await connection.end();
  }
}

async function down() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('Removing expert sharing system...');
    
    // Drop stored procedure
    await connection.query('DROP PROCEDURE IF EXISTS GenerateShareToken');
    console.log('✅ Dropped share token generation procedure');
    
    // Drop indexes
    const dropIndexQueries = [
      'DROP INDEX IF EXISTS idx_chat_share_token ON chat_sessions',
      'DROP INDEX IF EXISTS idx_chat_shared_sessions ON chat_sessions',
      'DROP INDEX IF EXISTS idx_chat_monitor_enabled ON chat_sessions'
    ];
    
    for (const dropQuery of dropIndexQueries) {
      try {
        await connection.execute(dropQuery);
      } catch (error) {
        console.log(`Index drop failed (might not exist): ${error.message}`);
      }
    }
    
    // Remove foreign key constraint
    try {
      await connection.execute(`
        ALTER TABLE chat_sessions 
        DROP FOREIGN KEY chat_sessions_ibfk_3
      `);
    } catch (error) {
      console.log(`Foreign key drop failed: ${error.message}`);
    }
    
    // Remove columns from chat_sessions
    const columnsToRemove = ['is_shared', 'shared_by_user_id', 'share_token', 'monitor_enabled'];
    
    for (const column of columnsToRemove) {
      try {
        await connection.execute(`ALTER TABLE chat_sessions DROP COLUMN ${column}`);
        console.log(`✅ Removed ${column} column`);
      } catch (error) {
        console.log(`Column ${column} drop failed: ${error.message}`);
      }
    }
    
    // Drop expert_shares table
    await connection.execute('DROP TABLE IF EXISTS expert_shares');
    console.log('✅ Dropped expert_shares table');
    
    await connection.commit();
    console.log('✅ Rollback completed successfully');
    
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    await connection.end();
  }
}

// Run migration
if (require.main === module) {
  up().then(() => {
    console.log('Migration completed');
    process.exit(0);
  }).catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
}

module.exports = { up, down };