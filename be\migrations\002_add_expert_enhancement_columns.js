// Migration: Add Expert Enhancement Columns
// Created: 2025-08-14
// Description: Add first_message, voice_enabled, total_chats, total_revenue, average_rating, total_reviews to experts table

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function getConnection() {
  return mysql.createConnection({
    host: process.env.DB_HOST || '127.0.0.1',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'aitrainerhub'
  });
}

async function up() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('Adding expert enhancement columns...');
    
    // Add new columns to experts table
    const alterTableSQL = `
      ALTER TABLE experts 
      ADD COLUMN first_message TEXT AFTER image_url,
      ADD COLUMN voice_enabled BOOLEAN DEFAULT FALSE AFTER first_message,
      ADD COLUMN total_chats INT DEFAULT 0 AFTER labels,
      ADD COLUMN total_revenue DECIMAL(10,2) DEFAULT 0 AFTER total_chats,
      ADD COLUMN average_rating DECIMAL(3,2) DEFAULT 0 AFTER total_revenue,
      ADD COLUMN total_reviews INT DEFAULT 0 AFTER average_rating
    `;
    
    await connection.execute(alterTableSQL);
    console.log('✅ Added expert enhancement columns');
    
    // Create indexes for performance optimization
    const indexSQL = [
      'CREATE INDEX idx_experts_voice_enabled ON experts(voice_enabled)',
      'CREATE INDEX idx_experts_total_chats ON experts(total_chats DESC)',
      'CREATE INDEX idx_experts_total_revenue ON experts(total_revenue DESC)',
      'CREATE INDEX idx_experts_average_rating ON experts(average_rating DESC)',
      'CREATE INDEX idx_experts_total_reviews ON experts(total_reviews DESC)'
    ];
    
    for (const sql of indexSQL) {
      await connection.execute(sql);
    }
    console.log('✅ Created performance indexes');
    
    await connection.commit();
    console.log('✅ Migration completed successfully');
    
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    await connection.end();
  }
}

async function down() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('Removing expert enhancement columns...');
    
    // Drop indexes first
    const dropIndexSQL = [
      'DROP INDEX idx_experts_voice_enabled ON experts',
      'DROP INDEX idx_experts_total_chats ON experts',
      'DROP INDEX idx_experts_total_revenue ON experts',
      'DROP INDEX idx_experts_average_rating ON experts',
      'DROP INDEX idx_experts_total_reviews ON experts'
    ];
    
    for (const sql of dropIndexSQL) {
      try {
        await connection.execute(sql);
      } catch (error) {
        // Index might not exist, continue
        console.log(`Index drop failed (might not exist): ${error.message}`);
      }
    }
    
    // Remove columns
    const alterTableSQL = `
      ALTER TABLE experts 
      DROP COLUMN first_message,
      DROP COLUMN voice_enabled,
      DROP COLUMN total_chats,
      DROP COLUMN total_revenue,
      DROP COLUMN average_rating,
      DROP COLUMN total_reviews
    `;
    
    await connection.execute(alterTableSQL);
    console.log('✅ Removed expert enhancement columns');
    
    await connection.commit();
    console.log('✅ Rollback completed successfully');
    
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    await connection.end();
  }
}

// Run migration
if (require.main === module) {
  up().then(() => {
    console.log('Migration completed');
    process.exit(0);
  }).catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
}

module.exports = { up, down };