// Test Database Schema Enhancements
// Created: 2025-08-14
// Description: Test all database schema enhancements to ensure they work correctly

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function getConnection() {
  return mysql.createConnection({
    host: process.env.DB_HOST || '127.0.0.1',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'aitrainerhub'
  });
}

async function testSchemaEnhancements() {
  const connection = await getConnection();
  
  try {
    console.log('🧪 Testing database schema enhancements...\n');
    
    // Test 1: Check if expert enhancement columns exist
    console.log('📋 Test 1: Expert enhancement columns');
    const [expertColumns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'experts' 
      AND COLUMN_NAME IN ('first_message', 'voice_enabled', 'total_chats', 'total_revenue', 'average_rating', 'total_reviews')
      ORDER BY COLUMN_NAME
    `, [process.env.DB_NAME || 'aitrainerhub']);
    
    const expectedExpertColumns = ['average_rating', 'first_message', 'total_chats', 'total_revenue', 'total_reviews', 'voice_enabled'];
    const actualExpertColumns = expertColumns.map(col => col.COLUMN_NAME).sort();
    
    if (JSON.stringify(expectedExpertColumns) === JSON.stringify(actualExpertColumns)) {
      console.log('✅ All expert enhancement columns exist');
      expertColumns.forEach(col => {
        console.log(`   - ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'nullable' : 'not null'})`);
      });
    } else {
      console.log('❌ Missing expert enhancement columns');
      console.log('Expected:', expectedExpertColumns);
      console.log('Actual:', actualExpertColumns);
    }
    
    // Test 2: Check if reviews table exists with proper structure
    console.log('\n📋 Test 2: Reviews table structure');
    const [reviewsTable] = await connection.execute(`
      SELECT COUNT(*) as table_exists 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'reviews'
    `, [process.env.DB_NAME || 'aitrainerhub']);
    
    if (reviewsTable[0].table_exists > 0) {
      console.log('✅ Reviews table exists');
      
      // Check reviews table columns
      const [reviewsColumns] = await connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'reviews'
        ORDER BY ORDINAL_POSITION
      `, [process.env.DB_NAME || 'aitrainerhub']);
      
      console.log('   Columns:');
      reviewsColumns.forEach(col => {
        console.log(`   - ${col.COLUMN_NAME}: ${col.DATA_TYPE}`);
      });
      
      // Check triggers
      const [triggers] = await connection.execute(`
        SELECT TRIGGER_NAME 
        FROM INFORMATION_SCHEMA.TRIGGERS 
        WHERE TRIGGER_SCHEMA = ? AND EVENT_OBJECT_TABLE = 'reviews'
      `, [process.env.DB_NAME || 'aitrainerhub']);
      
      console.log('   Triggers:');
      triggers.forEach(trigger => {
        console.log(`   - ${trigger.TRIGGER_NAME}`);
      });
    } else {
      console.log('❌ Reviews table does not exist');
    }
    
    // Test 3: Check expert sharing system
    console.log('\n📋 Test 3: Expert sharing system');
    const [expertSharesTable] = await connection.execute(`
      SELECT COUNT(*) as table_exists 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'expert_shares'
    `, [process.env.DB_NAME || 'aitrainerhub']);
    
    if (expertSharesTable[0].table_exists > 0) {
      console.log('✅ Expert shares table exists');
      
      // Check chat_sessions sharing columns
      const [chatSessionsColumns] = await connection.execute(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'chat_sessions' 
        AND COLUMN_NAME IN ('is_shared', 'shared_by_user_id', 'share_token', 'monitor_enabled')
      `, [process.env.DB_NAME || 'aitrainerhub']);
      
      const sharingColumns = chatSessionsColumns.map(col => col.COLUMN_NAME);
      console.log('   Chat sessions sharing columns:', sharingColumns);
      
      // Check stored procedure
      const [procedures] = await connection.execute(`
        SELECT ROUTINE_NAME 
        FROM INFORMATION_SCHEMA.ROUTINES 
        WHERE ROUTINE_SCHEMA = ? AND ROUTINE_NAME = 'GenerateShareToken'
      `, [process.env.DB_NAME || 'aitrainerhub']);
      
      if (procedures.length > 0) {
        console.log('✅ GenerateShareToken procedure exists');
      } else {
        console.log('❌ GenerateShareToken procedure missing');
      }
    } else {
      console.log('❌ Expert shares table does not exist');
    }
    
    // Test 4: Check AI generation tracking
    console.log('\n📋 Test 4: AI generation tracking system');
    const [aiGenerationTable] = await connection.execute(`
      SELECT COUNT(*) as table_exists 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'ai_generation_logs'
    `, [process.env.DB_NAME || 'aitrainerhub']);
    
    if (aiGenerationTable[0].table_exists > 0) {
      console.log('✅ AI generation logs table exists');
      
      // Check chat_messages multimedia columns
      const [chatMessagesColumns] = await connection.execute(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'chat_messages' 
        AND COLUMN_NAME IN ('message_type', 'file_url', 'voice_duration', 'generation_log_id')
      `, [process.env.DB_NAME || 'aitrainerhub']);
      
      const multimediaColumns = chatMessagesColumns.map(col => col.COLUMN_NAME);
      console.log('   Chat messages multimedia columns:', multimediaColumns);
      
      // Check view
      const [views] = await connection.execute(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.VIEWS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'ai_generation_stats'
      `, [process.env.DB_NAME || 'aitrainerhub']);
      
      if (views.length > 0) {
        console.log('✅ AI generation stats view exists');
      } else {
        console.log('❌ AI generation stats view missing');
      }
      
      // Check stored procedure
      const [aiProcedures] = await connection.execute(`
        SELECT ROUTINE_NAME 
        FROM INFORMATION_SCHEMA.ROUTINES 
        WHERE ROUTINE_SCHEMA = ? AND ROUTINE_NAME = 'LogAIGeneration'
      `, [process.env.DB_NAME || 'aitrainerhub']);
      
      if (aiProcedures.length > 0) {
        console.log('✅ LogAIGeneration procedure exists');
      } else {
        console.log('❌ LogAIGeneration procedure missing');
      }
    } else {
      console.log('❌ AI generation logs table does not exist');
    }
    
    // Test 5: Test data insertion and triggers
    console.log('\n📋 Test 5: Testing data insertion and triggers');
    
    // Create a test expert if none exists
    const [existingExperts] = await connection.execute('SELECT id FROM experts LIMIT 1');
    let testExpertId;
    
    if (existingExperts.length === 0) {
      console.log('   Creating test expert...');
      const [expertResult] = await connection.execute(`
        INSERT INTO experts (user_id, name, description, system_prompt, model, is_public) 
        VALUES (1, 'Test Expert', 'A test expert for schema validation', 'You are a helpful assistant', 'gpt-4o-mini', 1)
      `);
      testExpertId = expertResult.insertId;
      console.log('   ✅ Test expert created');
    } else {
      testExpertId = existingExperts[0].id;
      console.log('   ✅ Using existing expert for testing');
    }
    
    // Test review insertion and trigger
    try {
      await connection.execute(`
        INSERT INTO reviews (user_id, expert_id, rating, review_text) 
        VALUES (1, ?, 5, 'Test review for schema validation')
        ON DUPLICATE KEY UPDATE rating = 5, review_text = 'Test review for schema validation'
      `, [testExpertId]);
      
      // Check if expert rating was updated
      const [expertRating] = await connection.execute(`
        SELECT average_rating, total_reviews 
        FROM experts 
        WHERE id = ?
      `, [testExpertId]);
      
      if (expertRating.length > 0 && expertRating[0].average_rating > 0) {
        console.log(`   ✅ Review trigger working - Expert rating: ${expertRating[0].average_rating}, Reviews: ${expertRating[0].total_reviews}`);
      } else {
        console.log('   ❌ Review trigger not working properly');
      }
    } catch (error) {
      console.log('   ❌ Review insertion failed:', error.message);
    }
    
    console.log('\n🎉 Schema enhancement testing completed!');
    
  } catch (error) {
    console.error('❌ Testing failed:', error.message);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run tests
if (require.main === module) {
  testSchemaEnhancements().then(() => {
    console.log('Testing completed');
    process.exit(0);
  }).catch(err => {
    console.error('Testing failed:', err);
    process.exit(1);
  });
}

module.exports = { testSchemaEnhancements };