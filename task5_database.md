# Task 5: Database Schema & Migration Plans

## 🗄️ Database Schema for Simplified Shared Link Flow

### Current Status
✅ **EXISTING** (Migration 004 completed):
- `expert_shares` table - Basic sharing functionality

❌ **REQUIRED** (New Migration 006):
- Consent tracking tables
- Analytics tables
- Share access logs

## 📊 Existing Schema (Migration 004)

### Table: `expert_shares`
```sql
CREATE TABLE expert_shares (
  id INT PRIMARY KEY AUTO_INCREMENT,
  expert_id INT NOT NULL,
  shared_by_user_id INT NOT NULL,
  share_token VARCHAR(255) UNIQUE NOT NULL,
  monitor_enabled BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
  FOREI<PERSON><PERSON>EY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE
);
```

**Status:** ✅ Ready to use for simplified flow

## 🆕 Required Schema (Migration 006)

### Table: `share_consents`
```sql
CREATE TABLE share_consents (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT 'User who gave consent',
  share_token VARCHAR(255) NOT NULL COMMENT 'Reference to expert_shares.share_token',
  expert_id INT NOT NULL COMMENT 'Expert being accessed',
  shared_by_user_id INT NOT NULL COMMENT 'User who shared the expert',
  consent_given BOOLEAN DEFAULT FALSE COMMENT 'Whether user consented to monitoring',
  consent_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When consent was given',
  ip_address VARCHAR(45) COMMENT 'IP address when consent given',
  user_agent TEXT COMMENT 'Browser user agent',
  revoked_at TIMESTAMP NULL COMMENT 'When consent was revoked',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
  FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
  FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE,
  
  -- Unique constraint: one consent per user per share
  UNIQUE KEY unique_user_share (user_id, share_token),
  
  INDEX idx_user_id (user_id),
  INDEX idx_share_token (share_token),
  INDEX idx_expert_id (expert_id),
  INDEX idx_shared_by_user (shared_by_user_id),
  INDEX idx_consent_date (consent_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### Table: `share_analytics`
```sql
CREATE TABLE share_analytics (
  id INT PRIMARY KEY AUTO_INCREMENT,
  share_token VARCHAR(255) NOT NULL COMMENT 'Reference to expert_shares.share_token',
  user_id INT NULL COMMENT 'User who performed action (NULL for anonymous)',
  action_type ENUM('view', 'consent', 'login', 'register', 'chat_start', 'chat_message') NOT NULL,
  expert_id INT NOT NULL COMMENT 'Expert being accessed',
  shared_by_user_id INT NOT NULL COMMENT 'User who created the share',
  session_id VARCHAR(255) NULL COMMENT 'Browser session identifier',
  ip_address VARCHAR(45) COMMENT 'User IP address',
  user_agent TEXT COMMENT 'Browser user agent',
  referer VARCHAR(500) COMMENT 'Referring page URL',
  metadata JSON COMMENT 'Additional action-specific data',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE SET NULL,
  FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
  FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE,
  
  INDEX idx_share_token (share_token),
  INDEX idx_user_id (user_id),
  INDEX idx_action_type (action_type),
  INDEX idx_shared_by_user (shared_by_user_id),
  INDEX idx_created_at (created_at),
  INDEX idx_session_id (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### Table: `share_access_logs`
```sql
CREATE TABLE share_access_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  share_token VARCHAR(255) NOT NULL COMMENT 'Reference to expert_shares.share_token',
  user_id INT NOT NULL COMMENT 'User who accessed the chat',
  chat_session_id INT NOT NULL COMMENT 'Chat session being accessed',
  expert_id INT NOT NULL COMMENT 'Expert being chatted with',
  shared_by_user_id INT NOT NULL COMMENT 'User who can monitor this access',
  monitoring_enabled BOOLEAN DEFAULT FALSE COMMENT 'Whether monitoring was enabled',
  consent_given BOOLEAN DEFAULT FALSE COMMENT 'Whether user consented to monitoring',
  access_started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  message_count INT DEFAULT 0 COMMENT 'Number of messages sent in this session',
  session_duration INT DEFAULT 0 COMMENT 'Session duration in seconds',
  
  FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
  FOREIGN KEY (chat_session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
  FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
  FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE,
  
  INDEX idx_share_token (share_token),
  INDEX idx_user_id (user_id),
  INDEX idx_chat_session (chat_session_id),
  INDEX idx_shared_by_user (shared_by_user_id),
  INDEX idx_access_started (access_started_at),
  INDEX idx_monitoring (monitoring_enabled, consent_given)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 🔄 Migration Script: 006_create_simplified_sharing.js

```javascript
// Migration: Create simplified sharing system
// Created: December 2024
// Description: Add consent tracking and analytics for simplified shared link flow

const mysql = require('mysql2/promise');
require('dotenv').config();

async function getConnection() {
  return mysql.createConnection({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  });
}

async function up() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('🚀 Creating simplified sharing system tables...');
    
    // 1. Create share_consents table
    const createConsentsTable = `
      CREATE TABLE share_consents (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL COMMENT 'User who gave consent',
        share_token VARCHAR(255) NOT NULL COMMENT 'Reference to expert_shares.share_token',
        expert_id INT NOT NULL COMMENT 'Expert being accessed',
        shared_by_user_id INT NOT NULL COMMENT 'User who shared the expert',
        consent_given BOOLEAN DEFAULT FALSE COMMENT 'Whether user consented to monitoring',
        consent_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When consent was given',
        ip_address VARCHAR(45) COMMENT 'IP address when consent given',
        user_agent TEXT COMMENT 'Browser user agent',
        revoked_at TIMESTAMP NULL COMMENT 'When consent was revoked',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
        FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        
        UNIQUE KEY unique_user_share (user_id, share_token),
        
        INDEX idx_user_id (user_id),
        INDEX idx_share_token (share_token),
        INDEX idx_expert_id (expert_id),
        INDEX idx_shared_by_user (shared_by_user_id),
        INDEX idx_consent_date (consent_date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.execute(createConsentsTable);
    console.log('✅ Created share_consents table');
    
    // 2. Create share_analytics table
    const createAnalyticsTable = `
      CREATE TABLE share_analytics (
        id INT PRIMARY KEY AUTO_INCREMENT,
        share_token VARCHAR(255) NOT NULL COMMENT 'Reference to expert_shares.share_token',
        user_id INT NULL COMMENT 'User who performed action (NULL for anonymous)',
        action_type ENUM('view', 'consent', 'login', 'register', 'chat_start', 'chat_message') NOT NULL,
        expert_id INT NOT NULL COMMENT 'Expert being accessed',
        shared_by_user_id INT NOT NULL COMMENT 'User who created the share',
        session_id VARCHAR(255) NULL COMMENT 'Browser session identifier',
        ip_address VARCHAR(45) COMMENT 'User IP address',
        user_agent TEXT COMMENT 'Browser user agent',
        referer VARCHAR(500) COMMENT 'Referring page URL',
        metadata JSON COMMENT 'Additional action-specific data',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE SET NULL,
        FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
        FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        
        INDEX idx_share_token (share_token),
        INDEX idx_user_id (user_id),
        INDEX idx_action_type (action_type),
        INDEX idx_shared_by_user (shared_by_user_id),
        INDEX idx_created_at (created_at),
        INDEX idx_session_id (session_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.execute(createAnalyticsTable);
    console.log('✅ Created share_analytics table');
    
    // 3. Create share_access_logs table
    const createAccessLogsTable = `
      CREATE TABLE share_access_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        share_token VARCHAR(255) NOT NULL COMMENT 'Reference to expert_shares.share_token',
        user_id INT NOT NULL COMMENT 'User who accessed the chat',
        chat_session_id INT NOT NULL COMMENT 'Chat session being accessed',
        expert_id INT NOT NULL COMMENT 'Expert being chatted with',
        shared_by_user_id INT NOT NULL COMMENT 'User who can monitor this access',
        monitoring_enabled BOOLEAN DEFAULT FALSE COMMENT 'Whether monitoring was enabled',
        consent_given BOOLEAN DEFAULT FALSE COMMENT 'Whether user consented to monitoring',
        access_started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        message_count INT DEFAULT 0 COMMENT 'Number of messages sent in this session',
        session_duration INT DEFAULT 0 COMMENT 'Session duration in seconds',
        
        FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        FOREIGN KEY (chat_session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
        FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
        FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        
        INDEX idx_share_token (share_token),
        INDEX idx_user_id (user_id),
        INDEX idx_chat_session (chat_session_id),
        INDEX idx_shared_by_user (shared_by_user_id),
        INDEX idx_access_started (access_started_at),
        INDEX idx_monitoring (monitoring_enabled, consent_given)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.execute(createAccessLogsTable);
    console.log('✅ Created share_access_logs table');
    
    // 4. Add additional columns to existing expert_shares table
    const addColumnsToExpertShares = `
      ALTER TABLE expert_shares 
      ADD COLUMN share_type ENUM('creator', 'third_party') DEFAULT 'creator' COMMENT 'Type of sharing',
      ADD COLUMN click_count INT DEFAULT 0 COMMENT 'Number of times link was clicked',
      ADD COLUMN conversion_count INT DEFAULT 0 COMMENT 'Number of users who registered/logged in',
      ADD COLUMN last_accessed_at TIMESTAMP NULL COMMENT 'Last time link was accessed',
      ADD INDEX idx_share_type (share_type),
      ADD INDEX idx_last_accessed (last_accessed_at)
    `;
    
    await connection.execute(addColumnsToExpertShares);
    console.log('✅ Enhanced expert_shares table with additional columns');
    
    await connection.commit();
    console.log('🎉 Migration 006 completed successfully!');
    
  } catch (error) {
    await connection.rollback();
    console.error('❌ Migration 006 failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

async function down() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('🔄 Rolling back migration 006...');
    
    // Remove added columns from expert_shares
    await connection.execute(`
      ALTER TABLE expert_shares 
      DROP COLUMN share_type,
      DROP COLUMN click_count,
      DROP COLUMN conversion_count,
      DROP COLUMN last_accessed_at
    `);
    console.log('✅ Removed columns from expert_shares');
    
    // Drop tables in reverse order
    await connection.execute('DROP TABLE share_access_logs');
    console.log('✅ Dropped share_access_logs table');
    
    await connection.execute('DROP TABLE share_analytics');
    console.log('✅ Dropped share_analytics table');
    
    await connection.execute('DROP TABLE share_consents');
    console.log('✅ Dropped share_consents table');
    
    await connection.commit();
    console.log('🎉 Migration 006 rollback completed!');
    
  } catch (error) {
    await connection.rollback();
    console.error('❌ Migration 006 rollback failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run migration
if (require.main === module) {
  up().then(() => {
    console.log('Migration completed successfully');
    process.exit(0);
  }).catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
}

module.exports = { up, down };
```

## 📋 Database Queries for Common Operations

### Check if User Needs Consent
```sql
SELECT 
  sc.consent_given,
  sc.consent_date,
  sc.revoked_at
FROM share_consents sc
WHERE sc.user_id = ? 
  AND sc.share_token = ?
  AND sc.revoked_at IS NULL;
```

### Record Consent
```sql
INSERT INTO share_consents (
  user_id, share_token, expert_id, shared_by_user_id, 
  consent_given, ip_address, user_agent
) VALUES (?, ?, ?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE
  consent_given = VALUES(consent_given),
  consent_date = CURRENT_TIMESTAMP,
  ip_address = VALUES(ip_address),
  user_agent = VALUES(user_agent),
  revoked_at = NULL;
```

### Track Share Analytics
```sql
INSERT INTO share_analytics (
  share_token, user_id, action_type, expert_id, 
  shared_by_user_id, session_id, ip_address, 
  user_agent, referer, metadata
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
```

### Get Share Analytics for User
```sql
SELECT 
  es.share_token,
  e.name as expert_name,
  es.click_count,
  es.conversion_count,
  es.created_at,
  es.last_accessed_at,
  COUNT(DISTINCT sa.user_id) as unique_visitors,
  COUNT(DISTINCT sal.chat_session_id) as chat_sessions
FROM expert_shares es
JOIN experts e ON es.expert_id = e.id
LEFT JOIN share_analytics sa ON es.share_token = sa.share_token
LEFT JOIN share_access_logs sal ON es.share_token = sal.share_token
WHERE es.shared_by_user_id = ?
  AND es.is_active = TRUE
GROUP BY es.id
ORDER BY es.created_at DESC;
```

## 🔧 Database Maintenance

### Cleanup Old Analytics (Run Weekly)
```sql
-- Delete analytics older than 90 days
DELETE FROM share_analytics 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- Delete revoked consents older than 30 days
DELETE FROM share_consents 
WHERE revoked_at IS NOT NULL 
  AND revoked_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### Performance Optimization
```sql
-- Add composite indexes for common queries
ALTER TABLE share_analytics 
ADD INDEX idx_share_user_action (share_token, user_id, action_type);

ALTER TABLE share_access_logs 
ADD INDEX idx_shared_by_monitoring (shared_by_user_id, monitoring_enabled);
```

---

## 📊 Schema Summary

### Tables Overview
| Table | Purpose | Status |
|-------|---------|--------|
| `expert_shares` | Basic sharing functionality | ✅ Existing (Migration 004) |
| `share_consents` | Consent tracking | ❌ New (Migration 006) |
| `share_analytics` | Usage analytics | ❌ New (Migration 006) |
| `share_access_logs` | Chat access monitoring | ❌ New (Migration 006) |

### Key Relationships
- `expert_shares` → `experts` (expert being shared)
- `expert_shares` → `user` (user who shared)
- `share_consents` → `user` (user who consented)
- `share_analytics` → `user` (user who performed action)
- `share_access_logs` → `chat_sessions` (chat being monitored)

---

**Document Version:** 3.0 (Simplified Schema)  
**Last Updated:** December 2024  
**Status:** ✅ Ready for Migration 006