const express = require('express');
const chatController = require('../../controllers/chatController');
const { authenticateToken } = require('../../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @swagger
 * tags:
 *   name: Chat
 *   description: Chat functionality with AI experts
 */

// Chat with AI expert
/**
 * @swagger
 * /api/chat:
 *   post:
 *     summary: Send message to AI expert
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *               expertId:
 *                 type: string
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Chat response
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid input
 */
router.post('/', chatController.chat);

// Get thread messages
/**
 * @swagger
 * /api/thread/{threadId}/messages:
 *   get:
 *     summary: Get messages from a thread
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: threadId
 *         schema:
 *           type: string
 *         required: true
 *         description: Thread ID
 *     responses:
 *       200:
 *         description: Thread messages
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Thread not found
 */
router.get('/thread/:threadId/messages', chatController.getThreadMessages);

// Text-to-Speech endpoint
/**
 * @swagger
 * /api/chat/tts:
 *   post:
 *     summary: Convert text to speech
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               text:
 *                 type: string
 *                 description: Text to convert to speech (max 4096 characters)
 *                 maxLength: 4096
 *               voice:
 *                 type: string
 *                 enum: [alloy, echo, fable, onyx, nova, shimmer]
 *                 default: alloy
 *                 description: Voice to use for speech synthesis
 *             required:
 *               - text
 *     responses:
 *       200:
 *         description: Audio file (MP3)
 *         content:
 *           audio/mpeg:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           X-TTS-Cost:
 *             description: Cost of TTS generation
 *             schema:
 *               type: string
 *           X-TTS-Voice:
 *             description: Voice used for generation
 *             schema:
 *               type: string
 *           X-TTS-Text-Length:
 *             description: Length of input text
 *             schema:
 *               type: string
 *       400:
 *         description: Invalid input or insufficient balance
 *       401:
 *         description: Unauthorized
 */
router.post('/tts', chatController.textToSpeech);

module.exports = router;
