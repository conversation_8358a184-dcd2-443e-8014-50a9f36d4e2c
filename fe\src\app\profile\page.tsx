"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  authAPI,
  User,
  UpdateProfileData,
  ChangePasswordData,
} from "@/lib/api";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  User as UserIcon,
  Mail,
  Phone,
  CreditCard,
  Lock,
  Edit,
  Save,
  X,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";

export default function MyProfilePage() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [saving, setSaving] = useState(false);
  const router = useRouter();

  // Form states
  const [profileForm, setProfileForm] = useState<UpdateProfileData>({
    name: "",
    email: "",
    bank_name: "",
    account_holder_name: "",
    account_number: "",
  });

  const [passwordForm, setPasswordForm] = useState<ChangePasswordData>({
    currentPassword: "",
    newPassword: "",
  });

  const [confirmPassword, setConfirmPassword] = useState("");

  useEffect(() => {
    async function fetchProfile() {
      try {
        const profile = await authAPI.getProfile();
        setUser(profile.user);
        setProfileForm({
          name: profile.user.name || "",
          email: profile.user.email || "",
          bank_name: profile.user.bank_name || "",
          account_holder_name: profile.user.account_holder_name || "",
          account_number: profile.user.account_number || "",
        });
      } catch {
        router.push("/login");
      } finally {
        setLoading(false);
      }
    }
    fetchProfile();
  }, [router]);

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    if (saving) return;

    setSaving(true);
    setError("");
    setSuccess("");

    try {
      const response = await authAPI.updateProfile(profileForm);
      setUser(response.user);
      setSuccess("Profile updated successfully!");
      setIsEditing(false);
    } catch (error: any) {
      setError(error.message || "Failed to update profile");
    } finally {
      setSaving(false);
    }
  };

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    if (saving) return;

    if (passwordForm.newPassword !== confirmPassword) {
      setError("New passwords do not match");
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      setError("New password must be at least 6 characters long");
      return;
    }

    setSaving(true);
    setError("");
    setSuccess("");

    try {
      await authAPI.changePassword(passwordForm);
      setSuccess("Password changed successfully!");
      setIsChangingPassword(false);
      setPasswordForm({ currentPassword: "", newPassword: "" });
      setConfirmPassword("");
    } catch (error: any) {
      setError(error.message || "Failed to change password");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) return null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Profile</h1>
          <p className="text-gray-600">
            Manage your personal information and security settings
          </p>
        </div>

        {/* Alert Messages */}
        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50 text-red-800">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-6 border-green-200 bg-green-50 text-green-800">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-4 h-auto">
            <TabsTrigger
              value="profile"
              className="flex items-center gap-2 py-2"
            >
              <UserIcon className="h-4 w-4" />
              Profile Information
            </TabsTrigger>
            <TabsTrigger
              value="security"
              className="flex items-center gap-2 py-2"
            >
              <Lock className="h-4 w-4" />
              Security
            </TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile">
            <Card className="shadow-lg border-0 pt-0">
              <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg py-4">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-xl">
                      Personal Information
                    </CardTitle>
                    <CardDescription className="text-blue-100">
                      Keep your personal details up to date
                    </CardDescription>
                  </div>
                  {!isEditing && (
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => setIsEditing(true)}
                      className="bg-white/20 hover:bg-white/30 text-white border-white/20"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-6">
                {isEditing ? (
                  <form onSubmit={handleUpdateProfile} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label
                          htmlFor="name"
                          className="text-sm font-medium text-gray-700"
                        >
                          Name *
                        </Label>
                        <Input
                          id="name"
                          value={profileForm.name}
                          onChange={(e) =>
                            setProfileForm({
                              ...profileForm,
                              name: e.target.value,
                            })
                          }
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label
                          htmlFor="email"
                          className="text-sm font-medium text-gray-700"
                        >
                          Email *
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          value={profileForm.email}
                          onChange={(e) =>
                            setProfileForm({
                              ...profileForm,
                              email: e.target.value,
                            })
                          }
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                          required
                        />
                      </div>
                    </div>

                    <div className="border-t pt-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                        <CreditCard className="h-5 w-5" />
                        Bank Account Information
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="space-y-2">
                          <Label
                            htmlFor="bank_name"
                            className="text-sm font-medium text-gray-700"
                          >
                            Bank Name
                          </Label>
                          <Input
                            id="bank_name"
                            value={profileForm.bank_name}
                            onChange={(e) =>
                              setProfileForm({
                                ...profileForm,
                                bank_name: e.target.value,
                              })
                            }
                            placeholder="e.g. Bank BCA"
                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label
                            htmlFor="account_holder_name"
                            className="text-sm font-medium text-gray-700"
                          >
                            Account Holder Name
                          </Label>
                          <Input
                            id="account_holder_name"
                            value={profileForm.account_holder_name}
                            onChange={(e) =>
                              setProfileForm({
                                ...profileForm,
                                account_holder_name: e.target.value,
                              })
                            }
                            placeholder="Full name as on bank account"
                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label
                            htmlFor="account_number"
                            className="text-sm font-medium text-gray-700"
                          >
                            Account Number
                          </Label>
                          <Input
                            id="account_number"
                            value={profileForm.account_number}
                            onChange={(e) =>
                              setProfileForm({
                                ...profileForm,
                                account_number: e.target.value,
                              })
                            }
                            placeholder="Account number"
                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-3 pt-4">
                      <Button
                        type="submit"
                        disabled={saving}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        {saving ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </>
                        )}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setIsEditing(false);
                          setError("");
                          setSuccess("");
                        }}
                        className="border-gray-300 hover:bg-gray-50"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Cancel
                      </Button>
                    </div>
                  </form>
                ) : (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-gray-500">
                          Name
                        </Label>
                        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <UserIcon className="h-5 w-5 text-gray-400" />
                          <span className="text-gray-900 font-medium">
                            {user.name}
                          </span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-gray-500">
                          Email
                        </Label>
                        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <Mail className="h-5 w-5 text-gray-400" />
                          <span className="text-gray-900 font-medium">
                            {user.email}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-500">
                        Phone Number
                      </Label>
                      <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <Phone className="h-5 w-5 text-gray-400" />
                        <span className="text-gray-900 font-medium">
                          {user.phone}
                        </span>
                      </div>
                    </div>

                    <div className="border-t pt-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                        <CreditCard className="h-5 w-5" />
                        Bank Account Information
                      </h3>
                      {user.bank_name ||
                      user.account_holder_name ||
                      user.account_number ? (
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div className="space-y-2">
                            <Label className="text-sm font-medium text-gray-500">
                              Bank Name
                            </Label>
                            <div className="p-3 bg-gray-50 rounded-lg">
                              <span className="text-gray-900 font-medium">
                                {user.bank_name || "-"}
                              </span>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium text-gray-500">
                              Account Holder
                            </Label>
                            <div className="p-3 bg-gray-50 rounded-lg">
                              <span className="text-gray-900 font-medium">
                                {user.account_holder_name || "-"}
                              </span>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium text-gray-500">
                              Account Number
                            </Label>
                            <div className="p-3 bg-gray-50 rounded-lg">
                              <span className="text-gray-900 font-medium">
                                {user.account_number || "-"}
                              </span>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                          <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                          <p className="text-gray-600 mb-2">
                            No bank account information added
                          </p>
                          <p className="text-sm text-gray-500">
                            Add your bank details to receive payments
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security">
            <Card className="shadow-lg border-0 pt-0">
              <CardHeader className="bg-gradient-to-r from-red-600 to-pink-600 text-white rounded-t-lg py-4">
                <CardTitle className="text-xl">Security Settings</CardTitle>
                <CardDescription className="text-red-100">
                  Manage your password and security preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Change Password
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Update your password to keep your account secure
                    </p>

                    <Dialog
                      open={isChangingPassword}
                      onOpenChange={setIsChangingPassword}
                    >
                      <DialogTrigger asChild>
                        <Button className="bg-red-600 hover:bg-red-700 text-white">
                          <Lock className="h-4 w-4 mr-2" />
                          Change Password
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                          <DialogTitle>Change Password</DialogTitle>
                          <DialogDescription>
                            Enter your current password and choose a new one.
                          </DialogDescription>
                        </DialogHeader>
                        <form
                          onSubmit={handleChangePassword}
                          className="space-y-4"
                        >
                          <div className="space-y-2">
                            <Label htmlFor="currentPassword">
                              Current Password
                            </Label>
                            <Input
                              id="currentPassword"
                              type="password"
                              value={passwordForm.currentPassword}
                              onChange={(e) =>
                                setPasswordForm({
                                  ...passwordForm,
                                  currentPassword: e.target.value,
                                })
                              }
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="newPassword">New Password</Label>
                            <Input
                              id="newPassword"
                              type="password"
                              value={passwordForm.newPassword}
                              onChange={(e) =>
                                setPasswordForm({
                                  ...passwordForm,
                                  newPassword: e.target.value,
                                })
                              }
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="confirmPassword">
                              Confirm New Password
                            </Label>
                            <Input
                              id="confirmPassword"
                              type="password"
                              value={confirmPassword}
                              onChange={(e) =>
                                setConfirmPassword(e.target.value)
                              }
                              required
                            />
                          </div>
                          <div className="flex gap-3 pt-4">
                            <Button
                              type="submit"
                              disabled={saving}
                              className="bg-red-600 hover:bg-red-700 text-white"
                            >
                              {saving ? (
                                <>
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                  Changing...
                                </>
                              ) : (
                                <>
                                  <Save className="h-4 w-4 mr-2" />
                                  Change Password
                                </>
                              )}
                            </Button>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => {
                                setIsChangingPassword(false);
                                setPasswordForm({
                                  currentPassword: "",
                                  newPassword: "",
                                });
                                setConfirmPassword("");
                                setError("");
                                setSuccess("");
                              }}
                            >
                              Cancel
                            </Button>
                          </div>
                        </form>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
