// sharing.js - Routes for expert sharing functionality
const express = require('express');
const router = express.Router();
const SharingController = require('../controllers/sharingController');
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const { validateShareCreation, validateConsentData, validateAnalyticsQuery } = require('../middleware/validation');
const rateLimit = require('express-rate-limit');

// Rate limiting for sharing endpoints
const shareCreationLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 share creations per windowMs
  message: {
    success: false,
    message: 'Too many shares created from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const shareAccessLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // limit each IP to 30 share accesses per minute
  message: {
    success: false,
    message: 'Too many share access attempts, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const analyticsLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // limit each IP to 20 analytics requests per 5 minutes
  message: {
    success: false,
    message: 'Too many analytics requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Share Management Routes (Protected)

/**
 * @route   POST /api/sharing/create
 * @desc    Create a new share for an expert
 * @access  Private
 * @body    { expertId: number, privacyLevel: 'public'|'private', expiresAt?: string }
 */
router.post('/create', 
  shareCreationLimit,
  authenticateToken, 
  validateShareCreation, 
  SharingController.createShare
);

/**
 * @route   GET /api/sharing/my-shares
 * @desc    Get all shares created by the authenticated user
 * @access  Private
 * @query   { page?: number, limit?: number, status?: 'active'|'expired'|'all' }
 */
router.get('/my-shares', 
  authenticateToken, 
  SharingController.getMyShares
);

/**
 * @route   PUT /api/sharing/:shareToken
 * @desc    Update share settings
 * @access  Private (owner only)
 * @body    { privacyLevel?: 'public'|'private', expiresAt?: string, isActive?: boolean }
 */
router.put('/:shareToken', 
  authenticateToken, 
  SharingController.updateShare
);

/**
 * @route   DELETE /api/sharing/:shareToken
 * @desc    Delete a share
 * @access  Private (owner only)
 */
router.delete('/:shareToken', 
  authenticateToken, 
  SharingController.deleteShare
);

/**
 * @route   GET /api/sharing/:shareToken/url
 * @desc    Get share URL for frontend
 * @access  Private (owner only)
 */
router.get('/:shareToken/url', 
  authenticateToken, 
  SharingController.getShareUrl
);

// Public Share Access Routes

/**
 * @route   GET /api/sharing/:shareToken
 * @desc    Get share information by token (public access)
 * @access  Public (with optional auth for analytics)
 */
router.get('/:shareToken', 
  shareAccessLimit,
  optionalAuth, 
  SharingController.getShare
);

// Consent Management Routes

/**
 * @route   POST /api/sharing/:shareToken/consent
 * @desc    Record user consent for accessing shared expert
 * @access  Private
 * @body    { consentGiven: boolean, consentType?: string }
 */
router.post('/:shareToken/consent', 
  authenticateToken, 
  validateConsentData, 
  SharingController.recordConsent
);

/**
 * @route   GET /api/sharing/:shareToken/consent
 * @desc    Get consent status for a user and share
 * @access  Private
 */
router.get('/:shareToken/consent', 
  authenticateToken, 
  SharingController.getConsentStatus
);

// Chat Session Routes

/**
 * @route   POST /api/sharing/:shareToken/chat/start
 * @desc    Start chat session with shared expert
 * @access  Private
 */
router.post('/:shareToken/chat/start', 
  authenticateToken, 
  SharingController.startChatSession
);

/**
 * @route   POST /api/sharing/:shareToken/chat/activity
 * @desc    Record chat activity
 * @access  Private
 * @body    { messageCount?: number }
 */
router.post('/:shareToken/chat/activity', 
  authenticateToken, 
  SharingController.recordChatActivity
);

// Analytics Routes (Protected)

/**
 * @route   GET /api/sharing/:shareToken/analytics
 * @desc    Get analytics for a specific share
 * @access  Private (owner only)
 * @query   { days?: number }
 */
router.get('/:shareToken/analytics', 
  analyticsLimit,
  authenticateToken, 
  validateAnalyticsQuery,
  SharingController.getShareAnalytics
);

/**
 * @route   GET /api/sharing/analytics/overview
 * @desc    Get analytics overview for all user's shares
 * @access  Private
 * @query   { days?: number }
 */
router.get('/analytics/overview', 
  analyticsLimit,
  authenticateToken, 
  validateAnalyticsQuery,
  SharingController.getAnalyticsOverview
);

/**
 * @route   GET /api/sharing/analytics/realtime
 * @desc    Get real-time analytics
 * @access  Private
 * @query   { hours?: number }
 */
router.get('/analytics/realtime', 
  analyticsLimit,
  authenticateToken, 
  SharingController.getRealTimeAnalytics
);

/**
 * @route   POST /api/sharing/analytics/report
 * @desc    Generate analytics report
 * @access  Private
 * @body    { startDate: string, endDate: string }
 */
router.post('/analytics/report', 
  analyticsLimit,
  authenticateToken, 
  SharingController.generateAnalyticsReport
);

// Error handling middleware for this router
router.use((error, req, res, next) => {
  console.error('Sharing route error:', error);
  
  // Handle specific error types
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: error.details
    });
  }
  
  if (error.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }
  
  if (error.name === 'ForbiddenError') {
    return res.status(403).json({
      success: false,
      message: 'Access denied'
    });
  }
  
  // Default error response
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { error: error.message })
  });
});

module.exports = router;