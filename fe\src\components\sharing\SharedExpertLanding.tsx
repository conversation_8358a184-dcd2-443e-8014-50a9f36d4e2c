'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { AlertCircle, MessageCircle, User, Calendar, Share2, Eye } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { api } from '@/lib/api';
import { asset } from '@/lib/utils';

interface SharedExpert {
  shareToken: string;
  expert: {
    id: number;
    name: string;
    description: string;
    imageUrl?: string;
    labels: string[];
    model: string;
  };
  sharedBy: {
    id: number;
    name: string;
  };
  monitorEnabled: boolean;
  shareType: string;
  createdAt: string;
  clickCount: number;
  conversionCount: number;
}

interface ConsentStatus {
  needsConsent: boolean;
  hasConsented: boolean;
}

interface SharedExpertLandingProps {
  shareToken: string;
}

export default function SharedExpertLanding({ shareToken }: SharedExpertLandingProps) {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  
  const [expert, setExpert] = useState<SharedExpert | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [consentStatus, setConsentStatus] = useState<ConsentStatus>({ needsConsent: false, hasConsented: false });
  const [consentGiven, setConsentGiven] = useState(false);
  const [submittingConsent, setSubmittingConsent] = useState(false);

  const trackClick = useCallback(async () => {
    try {
      await api.post(`/sharing/public/${shareToken}/track`, {
        body: {
          action: 'click'
        }
      });
    } catch (err) {
      console.error('Error tracking click:', err);
    }
  }, [shareToken]);

  const loadSharedExpert = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get(`/sharing/public/${shareToken}`);
      
      if (response.data.success) {
        setExpert(response.data.data);
        // Track the click
        await trackClick();
      } else {
        setError(response.data.message || 'Failed to load shared expert');
      }
    } catch (err: any) {
      console.error('Error loading shared expert:', err);
      if (err.response?.status === 404) {
        setError('This shared link is no longer available or has expired.');
      } else {
        setError('Failed to load shared expert. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  }, [shareToken, trackClick]);

  useEffect(() => {
    loadSharedExpert();
  }, [shareToken, loadSharedExpert]);

  const checkConsentStatus = useCallback(async () => {
    if (!isAuthenticated || !expert?.monitorEnabled) return;
    
    try {
      const response = await api.get(`/sharing/public/${shareToken}/consent-status`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.data.success) {
        setConsentStatus(response.data.data);
        setConsentGiven(response.data.data.hasConsented);
      }
    } catch (err) {
      console.error('Error checking consent status:', err);
    }
  }, [isAuthenticated, expert?.monitorEnabled, shareToken]);

  useEffect(() => {
    if (isAuthenticated && expert?.monitorEnabled) {
      checkConsentStatus();
    }
  }, [isAuthenticated, expert, checkConsentStatus]);

  const handleConsentSubmit = async () => {
    if (!isAuthenticated) {
      toast({
        title: "Authentication Required",
        description: "Please log in to continue.",
        variant: "destructive"
      });
      return;
    }

    setSubmittingConsent(true);
    try {
      const response = await api.post(`/sharing/public/${shareToken}/consent`, {
        body: {
          consented: true
        },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.data.success) {
        setConsentGiven(true);
        setConsentStatus(prev => ({ ...prev, hasConsented: true }));
        toast({
          title: "Consent Recorded",
          description: "You can now start chatting with the expert."
        });
      }
    } catch (err: any) {
      console.error('Error submitting consent:', err);
      toast({
        title: "Error",
        description: err.response?.data?.message || "Failed to record consent. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSubmittingConsent(false);
    }
  };

  const handleStartChat = async () => {
    if (!isAuthenticated) {
      // Redirect to login with return URL
      const returnUrl = `/shared/${shareToken}`;
      router.push(`/login?returnUrl=${encodeURIComponent(returnUrl)}`);
      return;
    }

    if (expert?.monitorEnabled && consentStatus.needsConsent && !consentGiven) {
      await handleConsentSubmit();
      return;
    }

    // Track conversion
    try {
      await api.post(`/sharing/public/${shareToken}/track`, {
        body: {
          action: 'conversion'
        },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
    } catch (err) {
      console.error('Error tracking conversion:', err);
    }

    // Navigate to chat with ref parameter
    router.push(`/chat?expertId=${expert?.expert.id}&ref=${shareToken}`);
  };

  const getExpertIcon = (labels: string[]) => {
    if (labels.includes('business')) return '💼';
    if (labels.includes('education')) return '📚';
    if (labels.includes('health')) return '🏥';
    if (labels.includes('technology')) return '💻';
    if (labels.includes('creative')) return '🎨';
    return '🤖';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading shared expert...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-red-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Link Not Available</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Button onClick={() => router.push('/')} className="bg-blue-600 hover:bg-blue-700">
            Browse Other Experts
          </Button>
        </div>
      </div>
    );
  }

  if (!expert) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Share2 className="h-6 w-6 text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-900">Shared AI Expert</h1>
            </div>
            <p className="text-gray-600">
              {expert.sharedBy.name} shared this AI expert with you
            </p>
          </div>

          {/* Expert Card */}
          <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl mb-6">
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                {expert.expert.imageUrl ? (
                  <Avatar className="w-20 h-20">
                    <AvatarImage src={asset(expert.expert.imageUrl)} alt={expert.expert.name} />
                    <AvatarFallback className="text-2xl bg-blue-100 text-blue-600">
                      {getExpertIcon(expert.expert.labels)}
                    </AvatarFallback>
                  </Avatar>
                ) : (
                  <div className="w-20 h-20 rounded-full bg-blue-100 flex items-center justify-center text-3xl">
                    {getExpertIcon(expert.expert.labels)}
                  </div>
                )}
              </div>
              <CardTitle className="text-2xl text-gray-900">{expert.expert.name}</CardTitle>
              <CardDescription className="text-gray-600 text-lg">
                {expert.expert.description}
              </CardDescription>
              <div className="flex items-center justify-center space-x-2 mt-4">
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {expert.expert.model}
                </Badge>
                {expert.expert.labels.map((label) => (
                  <Badge key={label} variant="outline">
                    {label}
                  </Badge>
                ))}
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-gray-600">
                    <Eye className="h-4 w-4" />
                    <span className="text-sm">Views</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{expert.clickCount}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-gray-600">
                    <MessageCircle className="h-4 w-4" />
                    <span className="text-sm">Chats</span>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{expert.conversionCount}</p>
                </div>
              </div>

              {/* Consent Section */}
              {expert.monitorEnabled && consentStatus.needsConsent && !consentGiven && isAuthenticated && (
                <Alert className="border-amber-200 bg-amber-50">
                  <AlertCircle className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-800">
                    <div className="space-y-3">
                      <p className="font-medium">Monitoring Consent Required</p>
                      <p className="text-sm">
                        This expert creator has enabled monitoring to track chat interactions for analytics purposes. 
                        Your consent is required to proceed.
                      </p>
                      <div className="flex items-start space-x-2">
                        <Checkbox 
                          id="consent" 
                          checked={consentGiven}
                          onCheckedChange={(checked) => setConsentGiven(checked as boolean)}
                        />
                        <label htmlFor="consent" className="text-sm leading-relaxed cursor-pointer">
                          I consent to having my chat interactions with this expert monitored for analytics purposes. 
                          I understand this helps improve the expert's performance.
                        </label>
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Shared Info */}
              <div className="flex items-center justify-between text-sm text-gray-500 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4" />
                  <span>Shared by {expert.sharedBy.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(expert.createdAt).toLocaleDateString()}</span>
                </div>
              </div>

              {/* Action Button */}
              <Button
                onClick={handleStartChat}
                disabled={submittingConsent || (expert.monitorEnabled && consentStatus.needsConsent && !consentGiven && isAuthenticated)}
                className="w-full py-4 text-lg bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                size="lg"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                {!isAuthenticated 
                  ? 'Login to Chat with Expert'
                  : submittingConsent
                  ? 'Recording Consent...'
                  : 'Start Chatting'
                }
              </Button>

              {/* Alternative Action */}
              {expert.monitorEnabled && !consentGiven && isAuthenticated && (
                <div className="text-center">
                  <Button
                    variant="outline"
                    onClick={() => router.push('/')}
                    className="w-full"
                  >
                    Browse Other Experts Instead
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center text-sm text-gray-500">
            <p>
              Powered by AI Trainer Hub • 
              <Button variant="link" className="p-0 h-auto text-blue-600" onClick={() => router.push('/')}>
                Create your own AI expert
              </Button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}