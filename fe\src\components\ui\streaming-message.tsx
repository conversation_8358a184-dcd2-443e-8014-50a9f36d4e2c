"use client";

import React, { useEffect, useRef } from 'react';
import Image from 'next/image';
import { Loader2, User } from 'lucide-react';
import { asset } from '@/lib/utils';

interface StreamingMessageProps {
  role: 'user' | 'assistant';
  content: string;
  isStreaming?: boolean;
  timestamp: number;
  cost?: number;
  tokens?: number;
  expertName?: string;
  expertImageUrl?: string;
  expertIcon?: string;
}

export const StreamingMessage: React.FC<StreamingMessageProps> = ({
  role,
  content,
  isStreaming = false,
  timestamp,
  cost,
  tokens,
  expertName,
  expertImageUrl,
  expertIcon = '🤖'
}) => {
  const messageRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to new content when streaming
  useEffect(() => {
    if (isStreaming && messageRef.current) {
      messageRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
  }, [content, isStreaming]);

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCost = (cost: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(cost);
  };

  return (
    <div
      ref={messageRef}
      className={`flex gap-3 ${role === 'user' ? 'justify-end' : 'justify-start'} group`}
    >
      {/* Assistant Avatar */}
      {role === 'assistant' && (
        <div className="flex-shrink-0">
          {expertImageUrl ? (
            <Image
              src={asset(expertImageUrl)}
              alt={expertName || 'AI Assistant'}
              width={32}
              height={32}
              className="w-8 h-8 object-cover rounded-full border border-gray-200"
            />
          ) : (
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
              style={{ backgroundColor: "#1E3A8A" }}
            >
              {expertIcon}
            </div>
          )}
        </div>
      )}

      {/* Message Content */}
      <div className="flex flex-col max-w-[70%]">
        <div
          className={`rounded-2xl px-4 py-3 ${
            role === 'user'
              ? 'text-white shadow-lg'
              : 'bg-gray-50 text-gray-900 border border-gray-100'
          } ${isStreaming ? 'animate-pulse' : ''}`}
          style={
            role === 'user' ? { backgroundColor: "#1E3A8A" } : {}
          }
        >
          <div
            className="text-sm leading-relaxed"
            dangerouslySetInnerHTML={{
              __html: content.replace(/\n/g, "<br/>"),
            }}
          />
          
          {/* Streaming indicator */}
          {isStreaming && (
            <div className="flex items-center space-x-2 mt-2 pt-2 border-t border-gray-200">
              <Loader2 className="w-3 h-3 animate-spin text-gray-500" />
              <span className="text-xs text-gray-500">Streaming...</span>
            </div>
          )}
        </div>

        {/* Message metadata */}
        <div className={`flex items-center space-x-2 mt-1 px-2 ${
          role === 'user' ? 'justify-end' : 'justify-start'
        }`}>
          <span className="text-xs text-gray-400">
            {formatTime(timestamp)}
          </span>
          
          {/* Cost and token info for completed assistant messages */}
          {role === 'assistant' && !isStreaming && (cost || tokens) && (
            <>
              {tokens && (
                <span className="text-xs text-gray-400">
                  • {tokens.toLocaleString()} tokens
                </span>
              )}
              {cost && (
                <span className="text-xs text-gray-400">
                  • {formatCost(cost)}
                </span>
              )}
            </>
          )}
        </div>
      </div>

      {/* User Avatar */}
      {role === 'user' && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
            <User className="w-4 h-4 text-gray-600" />
          </div>
        </div>
      )}
    </div>
  );
};

// Typing indicator component
export const TypingIndicator: React.FC<{
  expertName?: string;
  expertImageUrl?: string;
  expertIcon?: string;
}> = ({ expertName, expertImageUrl, expertIcon = '🤖' }) => {
  return (
    <div className="flex gap-3 justify-start">
      <div className="flex-shrink-0">
        {expertImageUrl ? (
          <Image
            src={asset(expertImageUrl)}
            alt={expertName || 'AI Assistant'}
            width={32}
            height={32}
            className="w-8 h-8 object-cover rounded-full border border-gray-200"
          />
        ) : (
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm"
            style={{ backgroundColor: "#1E3A8A" }}
          >
            {expertIcon}
          </div>
        )}
      </div>
      
      <div className="rounded-2xl px-4 py-3 bg-gray-50 border border-gray-100 flex items-center gap-2">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <span className="text-sm text-gray-600">
          {expertName || 'AI'} is thinking...
        </span>
      </div>
    </div>
  );
};