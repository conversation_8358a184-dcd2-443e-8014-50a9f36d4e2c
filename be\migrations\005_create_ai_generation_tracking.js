// Migration: Create AI Generation Tracking Table
// Created: 2025-08-14
// Description: Implement ai_generation_logs table and add multimedia support columns to chat_messages

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function getConnection() {
  return mysql.createConnection({
    host: process.env.DB_HOST || '127.0.0.1',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'aitrainerhub'
  });
}

async function up() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('Creating AI generation tracking system...');
    
    // Create ai_generation_logs table
    const createAIGenerationLogsSQL = `
      CREATE TABLE IF NOT EXISTS ai_generation_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        generation_type ENUM('image', 'voice_tts', 'voice_stt', 'labels') NOT NULL,
        cost DECIMAL(10,6) NOT NULL,
        cost_idr DECIMAL(15,2) NOT NULL,
        reference_type ENUM('expert_creation', 'chat_message', 'profile_image') NOT NULL,
        reference_id INT,
        prompt_used TEXT,
        result_url VARCHAR(500),
        model_used VARCHAR(100),
        tokens_used INT DEFAULT 0,
        processing_time_ms INT DEFAULT 0,
        status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        INDEX idx_user_generations (user_id, created_at DESC),
        INDEX idx_generation_type (generation_type),
        INDEX idx_reference (reference_type, reference_id),
        INDEX idx_status (status),
        INDEX idx_cost_tracking (user_id, generation_type, created_at DESC)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.execute(createAIGenerationLogsSQL);
    console.log('✅ Created ai_generation_logs table');
    
    // Add multimedia support columns to chat_messages table
    console.log('Adding multimedia support to chat_messages table...');
    
    // Check if columns already exist before adding them
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'chat_messages' 
      AND COLUMN_NAME IN ('message_type', 'file_url', 'voice_duration', 'generation_log_id')
    `, [process.env.DB_NAME || 'aitrainerhub']);
    
    const existingColumns = columns.map(col => col.COLUMN_NAME);
    
    if (!existingColumns.includes('message_type')) {
      await connection.execute(`
        ALTER TABLE chat_messages 
        ADD COLUMN message_type ENUM('text', 'image', 'voice') DEFAULT 'text' AFTER content
      `);
      console.log('✅ Added message_type column');
    }
    
    if (!existingColumns.includes('file_url')) {
      await connection.execute(`
        ALTER TABLE chat_messages 
        ADD COLUMN file_url VARCHAR(500) AFTER message_type
      `);
      console.log('✅ Added file_url column');
    }
    
    if (!existingColumns.includes('voice_duration')) {
      await connection.execute(`
        ALTER TABLE chat_messages 
        ADD COLUMN voice_duration INT COMMENT 'Voice duration in seconds' AFTER file_url
      `);
      console.log('✅ Added voice_duration column');
    }
    
    if (!existingColumns.includes('generation_log_id')) {
      await connection.execute(`
        ALTER TABLE chat_messages 
        ADD COLUMN generation_log_id INT AFTER voice_duration
      `);
      console.log('✅ Added generation_log_id column');
    }
    
    // Add foreign key constraint for generation_log_id if it doesn't exist
    const [constraints] = await connection.execute(`
      SELECT CONSTRAINT_NAME 
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'chat_messages' 
      AND COLUMN_NAME = 'generation_log_id' 
      AND REFERENCED_TABLE_NAME IS NOT NULL
    `, [process.env.DB_NAME || 'aitrainerhub']);
    
    if (constraints.length === 0) {
      await connection.execute(`
        ALTER TABLE chat_messages 
        ADD FOREIGN KEY (generation_log_id) REFERENCES ai_generation_logs(id) ON DELETE SET NULL
      `);
      console.log('✅ Added foreign key constraint for generation_log_id');
    }
    
    // Create indexes for multimedia support
    const indexQueries = [
      'CREATE INDEX IF NOT EXISTS idx_message_type ON chat_messages(message_type)',
      'CREATE INDEX IF NOT EXISTS idx_multimedia_messages ON chat_messages(message_type, created_at DESC)',
      'CREATE INDEX IF NOT EXISTS idx_voice_messages ON chat_messages(voice_duration)',
      'CREATE INDEX IF NOT EXISTS idx_generation_log ON chat_messages(generation_log_id)'
    ];
    
    for (const indexQuery of indexQueries) {
      try {
        await connection.execute(indexQuery);
      } catch (error) {
        // Index might already exist, continue
        console.log(`Index creation skipped: ${error.message}`);
      }
    }
    console.log('✅ Created multimedia indexes');
    
    // Create stored procedure for logging AI generation
    const createLogGenerationProcedureSQL = `
      CREATE PROCEDURE IF NOT EXISTS LogAIGeneration(
        IN p_user_id INT,
        IN p_generation_type ENUM('image', 'voice_tts', 'voice_stt', 'labels'),
        IN p_cost DECIMAL(10,6),
        IN p_cost_idr DECIMAL(15,2),
        IN p_reference_type ENUM('expert_creation', 'chat_message', 'profile_image'),
        IN p_reference_id INT,
        IN p_prompt_used TEXT,
        IN p_result_url VARCHAR(500),
        IN p_model_used VARCHAR(100),
        IN p_tokens_used INT,
        IN p_processing_time_ms INT,
        OUT p_log_id INT
      )
      BEGIN
        INSERT INTO ai_generation_logs (
          user_id, generation_type, cost, cost_idr, reference_type, reference_id,
          prompt_used, result_url, model_used, tokens_used, processing_time_ms
        ) VALUES (
          p_user_id, p_generation_type, p_cost, p_cost_idr, p_reference_type, p_reference_id,
          p_prompt_used, p_result_url, p_model_used, p_tokens_used, p_processing_time_ms
        );
        
        SET p_log_id = LAST_INSERT_ID();
      END
    `;
    
    await connection.query(createLogGenerationProcedureSQL);
    console.log('✅ Created AI generation logging procedure');
    
    // Create view for AI generation statistics
    const createGenerationStatsViewSQL = `
      CREATE OR REPLACE VIEW ai_generation_stats AS
      SELECT 
        u.user_id,
        u.name,
        u.email,
        COUNT(agl.id) as total_generations,
        SUM(agl.cost_idr) as total_cost_idr,
        SUM(CASE WHEN agl.generation_type = 'image' THEN 1 ELSE 0 END) as image_generations,
        SUM(CASE WHEN agl.generation_type = 'voice_tts' THEN 1 ELSE 0 END) as tts_generations,
        SUM(CASE WHEN agl.generation_type = 'voice_stt' THEN 1 ELSE 0 END) as stt_generations,
        SUM(CASE WHEN agl.generation_type = 'labels' THEN 1 ELSE 0 END) as label_generations,
        SUM(CASE WHEN agl.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN agl.cost_idr ELSE 0 END) as cost_last_30_days,
        SUM(CASE WHEN agl.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN agl.cost_idr ELSE 0 END) as cost_last_7_days,
        MAX(agl.created_at) as last_generation_at
      FROM user u
      LEFT JOIN ai_generation_logs agl ON u.user_id = agl.user_id
      GROUP BY u.user_id
    `;
    
    await connection.query(createGenerationStatsViewSQL);
    console.log('✅ Created AI generation statistics view');
    
    await connection.commit();
    console.log('✅ Migration completed successfully');
    
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    await connection.end();
  }
}

async function down() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('Removing AI generation tracking system...');
    
    // Drop view
    await connection.query('DROP VIEW IF EXISTS ai_generation_stats');
    console.log('✅ Dropped AI generation statistics view');
    
    // Drop stored procedure
    await connection.query('DROP PROCEDURE IF EXISTS LogAIGeneration');
    console.log('✅ Dropped AI generation logging procedure');
    
    // Drop indexes from chat_messages
    const dropIndexQueries = [
      'DROP INDEX IF EXISTS idx_message_type ON chat_messages',
      'DROP INDEX IF EXISTS idx_multimedia_messages ON chat_messages',
      'DROP INDEX IF EXISTS idx_voice_messages ON chat_messages',
      'DROP INDEX IF EXISTS idx_generation_log ON chat_messages'
    ];
    
    for (const dropQuery of dropIndexQueries) {
      try {
        await connection.execute(dropQuery);
      } catch (error) {
        console.log(`Index drop failed (might not exist): ${error.message}`);
      }
    }
    
    // Remove foreign key constraint
    try {
      await connection.execute(`
        ALTER TABLE chat_messages 
        DROP FOREIGN KEY chat_messages_ibfk_2
      `);
    } catch (error) {
      console.log(`Foreign key drop failed: ${error.message}`);
    }
    
    // Remove columns from chat_messages
    const columnsToRemove = ['message_type', 'file_url', 'voice_duration', 'generation_log_id'];
    
    for (const column of columnsToRemove) {
      try {
        await connection.execute(`ALTER TABLE chat_messages DROP COLUMN ${column}`);
        console.log(`✅ Removed ${column} column`);
      } catch (error) {
        console.log(`Column ${column} drop failed: ${error.message}`);
      }
    }
    
    // Drop ai_generation_logs table
    await connection.execute('DROP TABLE IF EXISTS ai_generation_logs');
    console.log('✅ Dropped ai_generation_logs table');
    
    await connection.commit();
    console.log('✅ Rollback completed successfully');
    
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    await connection.end();
  }
}

// Run migration
if (require.main === module) {
  up().then(() => {
    console.log('Migration completed');
    process.exit(0);
  }).catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
}

module.exports = { up, down };