"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Alert } from "@/components/ui/alert";
import StarRating from "@/components/ui/star-rating";
import { api } from "@/lib/api";

interface ReviewFormProps {
  expertId: number;
  expertName: string;
  existingReview?: {
    id: number;
    rating: number;
    review_text?: string;
  };
  onSuccess?: (review: any) => void;
  onCancel?: () => void;
}

const ReviewForm: React.FC<ReviewFormProps> = ({
  expertId,
  expertName,
  existingReview,
  onSuccess,
  onCancel
}) => {
  const [rating, setRating] = useState(existingReview?.rating || 0);
  const [reviewText, setReviewText] = useState(existingReview?.review_text || "");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (rating === 0) {
      setError("Please select a rating");
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      let result;
      if (existingReview) {
        // Update existing review
        result = await api.updateReview(existingReview.id, {
          rating,
          reviewText: reviewText.trim() || undefined
        });
      } else {
        // Create new review
        result = await api.createReview({
          expertId,
          rating,
          reviewText: reviewText.trim() || undefined
        });
      }

      if (result.success) {
        onSuccess?.(result.review);
      } else {
        setError(result.error || "Failed to submit review");
      }
    } catch (err: any) {
      setError(err.message || "Failed to submit review");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>
          {existingReview ? "Update Review" : "Write a Review"}
        </CardTitle>
        <p className="text-sm text-gray-600">
          Share your experience with {expertName}
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              {error}
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="rating">Rating *</Label>
            <div className="flex items-center gap-2">
              <StarRating
                rating={rating}
                interactive
                onRatingChange={setRating}
                size="lg"
              />
              <span className="text-sm text-gray-500">
                ({rating > 0 ? `${rating} star${rating !== 1 ? 's' : ''}` : 'Select rating'})
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reviewText">Review (Optional)</Label>
            <textarea
              id="reviewText"
              value={reviewText}
              onChange={(e) => setReviewText(e.target.value)}
              placeholder="Share your thoughts about this expert..."
              className="w-full min-h-[100px] p-3 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              maxLength={1000}
            />
            <div className="text-xs text-gray-500 text-right">
              {reviewText.length}/1000 characters
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || rating === 0}
              className="flex-1"
            >
              {isSubmitting ? "Submitting..." : existingReview ? "Update Review" : "Submit Review"}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ReviewForm;