const express = require('express');
const reviewController = require('../../controllers/reviewController');
const { authenticateToken } = require('../../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Review:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Review ID
 *         user_id:
 *           type: integer
 *           description: ID of the user who wrote the review
 *         expert_id:
 *           type: integer
 *           description: ID of the expert being reviewed
 *         rating:
 *           type: integer
 *           minimum: 1
 *           maximum: 5
 *           description: Rating from 1 to 5 stars
 *         review_text:
 *           type: string
 *           description: Optional review text
 *         is_verified:
 *           type: boolean
 *           description: Whether the review is verified
 *         is_hidden:
 *           type: boolean
 *           description: Whether the review is hidden by admin
 *         reviewer_name:
 *           type: string
 *           description: Name of the reviewer
 *         expert_name:
 *           type: string
 *           description: Name of the expert
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     
 *     CreateReviewRequest:
 *       type: object
 *       required:
 *         - expertId
 *         - rating
 *       properties:
 *         expertId:
 *           type: integer
 *           description: ID of the expert to review
 *         rating:
 *           type: integer
 *           minimum: 1
 *           maximum: 5
 *           description: Rating from 1 to 5 stars
 *         reviewText:
 *           type: string
 *           maxLength: 1000
 *           description: Optional review text
 *     
 *     RatingStats:
 *       type: object
 *       properties:
 *         total_reviews:
 *           type: integer
 *           description: Total number of reviews
 *         average_rating:
 *           type: string
 *           description: Average rating as decimal string
 *         rating_distribution:
 *           type: object
 *           properties:
 *             5:
 *               type: integer
 *             4:
 *               type: integer
 *             3:
 *               type: integer
 *             2:
 *               type: integer
 *             1:
 *               type: integer
 */

/**
 * @swagger
 * /api/reviews:
 *   post:
 *     summary: Create a new review
 *     tags: [Reviews]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateReviewRequest'
 *     responses:
 *       201:
 *         description: Review created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 review:
 *                   $ref: '#/components/schemas/Review'
 *       400:
 *         description: Validation error or business rule violation
 *       401:
 *         description: Authentication required
 */
router.post('/', authenticateToken, reviewController.createReview);

/**
 * @swagger
 * /api/reviews/my:
 *   get:
 *     summary: Get current user's reviews
 *     tags: [Reviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of reviews per page
 *     responses:
 *       200:
 *         description: User reviews retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 reviews:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Review'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 */
router.get('/my', authenticateToken, reviewController.getUserReviews);

/**
 * @swagger
 * /api/reviews/{reviewId}:
 *   get:
 *     summary: Get review by ID
 *     tags: [Reviews]
 *     parameters:
 *       - in: path
 *         name: reviewId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Review ID
 *     responses:
 *       200:
 *         description: Review retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 review:
 *                   $ref: '#/components/schemas/Review'
 *       404:
 *         description: Review not found
 *   
 *   put:
 *     summary: Update a review
 *     tags: [Reviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: reviewId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Review ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - rating
 *             properties:
 *               rating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *               reviewText:
 *                 type: string
 *                 maxLength: 1000
 *     responses:
 *       200:
 *         description: Review updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Authentication required
 *       404:
 *         description: Review not found or no permission
 */
router.get('/:reviewId', reviewController.getReview);
router.put('/:reviewId', authenticateToken, reviewController.updateReview);

/**
 * @swagger
 * /api/reviews/expert/{expertId}:
 *   get:
 *     summary: Get reviews for an expert
 *     tags: [Reviews]
 *     parameters:
 *       - in: path
 *         name: expertId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Expert ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of reviews per page
 *     responses:
 *       200:
 *         description: Expert reviews retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 reviews:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Review'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 */
router.get('/expert/:expertId', reviewController.getExpertReviews);

/**
 * @swagger
 * /api/reviews/expert/{expertId}/can-review:
 *   get:
 *     summary: Check if user can review an expert
 *     tags: [Reviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: expertId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Expert ID
 *     responses:
 *       200:
 *         description: Review eligibility checked
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 canReview:
 *                   type: boolean
 *                 reason:
 *                   type: string
 *                 message:
 *                   type: string
 */
router.get('/expert/:expertId/can-review', authenticateToken, reviewController.canUserReview);

/**
 * @swagger
 * /api/reviews/expert/{expertId}/stats:
 *   get:
 *     summary: Get rating statistics for an expert
 *     tags: [Reviews]
 *     parameters:
 *       - in: path
 *         name: expertId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Expert ID
 *     responses:
 *       200:
 *         description: Rating statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 stats:
 *                   $ref: '#/components/schemas/RatingStats'
 */
router.get('/expert/:expertId/stats', reviewController.getExpertRatingStats);

// Admin routes
/**
 * @swagger
 * /api/reviews/admin/pending:
 *   get:
 *     summary: Get pending reviews for moderation (Admin only)
 *     tags: [Reviews, Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: Pending reviews retrieved successfully
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin access required
 */
router.get('/admin/pending', authenticateToken, reviewController.getPendingReviews);

/**
 * @swagger
 * /api/reviews/admin/{reviewId}/hide:
 *   put:
 *     summary: Hide a review (Admin only)
 *     tags: [Reviews, Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: reviewId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Review hidden successfully
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Review not found
 */
router.put('/admin/:reviewId/hide', authenticateToken, reviewController.hideReview);

/**
 * @swagger
 * /api/reviews/admin/{reviewId}/show:
 *   put:
 *     summary: Show a hidden review (Admin only)
 *     tags: [Reviews, Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: reviewId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Review shown successfully
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Review not found
 */
router.put('/admin/:reviewId/show', authenticateToken, reviewController.showReview);

/**
 * @swagger
 * /api/reviews/admin/{reviewId}:
 *   delete:
 *     summary: Delete a review (Admin only)
 *     tags: [Reviews, Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: reviewId
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Review deleted successfully
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Review not found
 */
router.delete('/admin/:reviewId', authenticateToken, reviewController.deleteReview);

module.exports = router;