'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs-simple';
import { apiCall } from '@/lib/api';
import { 
  Wallet, 
  Gift, 
  CreditCard, 
  History, 
  Plus,
  Coins,
  DollarSign
} from 'lucide-react';

interface BalanceData {
  pointBalance: number;
  creditBalance: number;
  totalBalance: number;
  totalPointsEarned: number;
  totalCreditsPurchased: number;
  formattedBalance: string;
}

interface Transaction {
  id: number;
  transaction_type: string;
  amount: number;
  balance_before: number;
  balance_after: number;
  description: string;
  created_at: string;
  formattedAmount: string;
}

interface BalanceComponentProps {
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

export default function BalanceComponent({ 
  showActions = true, 
  compact = false, 
  className = '' 
}: BalanceComponentProps) {
  const [balance, setBalance] = useState<BalanceData | null>(null);
  const [pointHistory, setPointHistory] = useState<Transaction[]>([]);
  const [creditHistory, setCreditHistory] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBalanceData();
  }, []);

  const fetchBalanceData = async () => {
    try {
      setLoading(true);
      
      // Fetch balance summary using apiCall directly
      const summaryData = await apiCall('/api/balance/summary');
      
      if (summaryData.success) {
        setBalance(summaryData.data.balance);
        setPointHistory(summaryData.data.recentPointTransactions);
        setCreditHistory(summaryData.data.recentCreditTransactions);
      } else {
        throw new Error(summaryData.message || 'Failed to fetch balance');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Error fetching balance:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'EARNED':
      case 'PURCHASED':
        return 'text-green-600';
      case 'USED':
        return 'text-red-600';
      case 'ADMIN_ADDED':
        return 'text-blue-600';
      case 'ADMIN_DEDUCTED':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'EARNED':
        return <Gift className="w-4 h-4 text-green-600" />;
      case 'PURCHASED':
        return <CreditCard className="w-4 h-4 text-blue-600" />;
      case 'USED':
        return <DollarSign className="w-4 h-4 text-red-600" />;
      default:
        return <Coins className="w-4 h-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Error loading balance: {error}</p>
            <Button 
              onClick={fetchBalanceData} 
              variant="outline" 
              className="mt-2"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!balance) {
    return null;
  }

  if (compact) {
    return (
      <Card className={`bg-gradient-to-r from-blue-50 to-purple-50 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <Wallet className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Balance</p>
                <p className="text-lg font-bold text-gray-900">
                  {formatCurrency(balance.totalBalance)}
                </p>
              </div>
            </div>
            <div className="text-right text-xs text-gray-500">
              <p>{formatCurrency(balance.pointBalance)} points</p>
              <p>{formatCurrency(balance.creditBalance)} credits</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const pointPercentage = balance.totalBalance > 0 
    ? (balance.pointBalance / balance.totalBalance) * 100 
    : 0;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Balance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Total Balance */}
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Balance</p>
                <p className="text-2xl font-bold text-blue-900">
                  {formatCurrency(balance.totalBalance)}
                </p>
              </div>
              <div className="p-3 bg-blue-200 rounded-full">
                <Wallet className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-blue-600">Points: {formatCurrency(balance.pointBalance)}</span>
                <span className="text-blue-600">{pointPercentage.toFixed(1)}%</span>
              </div>
              <Progress value={pointPercentage} className="h-2" />
              <p className="text-xs text-blue-500">
                Credits: {formatCurrency(balance.creditBalance)}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Points */}
        <Card className="bg-gradient-to-r from-green-50 to-green-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Points (Bonus)</p>
                <p className="text-2xl font-bold text-green-900">
                  {formatCurrency(balance.pointBalance)}
                </p>
              </div>
              <div className="p-3 bg-green-200 rounded-full">
                <Gift className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-xs text-green-600">
                Total earned: {formatCurrency(balance.totalPointsEarned)}
              </p>
              {/* No commission message removed as requested */}
            </div>
          </CardContent>
        </Card>

        {/* Credits */}
        <Card className="bg-gradient-to-r from-purple-50 to-purple-100">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">Credits (Paid)</p>
                <p className="text-2xl font-bold text-purple-900">
                  {formatCurrency(balance.creditBalance)}
                </p>
              </div>
              <div className="p-3 bg-purple-200 rounded-full">
                <CreditCard className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4">
              <p className="text-xs text-purple-600">
                Total purchased: {formatCurrency(balance.totalCreditsPurchased)}
              </p>
              {/* Generates commission message removed as requested */}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      {showActions && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Plus className="w-5 h-5" />
              <span>Add Balance</span>
            </CardTitle>
            <CardDescription>
              Top up your credits or learn how to earn more points
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button className="flex items-center space-x-2">
                <CreditCard className="w-4 h-4" />
                <span>Top Up Credits</span>
              </Button>
              {/* 'Earn Points' and 'View History' buttons removed as requested */}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transaction History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <History className="w-5 h-5" />
            <span>Recent Transactions</span>
          </CardTitle>
          <CardDescription>
            Latest point and credit transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="points" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="points" className="flex items-center space-x-2">
                <Gift className="w-4 h-4" />
                <span>Points</span>
              </TabsTrigger>
              <TabsTrigger value="credits" className="flex items-center space-x-2">
                <CreditCard className="w-4 h-4" />
                <span>Credits</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="points" className="space-y-4">
              {pointHistory.length > 0 ? (
                <div className="space-y-3">
                  {pointHistory.map((transaction) => (
                    <div 
                      key={transaction.id} 
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        {getTransactionIcon(transaction.transaction_type)}
                        <div>
                          <p className="font-medium text-gray-900">
                            {transaction.description}
                          </p>
                          <p className="text-sm text-gray-500">
                            {new Date(transaction.created_at).toLocaleDateString('id-ID')}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-semibold ${getTransactionTypeColor(transaction.transaction_type)}`}>
                          {transaction.transaction_type === 'USED' ? '-' : '+'}
                          {transaction.formattedAmount}
                        </p>
                        <p className="text-xs text-gray-500">
                          Balance: {formatCurrency(transaction.balance_after)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Gift className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>No point transactions yet</p>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="credits" className="space-y-4">
              {creditHistory.length > 0 ? (
                <div className="space-y-3">
                  {creditHistory.map((transaction) => (
                    <div 
                      key={transaction.id} 
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        {getTransactionIcon(transaction.transaction_type)}
                        <div>
                          <p className="font-medium text-gray-900">
                            {transaction.description}
                          </p>
                          <p className="text-sm text-gray-500">
                            {new Date(transaction.created_at).toLocaleDateString('id-ID')}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-semibold ${getTransactionTypeColor(transaction.transaction_type)}`}>
                          {transaction.transaction_type === 'USED' ? '-' : '+'}
                          {transaction.formattedAmount}
                        </p>
                        <p className="text-xs text-gray-500">
                          Balance: {formatCurrency(transaction.balance_after)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <CreditCard className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>No credit transactions yet</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
