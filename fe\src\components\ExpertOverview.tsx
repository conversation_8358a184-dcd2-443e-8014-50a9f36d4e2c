'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Brain, 
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Eye,
  Star,
  Zap,
  Target,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { api } from '@/lib/api';

interface OverviewStats {
  totalExperts: number;
  publicExperts: number;
  totalUsers: number;
  totalRevenue: number;
  totalCommission: number;
  last30DaysCommission: number;
  totalSessions: number;
  totalMessages: number;
  averageRating: number;
  topPerformingExpert: string;
}

interface Expert {
  id: number;
  name: string;
  totalUsers: number;
  totalCommission: number;
  last30DaysCommission: number;
}

interface RevenueData {
  month: string;
  revenue: number;
  commission: number;
  growth: number;
}

const ExpertOverview: React.FC = () => {
  const [stats, setStats] = useState<OverviewStats | null>(null);
  const [topExperts, setTopExperts] = useState<Expert[]>([]);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadOverviewData();
  }, []);

  const loadOverviewData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load experts list to calculate overview stats
      const expertsResult = await api.listExperts();
      
      if (expertsResult.success) {
        const experts = expertsResult.experts;
        
        // Calculate overview statistics
        const overviewStats: OverviewStats = {
          totalExperts: experts.length,
          publicExperts: experts.filter((e: any) => e.isPublic).length,
          totalUsers: 0,
          totalRevenue: 0,
          totalCommission: 0,
          last30DaysCommission: 0,
          totalSessions: 0,
          totalMessages: 0,
          averageRating: 0,
          topPerformingExpert: experts[0]?.name || 'N/A'
        };

        const expertPerformance: Expert[] = [];
        const monthlyData: { [key: string]: { revenue: number; commission: number; count: number } } = {};

        // Load individual expert stats
        await Promise.all(
          experts.slice(0, 10).map(async (expert: any) => {
            try {
              const statsResult = await api.getExpertStats(expert.id.toString());
              if (statsResult.success) {
                const expertStats = statsResult.stats;
                
                // Accumulate overview stats
                overviewStats.totalUsers += expertStats.totalUsers;
                overviewStats.totalRevenue += expertStats.totalRevenue || 0;
                overviewStats.totalCommission += expertStats.totalCommission;
                overviewStats.last30DaysCommission += expertStats.last30DaysCommission;
                overviewStats.totalSessions += expertStats.totalSessions;
                overviewStats.totalMessages += expertStats.totalMessages;

                // Add to expert performance list
                expertPerformance.push({
                  id: expert.id,
                  name: expert.name,
                  totalUsers: expertStats.totalUsers,
                  totalCommission: expertStats.totalCommission,
                  last30DaysCommission: expertStats.last30DaysCommission
                });

                // Generate monthly data from expert creation dates and stats
                const createdDate = new Date(expert.createdAt);
                const monthKey = createdDate.toLocaleDateString('en-US', { month: 'short' });
                
                if (!monthlyData[monthKey]) {
                  monthlyData[monthKey] = { revenue: 0, commission: 0, count: 0 };
                }
                
                monthlyData[monthKey].revenue += expertStats.totalRevenue || 0;
                monthlyData[monthKey].commission += expertStats.totalCommission;
                monthlyData[monthKey].count += 1;
              }
            } catch (error) {
              console.error(`Failed to load stats for expert ${expert.id}:`, error);
            }
          })
        );

        // Sort experts by performance and set top performing expert
        expertPerformance.sort((a, b) => b.totalCommission - a.totalCommission);
        
        // Set top performing expert name
        if (expertPerformance.length > 0) {
          overviewStats.topPerformingExpert = expertPerformance[0].name;
        }
        
        // Calculate average rating from expert performance
        if (expertPerformance.length > 0) {
          const totalRating = expertPerformance.reduce((sum, expert) => {
            // Calculate rating based on user engagement (users per expert)
            const avgUsers = overviewStats.totalUsers / expertPerformance.length;
            const expertRating = Math.min(5, Math.max(1, (expert.totalUsers / avgUsers) * 4 + 1));
            return sum + expertRating;
          }, 0);
          overviewStats.averageRating = Number((totalRating / expertPerformance.length).toFixed(1));
        } else {
          overviewStats.averageRating = 0;
        }

        // Convert monthly data to revenue chart format
        const last6Months = [];
        const currentDate = new Date();
        
        for (let i = 5; i >= 0; i--) {
          const monthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
          const monthKey = monthDate.toLocaleDateString('en-US', { month: 'short' });
          
          const monthData = monthlyData[monthKey] || { revenue: 0, commission: 0, count: 0 };
          
          // Calculate growth rate compared to previous month
          let growth = 0;
          if (i < 5) {
            const prevMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i - 1, 1);
            const prevMonthKey = prevMonthDate.toLocaleDateString('en-US', { month: 'short' });
            const prevMonthData = monthlyData[prevMonthKey] || { revenue: 0, commission: 0, count: 0 };
            
            if (prevMonthData.revenue > 0) {
              growth = Math.round(((monthData.revenue - prevMonthData.revenue) / prevMonthData.revenue) * 100);
            }
          }
          
          last6Months.push({
            month: monthKey,
            revenue: monthData.revenue,
            commission: monthData.commission,
            growth: growth
          });
        }
        
        setStats(overviewStats);
        setTopExperts(expertPerformance.slice(0, 5));
        setRevenueData(last6Months);
      } else {
        setError(expertsResult.error || 'Failed to load overview data');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load overview data');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  if (isLoading) {
    return (
      <Card className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6 border-red-200 bg-red-50">
        <div className="text-red-700">
          <p className="font-semibold mb-2">Error loading overview</p>
          <p className="text-sm mb-4">{error}</p>
          <Button 
            onClick={loadOverviewData}
            variant="outline"
            className="border-red-300 text-red-700 hover:bg-red-100"
          >
            Retry
          </Button>
        </div>
      </Card>
    );
  }

  if (!stats) return null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Expert Overview</h2>
          <p className="text-gray-600 mt-1">Performance dashboard and insights</p>
        </div>
        <div className="flex items-center space-x-2">
          <Calendar className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-500">
            Updated: {new Date().toLocaleDateString('en-US', { 
              month: 'short', 
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </span>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-600 text-sm font-medium">Total Experts</p>
              <p className="text-3xl font-bold text-blue-900">{stats.totalExperts}</p>
              <p className="text-xs text-blue-700 mt-1">
                {stats.publicExperts} public
              </p>
            </div>
            <Brain className="w-12 h-12 text-blue-600" />
          </div>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-600 text-sm font-medium">Total Commission</p>
              <p className="text-3xl font-bold text-green-900">
                {formatCurrency(stats.totalCommission).replace('IDR', '').trim()}
              </p>
              <p className="text-xs text-green-700 mt-1">
                Last 30 days: {formatCurrency(stats.last30DaysCommission).replace('IDR', '').trim()}
              </p>
            </div>
            <DollarSign className="w-12 h-12 text-green-600" />
          </div>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-600 text-sm font-medium">Total Users</p>
              <p className="text-3xl font-bold text-purple-900">{formatNumber(stats.totalUsers)}</p>
              <p className="text-xs text-purple-700 mt-1">
                Across all experts
              </p>
            </div>
            <Users className="w-12 h-12 text-purple-600" />
          </div>
        </Card>

        <Card className="p-6 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-600 text-sm font-medium">Total Sessions</p>
              <p className="text-3xl font-bold text-orange-900">{formatNumber(stats.totalSessions)}</p>
              <p className="text-xs text-orange-700 mt-1">
                {formatNumber(stats.totalMessages)} messages
              </p>
            </div>
            <Activity className="w-12 h-12 text-orange-600" />
          </div>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Top Performing Experts */}
        <Card className="lg:col-span-2 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900 flex items-center">
              <Target className="w-5 h-5 mr-2 text-blue-600" />
              Top Performing Experts
            </h3>
            <Button variant="outline" size="sm">
              <Eye className="w-4 h-4 mr-1" />
              View All
            </Button>
          </div>
          
          <div className="space-y-4">
            {topExperts.length > 0 ? (
              topExperts.map((expert, index) => (
                <div key={expert.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-bold">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{expert.name}</p>
                      <p className="text-sm text-gray-600">{expert.totalUsers} users</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-green-600">
                      {formatCurrency(expert.totalCommission).replace('IDR', '').trim()}
                    </p>
                    <p className="text-xs text-gray-500">
                      30d: {formatCurrency(expert.last30DaysCommission).replace('IDR', '').trim()}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Brain className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No expert performance data available yet</p>
              </div>
            )}
          </div>
        </Card>

        {/* Quick Stats */}
        <Card className="p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
            <BarChart3 className="w-5 h-5 mr-2 text-purple-600" />
            Quick Stats
          </h3>
          
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Star className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Avg. Rating</p>
                  <p className="text-sm text-gray-600">User satisfaction</p>
                </div>
              </div>
              <p className="text-2xl font-bold text-blue-600">{stats.averageRating}</p>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Growth Rate</p>
                  <p className="text-sm text-gray-600">This month</p>
                </div>
              </div>
              <p className="text-2xl font-bold text-green-600">
                {revenueData.length > 0 && revenueData[revenueData.length - 1].growth > 0 
                  ? `+${revenueData[revenueData.length - 1].growth}%`
                  : revenueData.length > 0 
                    ? `${revenueData[revenueData.length - 1].growth}%`
                    : '0%'
                }
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Top Expert</p>
                  <p className="text-sm text-gray-600">Best performer</p>
                </div>
              </div>
              <p className="text-sm font-bold text-purple-600 text-right max-w-24 truncate">
                {stats.topPerformingExpert}
              </p>
            </div>

            <div className="pt-4 border-t border-gray-200">
              <Button 
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                onClick={() => window.open('/analytics', '_blank')}
              >
                <PieChart className="w-4 h-4 mr-2" />
                View Detailed Analytics
              </Button>
            </div>
          </div>
        </Card>
      </div>

      {/* Revenue Trend Chart */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
            Revenue Trend Analysis
          </h3>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Calendar className="w-4 h-4 mr-1" />
              Last 6 Months
            </Button>
          </div>
        </div>
        
        {/* Chart Container */}
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200">
              <div className="flex items-center justify-center mb-2">
                <DollarSign className="w-5 h-5 text-blue-600 mr-1" />
                <span className="text-sm text-blue-600 font-medium">Total Revenue</span>
              </div>
              <p className="text-2xl font-bold text-blue-900">
                {formatCurrency(stats.totalRevenue)}
              </p>
              <p className="text-xs text-blue-700 mt-1">All time earnings</p>
            </div>
            
            <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200">
              <div className="flex items-center justify-center mb-2">
                <Target className="w-5 h-5 text-green-600 mr-1" />
                <span className="text-sm text-green-600 font-medium">Your Commission</span>
              </div>
              <p className="text-2xl font-bold text-green-900">
                {formatCurrency(stats.totalCommission)}
              </p>
              <p className="text-xs text-green-700 mt-1">
                {((stats.totalCommission / Math.max(stats.totalRevenue, 1)) * 100).toFixed(1)}% of total
              </p>
            </div>
            
            <div className="text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200">
              <div className="flex items-center justify-center mb-2">
                <Calendar className="w-5 h-5 text-orange-600 mr-1" />
                <span className="text-sm text-orange-600 font-medium">Last 30 Days</span>
              </div>
              <p className="text-2xl font-bold text-orange-900">
                {formatCurrency(stats.last30DaysCommission)}
              </p>
              <p className="text-xs text-orange-700 mt-1">Recent commission</p>
            </div>
          </div>

          {/* Visual Chart */}
          <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-gray-900">Monthly Revenue & Commission</h4>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                  <span className="text-gray-600">Revenue</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-gray-600">Commission</span>
                </div>
              </div>
            </div>
            
            {/* Simple Bar Chart */}
            <div className="grid grid-cols-6 gap-4 items-end h-48">
              {revenueData.map((data) => {
                const maxRevenue = Math.max(...revenueData.map(d => d.revenue));
                const revenueHeight = (data.revenue / maxRevenue) * 100;
                const commissionHeight = (data.commission / maxRevenue) * 100;
                
                return (
                  <div key={data.month} className="flex flex-col items-center space-y-2">
                    {/* Bars */}
                    <div className="flex items-end space-x-1 h-32">
                      {/* Revenue Bar */}
                      <div className="relative group">
                        <div 
                          className="w-6 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-md transition-all duration-300 hover:from-blue-600 hover:to-blue-500"
                          style={{ height: `${revenueHeight}%` }}
                        ></div>
                        {/* Tooltip */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <div className="bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                            {formatCurrency(data.revenue)}
                          </div>
                        </div>
                      </div>
                      
                      {/* Commission Bar */}
                      <div className="relative group">
                        <div 
                          className="w-6 bg-gradient-to-t from-green-500 to-green-400 rounded-t-md transition-all duration-300 hover:from-green-600 hover:to-green-500"
                          style={{ height: `${commissionHeight}%` }}
                        ></div>
                        {/* Tooltip */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <div className="bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                            {formatCurrency(data.commission)}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Month Label */}
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-700">{data.month}</div>
                      {/* Growth Indicator */}
                      <div className="flex items-center justify-center mt-1">
                        {data.growth > 0 ? (
                          <div className="flex items-center text-green-600">
                            <ArrowUpRight className="w-3 h-3" />
                            <span className="text-xs font-medium">{data.growth}%</span>
                          </div>
                        ) : data.growth < 0 ? (
                          <div className="flex items-center text-red-600">
                            <ArrowDownRight className="w-3 h-3" />
                            <span className="text-xs font-medium">{Math.abs(data.growth)}%</span>
                          </div>
                        ) : (
                          <div className="flex items-center text-gray-500">
                            <span className="text-xs font-medium">0%</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Performance Insights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="p-4 border-l-4 border-green-500 bg-green-50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-800">Best Month</p>
                  <p className="text-lg font-bold text-green-900">
                    {revenueData.length > 0 && 
                      revenueData.reduce((prev, current) => 
                        prev.commission > current.commission ? prev : current
                      ).month
                    }
                  </p>
                </div>
                <TrendingUp className="w-8 h-8 text-green-600" />
              </div>
            </Card>

            <Card className="p-4 border-l-4 border-blue-500 bg-blue-50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-800">Avg. Growth</p>
                  <p className="text-lg font-bold text-blue-900">
                    {revenueData.length > 0 && 
                      Math.round(revenueData.reduce((sum, data) => sum + data.growth, 0) / revenueData.length)
                    }%
                  </p>
                </div>
                <BarChart3 className="w-8 h-8 text-blue-600" />
              </div>
            </Card>

            <Card className="p-4 border-l-4 border-purple-500 bg-purple-50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-800">Avg. Commission Rate</p>
                  <p className="text-lg font-bold text-purple-900">
                    {stats.totalRevenue > 0 
                      ? `${((stats.totalCommission / stats.totalRevenue) * 100).toFixed(1)}%`
                      : '0%'
                    }
                  </p>
                </div>
                <Target className="w-8 h-8 text-purple-600" />
              </div>
            </Card>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ExpertOverview;
