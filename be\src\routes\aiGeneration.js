const express = require('express');
const aiGenerationController = require('../controllers/aiGenerationController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     LabelGenerationRequest:
 *       type: object
 *       required:
 *         - name
 *         - systemPrompt
 *       properties:
 *         name:
 *           type: string
 *           description: Expert name
 *         description:
 *           type: string
 *           description: Expert description
 *         systemPrompt:
 *           type: string
 *           description: Expert system prompt
 *         model:
 *           type: string
 *           description: AI model to use
 *           default: gpt-4o-mini
 *     
 *     ImageGenerationRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           description: Expert name
 *         description:
 *           type: string
 *           description: Expert description
 *         labels:
 *           type: array
 *           items:
 *             type: string
 *           description: Expert labels for context
 *     
 *     AIGenerationResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         labels:
 *           type: array
 *           items:
 *             type: string
 *         imageUrl:
 *           type: string
 *         cost:
 *           type: number
 *         tokensUsed:
 *           type: number
 */

/**
 * @swagger
 * /api/ai-generation/labels:
 *   post:
 *     summary: Generate AI labels for expert
 *     tags: [AI Generation]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LabelGenerationRequest'
 *     responses:
 *       200:
 *         description: Labels generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AIGenerationResponse'
 *       400:
 *         description: Invalid request data
 *       402:
 *         description: Insufficient balance
 *       500:
 *         description: Server error
 */
router.post('/labels', authenticateToken, aiGenerationController.generateLabels);

/**
 * @swagger
 * /api/ai-generation/image:
 *   post:
 *     summary: Generate AI image for expert
 *     tags: [AI Generation]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ImageGenerationRequest'
 *     responses:
 *       200:
 *         description: Image generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AIGenerationResponse'
 *       400:
 *         description: Invalid request data
 *       402:
 *         description: Insufficient balance
 *       500:
 *         description: Server error
 */
router.post('/image', authenticateToken, aiGenerationController.generateImage);

/**
 * @swagger
 * /api/ai-generation/costs:
 *   get:
 *     summary: Get AI generation cost estimation
 *     tags: [AI Generation]
 *     responses:
 *       200:
 *         description: Cost estimation retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 costs:
 *                   type: object
 *                   properties:
 *                     labelGeneration:
 *                       type: object
 *                       properties:
 *                         estimated:
 *                           type: number
 *                         description:
 *                           type: string
 *                     imageGeneration:
 *                       type: object
 *                       properties:
 *                         estimated:
 *                           type: number
 *                         description:
 *                           type: string
 *       500:
 *         description: Server error
 */
router.get('/costs', aiGenerationController.getCostEstimation);

/**
 * @swagger
 * /api/ai-generation/history:
 *   get:
 *     summary: Get user's AI generation history
 *     tags: [AI Generation]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of records to retrieve
 *     responses:
 *       200:
 *         description: Generation history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 history:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       user_id:
 *                         type: integer
 *                       type:
 *                         type: string
 *                       model:
 *                         type: string
 *                       prompt_tokens:
 *                         type: integer
 *                       completion_tokens:
 *                         type: integer
 *                       cost_idr:
 *                         type: number
 *                       metadata:
 *                         type: object
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Server error
 */
router.get('/history', authenticateToken, aiGenerationController.getGenerationHistory);

/**
 * @swagger
 * /api/ai-generation/validate-labels:
 *   post:
 *     summary: Validate labels format and content
 *     tags: [AI Generation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - labels
 *             properties:
 *               labels:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Labels to validate
 *     responses:
 *       200:
 *         description: Labels validated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 validLabels:
 *                   type: array
 *                   items:
 *                     type: string
 *                 invalidLabels:
 *                   type: array
 *                   items:
 *                     type: string
 *                 totalValid:
 *                   type: integer
 *                 maxAllowed:
 *                   type: integer
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
router.post('/validate-labels', aiGenerationController.validateLabels);

module.exports = router;