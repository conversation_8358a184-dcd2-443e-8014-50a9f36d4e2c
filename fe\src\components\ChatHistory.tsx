'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { api } from '@/lib/api';
import { MessageCircle, Clock, Trash2, Edit2, User, Bot } from 'lucide-react';

interface ChatSession {
  id: number;
  thread_id: string;
  expert_id: number | null;
  expert_name: string | null;
  expert_model: string | null;
  session_title: string;
  message_count: number;
  last_message_at: string | null;
  user_messages: number;
  assistant_messages: number;
  total_tokens: number;
  total_cost: number;
  created_at: string;
  updated_at: string;
}

const ChatHistory: React.FC = () => {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingSession, setEditingSession] = useState<number | null>(null);
  const [newTitle, setNewTitle] = useState('');

  const formatCurrency = (amount: number) => {
    // Simple Rupiah formatting without browser locale dependency
    const formatted = Math.round(amount).toLocaleString('en-US');
    return `Rp ${formatted}`;
  };

  const loadSessions = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const result = await api.getUserChatSessions(50);
      
      if (result.success) {
        setSessions(result.sessions);
      } else {
        setError(result.error || 'Failed to load chat sessions');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load chat sessions');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadSessions();
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) {
      return 'Just now';
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  const handleUpdateTitle = async (sessionId: number, title: string) => {
    try {
      const result = await api.updateSessionTitle(sessionId.toString(), title);
      if (result.success) {
        setSessions(sessions.map(session => 
          session.id === sessionId 
            ? { ...session, session_title: title }
            : session
        ));
        setEditingSession(null);
        setNewTitle('');
      }
    } catch (error) {
      console.error('Failed to update title:', error);
    }
  };

  const handleDeleteSession = async (sessionId: number) => {
    if (!confirm('Are you sure you want to delete this chat session?')) {
      return;
    }

    try {
      const result = await api.deleteSession(sessionId.toString());
      if (result.success) {
        setSessions(sessions.filter(session => session.id !== sessionId));
      }
    } catch (error) {
      console.error('Failed to delete session:', error);
    }
  };

  const startEditing = (session: ChatSession) => {
    setEditingSession(session.id);
    setNewTitle(session.session_title);
  };

  const cancelEditing = () => {
    setEditingSession(null);
    setNewTitle('');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="mb-8">
            <Link 
              href="/"
              className="inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors"
            >
              <span>←</span>
              <span>Back to Home</span>
            </Link>
          </div>

          <h1 className="text-3xl font-bold text-gray-900 mb-8">Chat History</h1>

          <div className="space-y-4">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 animate-pulse">
                <div className="flex items-center justify-between mb-4">
                  <div className="h-5 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="mb-8">
            <Link 
              href="/"
              className="inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors"
            >
              <span>←</span>
              <span>Back to Home</span>
            </Link>
          </div>

          <div className="text-center py-12">
            <div className="bg-red-50 border border-red-200 rounded-xl p-8 max-w-md mx-auto">
              <div className="text-red-600 text-6xl mb-4">⚠️</div>
              <h3 className="text-xl font-semibold text-red-800 mb-2">Unable to Load Chat History</h3>
              <p className="text-red-600 mb-4">{error}</p>
              <button 
                onClick={loadSessions}
                className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-8">
          <Link 
            href="/"
            className="inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors"
          >
            <span>←</span>
            <span>Back to Home</span>
          </Link>
        </div>

        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Chat History</h1>
          <div className="text-sm text-gray-500">
            {sessions.length} conversation{sessions.length !== 1 ? 's' : ''}
          </div>
        </div>

        {sessions.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">💬</div>
            <h3 className="text-xl font-semibold text-gray-700 mb-2">No Chat History Yet</h3>
            <p className="text-gray-500 mb-6">Start a conversation with an AI expert to see it here!</p>
            <Link 
              href="/"
              className="inline-block px-6 py-3 rounded-xl text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5"
              style={{ backgroundColor: '#1E3A8A' }}
            >
              Browse Experts
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {sessions.map((session) => (
              <div
                key={session.id}
                className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-200 group"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    {editingSession === session.id ? (
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={newTitle}
                          onChange={(e) => setNewTitle(e.target.value)}
                          className="flex-1 px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleUpdateTitle(session.id, newTitle);
                            } else if (e.key === 'Escape') {
                              cancelEditing();
                            }
                          }}
                          autoFocus
                        />
                        <button
                          onClick={() => handleUpdateTitle(session.id, newTitle)}
                          className="px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                        >
                          Save
                        </button>
                        <button
                          onClick={cancelEditing}
                          className="px-3 py-1 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors text-sm"
                        >
                          Cancel
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-900 transition-colors">
                          {session.session_title}
                        </h3>
                        <button
                          onClick={() => startEditing(session)}
                          className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-blue-600 transition-all"
                        >
                          <Edit2 className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                    
                    {session.expert_name && (
                      <div className="flex items-center space-x-2 mt-2">
                        <div 
                          className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs"
                          style={{ backgroundColor: '#1E3A8A' }}
                        >
                          <Bot className="w-3 h-3" />
                        </div>
                        <span className="text-sm text-gray-600">
                          Expert: <span className="font-medium">{session.expert_name}</span>
                        </span>
                        <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                          {session.expert_model}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <Link
                      href={`/chat?threadId=${session.thread_id}`}
                      className="px-4 py-2 rounded-lg text-white font-medium transition-all duration-200 hover:shadow-md"
                      style={{ backgroundColor: '#1E3A8A' }}
                    >
                      Continue
                    </Link>
                    <button
                      onClick={() => handleDeleteSession(session.id)}
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <MessageCircle className="w-4 h-4" />
                      <span>{session.message_count} messages</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <User className="w-4 h-4" />
                      <span>{session.user_messages}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Bot className="w-4 h-4" />
                      <span>{session.assistant_messages}</span>
                    </div>
                    {session.total_cost > 0 && (
                      <div>
                        <span>� {formatCurrency(session.total_cost)}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>
                      {session.last_message_at 
                        ? formatDate(session.last_message_at)
                        : formatDate(session.created_at)
                      }
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatHistory;
