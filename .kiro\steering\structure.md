# Project Structure

## Root Directory
```
/
├── be/                 # Backend application
├── fe/                 # Frontend application  
├── .kiro/              # Kiro IDE configuration
├── prompts/            # AI role prompts
├── schema.sql          # Database schema
├── role_*.prompt       # Individual role prompt files
└── README.md           # Project documentation
```

## Backend Structure (be/)
```
be/
├── src/                # Source code (main application logic)
├── uploads/            # File upload storage
├── logs/               # Application logs
├── node_modules/       # Dependencies
├── server.js           # Main server entry point
├── package.json        # Dependencies and scripts
├── .env                # Environment variables
├── .gitignore          # Git ignore rules
└── API_DOCUMENTATION.md # API documentation
```

## Frontend Structure (fe/)
```
fe/
├── src/                # Source code
├── public/             # Static assets
├── .next/              # Next.js build output
├── node_modules/       # Dependencies
├── package.json        # Dependencies and scripts
├── next.config.ts      # Next.js configuration
├── tsconfig.json       # TypeScript configuration
├── tailwind.config.js  # Tailwind CSS configuration
├── components.json     # UI components configuration
└── eslint.config.mjs   # ESLint configuration
```

## Key Conventions

### File Organization
- Backend logic organized in `be/src/` directory
- Frontend components and pages in `fe/src/` directory
- Shared database schema at root level
- Environment files kept at application root level

### Naming Conventions
- Use kebab-case for file and directory names
- Use camelCase for JavaScript/TypeScript variables and functions
- Use PascalCase for React components
- Use UPPER_CASE for environment variables

### Git Structure
- Separate git repositories for frontend and backend (submodules)
- Main repository contains both applications
- Individual .gitignore files for each application

### Configuration Files
- Environment variables in `.env` files at application level
- TypeScript configuration in `tsconfig.json`
- Package management with npm (package-lock.json present)
- ESLint and Prettier configurations at application level

### Development Workflow
- Backend runs on port 3001 (default)
- Frontend runs on port 3000 (default)
- Both applications can run simultaneously
- Hot reload enabled for development

### API Structure
- RESTful API endpoints from backend
- Swagger documentation available
- CORS configured for frontend-backend communication
- JWT authentication for protected routes