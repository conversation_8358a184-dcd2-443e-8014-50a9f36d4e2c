import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import ClientProvider from './client-provider';
import Navigation from "@/components/Navigation";
import { AuthProvider } from "@/contexts/AuthContext";
import { SocketProvider } from "@/contexts/SocketContext";
import Script from 'next/script';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AI Expert Marketplace - AI Trainer Hub",
  description: "Connect with specialized AI experts tailored to your needs. From business consulting to creative solutions.",
};

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode; }>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <AuthProvider>
          <ClientProvider>
            <SocketProvider>
              <Navigation />
              {children}
            </SocketProvider>
          </ClientProvider>
        </AuthProvider>
        <Script id="affiliate-tracker" strategy="afterInteractive">
          {`
            // Initialize affiliate tracking
            if (typeof window !== 'undefined') {
              const initTracking = async () => {
                const urlParams = new URLSearchParams(window.location.search);
                const refCode = urlParams.get('ref');
                
                if (refCode) {
                  try {
                    const response = await fetch('/api/affiliate/track/' + refCode, {
                      method: 'GET',
                      credentials: 'include'
                    });
                    
                    if (response.ok) {
                      console.log('Affiliate tracking successful');
                    }
                  } catch (error) {
                    console.error('Affiliate tracking failed:', error);
                  }
                }
              };
              
              initTracking();
            }
          `}
        </Script>
      </body>
    </html>
  );
}
