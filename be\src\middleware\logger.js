const fs = require('fs');
const path = require('path');

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Helper function to get current timestamp
const getTimestamp = () => {
  const now = new Date();
  return now.toISOString();
};

// Helper function to get date string for log file naming
const getDateString = () => {
  const now = new Date();
  return now.toISOString().split('T')[0]; // YYYY-MM-DD format
};

// Helper function to write to log file
const writeToLogFile = (logType, logData) => {
  const dateString = getDateString();
  const logFileName = `${logType}-${dateString}.log`;
  const logFilePath = path.join(logsDir, logFileName);
  
  const logEntry = `${getTimestamp()} - ${JSON.stringify(logData, null, 2)}\n`;
  
  fs.appendFile(logFilePath, logEntry, (err) => {
    if (err) {
      console.error('Error writing to log file:', err);
    }
  });
};

// Middleware to log all requests and responses
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substr(2, 9);
  
  // Store original response methods
  const originalSend = res.send;
  const originalJson = res.json;
  
  // Function to create combined log entry
  const createCombinedLog = (responseBody, isJson = false) => {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const combinedLog = {
      requestId,
      timestamp: getTimestamp(),
      duration: `${duration}ms`,
      request: {
        method: req.method,
        url: req.originalUrl,
        path: req.path,
        query: req.query,
        headers: {
          'user-agent': req.get('User-Agent'),
          'content-type': req.get('Content-Type'),
          'authorization': req.get('Authorization'), // Tidak disensor
          'x-forwarded-for': req.get('X-Forwarded-For'),
          'x-real-ip': req.get('X-Real-IP')
        },
        body: req.method !== 'GET' ? req.body : undefined,
        ip: req.ip || req.connection.remoteAddress
      },
      response: {
        statusCode: res.statusCode,
        statusMessage: res.statusMessage,
        headers: {
          'content-type': res.get('Content-Type'),
          'content-length': res.get('Content-Length')
        },
        body: sanitizeResponseBody(responseBody, res.get('Content-Type')),
        size: Buffer.byteLength(responseBody ? (typeof responseBody === 'string' ? responseBody : JSON.stringify(responseBody)) : '', 'utf8') + ' bytes'
      }
    };
    
    writeToLogFile('api-logs', combinedLog);
    console.log(`[${requestId}] ${req.method} ${req.originalUrl} - ${res.statusCode} (${duration}ms)`);
  };
  
  // Override res.send to capture response
  res.send = function(body) {
    createCombinedLog(body);
    return originalSend.call(this, body);
  };
  
  // Override res.json to capture JSON responses
  res.json = function(obj) {
    createCombinedLog(obj, true);
    return originalJson.call(this, obj);
  };
  
  next();
};

// Function to sanitize request body (remove sensitive data)
const sanitizeRequestBody = (body) => {
  if (!body) return undefined;
  
  const sanitized = { ...body };
  
  // Remove sensitive fields kecuali authorization
  const sensitiveFields = ['password', 'apiKey', 'secret', 'key'];
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
};

// Function to sanitize response body
const sanitizeResponseBody = (body, contentType) => {
  if (!body) return undefined;
  
  // If it's a large response, truncate it
  const bodyString = typeof body === 'string' ? body : JSON.stringify(body);
  const maxLogSize = 10000; // 10KB limit for response logging
  
  if (bodyString.length > maxLogSize) {
    return {
      message: '[RESPONSE TOO LARGE - TRUNCATED]',
      size: bodyString.length,
      preview: bodyString.substring(0, 500) + '...'
    };
  }
  
  // If it's JSON, try to parse and sanitize
  if (contentType && contentType.includes('application/json')) {
    try {
      const parsed = typeof body === 'string' ? JSON.parse(body) : body;
      return sanitizeResponseData(parsed);
    } catch (e) {
      return body;
    }
  }
  
  return body;
};

// Function to sanitize response data
const sanitizeResponseData = (data) => {
  if (!data || typeof data !== 'object') return data;
  
  if (Array.isArray(data)) {
    return data.map(item => sanitizeResponseData(item));
  }
  
  const sanitized = { ...data };
  
  // Remove sensitive fields from response kecuali authorization
  const sensitiveFields = ['password', 'apiKey', 'secret', 'key'];
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
};

// Error logger middleware
const errorLogger = (err, req, res, next) => {
  const errorLog = {
    type: 'ERROR',
    timestamp: getTimestamp(),
    request: {
      method: req.method,
      url: req.originalUrl,
      headers: {
        'user-agent': req.get('User-Agent'),
        'content-type': req.get('Content-Type'),
        'authorization': req.get('Authorization') // Tidak disensor
      },
      body: req.method !== 'GET' ? req.body : undefined,
      ip: req.ip || req.connection.remoteAddress
    },
    error: {
      message: err.message,
      stack: err.stack,
      name: err.name,
      code: err.code,
      status: err.status || err.statusCode
    }
  };
  
  writeToLogFile('api-logs', errorLog);
  console.error(`ERROR: ${req.method} ${req.originalUrl} - ${err.message}`);
  
  next(err);
};

module.exports = {
  requestLogger,
  errorLogger
};
