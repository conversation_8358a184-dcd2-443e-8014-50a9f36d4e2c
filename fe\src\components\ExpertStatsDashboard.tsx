"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import StarRating from "@/components/ui/star-rating";
import RatingSummary from "./RatingSummary";
import ReviewList from "./ReviewList";
import { api } from "@/lib/api";
import { TrendingUp, MessageCircle, DollarSign, Star, Users, Eye } from "lucide-react";

interface ExpertStats {
  id: number;
  name: string;
  totalChats: number;
  totalRevenue: number;
  averageRating: number;
  totalReviews: number;
  imageUrl?: string;
}

interface ExpertStatsDashboardProps {
  expertId: number;
  isOwner?: boolean;
}

const ExpertStatsDashboard: React.FC<ExpertStatsDashboardProps> = ({
  expertId,
  isOwner = false
}) => {
  const [stats, setStats] = useState<ExpertStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"overview" | "reviews">("overview");

  useEffect(() => {
    const loadStats = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const result = await api.getExpertStats(expertId.toString());
        
        if (result.success) {
          setStats(result.stats);
        } else {
          setError(result.error || "Failed to load expert statistics");
        }
      } catch (err: any) {
        setError(err.message || "Failed to load expert statistics");
      } finally {
        setIsLoading(false);
      }
    };

    loadStats();
  }, [expertId]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-red-600">
          {error || "Failed to load statistics"}
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {isOwner ? "Your Expert Statistics" : "Expert Statistics"}
          </h2>
          <p className="text-gray-600">{stats.name}</p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant={activeTab === "overview" ? "default" : "outline"}
            onClick={() => setActiveTab("overview")}
          >
            Overview
          </Button>
          <Button
            variant={activeTab === "reviews" ? "default" : "outline"}
            onClick={() => setActiveTab("reviews")}
          >
            Reviews
          </Button>
        </div>
      </div>

      {activeTab === "overview" && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Chats</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalChats}</p>
                  </div>
                  <MessageCircle className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(stats.totalRevenue)}
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Average Rating</p>
                    <div className="flex items-center gap-2">
                      <p className="text-2xl font-bold text-gray-900">
                        {stats.averageRating > 0 ? stats.averageRating.toFixed(1) : "N/A"}
                      </p>
                      {stats.averageRating > 0 && (
                        <StarRating rating={stats.averageRating} size="sm" />
                      )}
                    </div>
                  </div>
                  <Star className="w-8 h-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Reviews</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalReviews}</p>
                  </div>
                  <Users className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Rating Summary */}
          {stats.totalReviews > 0 && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <RatingSummary expertId={expertId} />
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Performance Insights</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-5 h-5 text-blue-600" />
                      <span className="text-sm font-medium">Engagement Rate</span>
                    </div>
                    <span className="text-sm font-bold text-blue-600">
                      {stats.totalChats > 0 ? ((stats.totalReviews / stats.totalChats) * 100).toFixed(1) : 0}%
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-5 h-5 text-green-600" />
                      <span className="text-sm font-medium">Avg. Revenue per Chat</span>
                    </div>
                    <span className="text-sm font-bold text-green-600">
                      {stats.totalChats > 0 ? formatCurrency(stats.totalRevenue / stats.totalChats) : formatCurrency(0)}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Star className="w-5 h-5 text-yellow-600" />
                      <span className="text-sm font-medium">Rating Quality</span>
                    </div>
                    <span className="text-sm font-bold text-yellow-600">
                      {stats.averageRating >= 4.5 ? "Excellent" : 
                       stats.averageRating >= 4.0 ? "Very Good" :
                       stats.averageRating >= 3.5 ? "Good" :
                       stats.averageRating >= 3.0 ? "Average" : "Needs Improvement"}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* No Data State */}
          {stats.totalReviews === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <Eye className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-700 mb-2">
                  No Reviews Yet
                </h3>
                <p className="text-gray-500 mb-4">
                  Start getting conversations to receive your first reviews!
                </p>
                {isOwner && (
                  <div className="text-sm text-gray-400">
                    <p>Tips to get more reviews:</p>
                    <ul className="mt-2 space-y-1">
                      <li>• Make your expert public in the marketplace</li>
                      <li>• Provide helpful and accurate responses</li>
                      <li>• Engage with users in meaningful conversations</li>
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}

      {activeTab === "reviews" && (
        <div className="space-y-6">
          <ReviewList 
            expertId={expertId} 
            showPagination={true}
            limit={10}
          />
        </div>
      )}
    </div>
  );
};

export default ExpertStatsDashboard;