// validation.js - Validation middleware for API endpoints
const { body, query, param, validationResult } = require('express-validator');

/**
 * Handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: error.path,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

/**
 * Validation for share creation
 */
const validateShareCreation = [
  body('expertId')
    .isInt({ min: 1 })
    .withMessage('Expert ID must be a positive integer'),
  
  body('monitorEnabled')
    .optional()
    .isBoolean()
    .withMessage('Monitor enabled must be a boolean'),
  
  handleValidationErrors
];

/**
 * Validation for share updates
 */
const validateShareUpdate = [
  param('shareToken')
    .isLength({ min: 10, max: 100 })
    .withMessage('Share token must be between 10 and 100 characters'),
  
  body('monitorEnabled')
    .optional()
    .isBoolean()
    .withMessage('Monitor enabled must be a boolean'),
  
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  
  handleValidationErrors
];

/**
 * Validation for consent data
 */
const validateConsentData = [
  param('shareToken')
    .isLength({ min: 10, max: 100 })
    .withMessage('Share token must be between 10 and 100 characters'),
  
  body('consentGiven')
    .isBoolean()
    .withMessage('Consent given must be a boolean'),
  
  body('consentType')
    .optional()
    .isIn(['data_usage', 'analytics', 'marketing', 'communication'])
    .withMessage('Invalid consent type'),
  
  handleValidationErrors
];

/**
 * Validation for analytics queries
 */
const validateAnalyticsQuery = [
  query('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Days must be between 1 and 365'),
  
  query('hours')
    .optional()
    .isInt({ min: 1, max: 168 })
    .withMessage('Hours must be between 1 and 168 (7 days)'),
  
  handleValidationErrors
];

/**
 * Validation for analytics report generation
 */
const validateAnalyticsReport = [
  body('startDate')
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date')
    .custom((value, { req }) => {
      const startDate = new Date(value);
      const now = new Date();
      if (startDate > now) {
        throw new Error('Start date cannot be in the future');
      }
      return true;
    }),
  
  body('endDate')
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date')
    .custom((value, { req }) => {
      const endDate = new Date(value);
      const startDate = new Date(req.body.startDate);
      const now = new Date();
      
      if (endDate > now) {
        throw new Error('End date cannot be in the future');
      }
      
      if (endDate <= startDate) {
        throw new Error('End date must be after start date');
      }
      
      // Limit report range to 1 year
      const maxRange = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
      if (endDate - startDate > maxRange) {
        throw new Error('Report range cannot exceed 1 year');
      }
      
      return true;
    }),
  
  handleValidationErrors
];

/**
 * Validation for pagination
 */
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  query('status')
    .optional()
    .isIn(['active', 'expired', 'all'])
    .withMessage('Status must be active, expired, or all'),
  
  handleValidationErrors
];

/**
 * Validation for chat activity
 */
const validateChatActivity = [
  param('shareToken')
    .isLength({ min: 10, max: 100 })
    .withMessage('Share token must be between 10 and 100 characters'),
  
  body('messageCount')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Message count must be between 1 and 1000'),
  
  handleValidationErrors
];

/**
 * Validation for expert creation/update
 */
const validateExpertData = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Expert name must be between 1 and 100 characters'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),
  
  body('systemPrompt')
    .optional()
    .trim()
    .isLength({ max: 5000 })
    .withMessage('System prompt cannot exceed 5000 characters'),
  
  body('category')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Category cannot exceed 50 characters'),
  
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array')
    .custom((tags) => {
      if (tags.length > 10) {
        throw new Error('Cannot have more than 10 tags');
      }
      for (const tag of tags) {
        if (typeof tag !== 'string' || tag.length > 30) {
          throw new Error('Each tag must be a string with max 30 characters');
        }
      }
      return true;
    }),
  
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean'),
  
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a non-negative number'),
  
  handleValidationErrors
];

/**
 * Validation for user registration
 */
const validateUserRegistration = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username can only contain letters, numbers, underscores, and hyphens'),
  
  body('email')
    .trim()
    .isEmail()
    .withMessage('Must be a valid email address')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Password confirmation does not match password');
      }
      return true;
    }),
  
  handleValidationErrors
];

/**
 * Validation for user login
 */
const validateUserLogin = [
  body('email')
    .trim()
    .isEmail()
    .withMessage('Must be a valid email address')
    .normalizeEmail(),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  
  handleValidationErrors
];

/**
 * Validation for password reset request
 */
const validatePasswordResetRequest = [
  body('email')
    .trim()
    .isEmail()
    .withMessage('Must be a valid email address')
    .normalizeEmail(),
  
  handleValidationErrors
];

/**
 * Validation for password reset
 */
const validatePasswordReset = [
  body('token')
    .notEmpty()
    .withMessage('Reset token is required'),
  
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Password confirmation does not match password');
      }
      return true;
    }),
  
  handleValidationErrors
];

/**
 * Validation for file uploads
 */
const validateFileUpload = [
  body('fileType')
    .optional()
    .isIn(['image', 'document', 'audio', 'video'])
    .withMessage('Invalid file type'),
  
  body('maxSize')
    .optional()
    .isInt({ min: 1, max: 50 * 1024 * 1024 }) // 50MB max
    .withMessage('Max size must be between 1 byte and 50MB'),
  
  handleValidationErrors
];

/**
 * Sanitize and validate share token parameter
 */
const validateShareToken = [
  param('shareToken')
    .trim()
    .isLength({ min: 10, max: 100 })
    .withMessage('Invalid share token format')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Share token contains invalid characters'),
  
  handleValidationErrors
];

/**
 * Validation for search queries
 */
const validateSearchQuery = [
  query('q')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search query must be between 1 and 100 characters'),
  
  query('category')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Category filter cannot exceed 50 characters'),
  
  query('tags')
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        const tags = value.split(',').map(tag => tag.trim());
        if (tags.length > 5) {
          throw new Error('Cannot filter by more than 5 tags');
        }
        for (const tag of tags) {
          if (tag.length > 30) {
            throw new Error('Each tag cannot exceed 30 characters');
          }
        }
      }
      return true;
    }),
  
  query('sortBy')
    .optional()
    .isIn(['name', 'created_at', 'updated_at', 'popularity', 'rating'])
    .withMessage('Invalid sort field'),
  
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  
  handleValidationErrors
];

module.exports = {
  handleValidationErrors,
  validateShareCreation,
  validateShareUpdate,
  validateConsentData,
  validateAnalyticsQuery,
  validateAnalyticsReport,
  validatePagination,
  validateChatActivity,
  validateExpertData,
  validateUserRegistration,
  validateUserLogin,
  validatePasswordResetRequest,
  validatePasswordReset,
  validateFileUpload,
  validateShareToken,
  validateSearchQuery
};