# Task 5: User Flow & Business Logic

## 🎯 Simplified Shared Link Flow (Final Decision)

### Core Principles
- **No visitor chat without registration** - Semua chat memerlukan authentication
- **Consent-based monitoring** - Transparency dan user control
- **Re-consent for new shares** - Setiap share link baru memerlukan consent ulang
- **Seamless user experience** - Minimal friction untuk legitimate users

## 🔄 Complete User Flow

### Scenario 1: User B Shares Expert to User C

#### Step 1: Share Creation
```
User B (di dashboard) → Expert Profile → Click "Share" Button
↓
ShareModal opens:
- Enable/disable monitoring toggle
- Generate share link: /shared/[shareToken]
- Copy link button
```

#### Step 2: User C Accesses Share Link
```
User C clicks: https://app.com/shared/abc123
↓
Landing Page (/shared/[shareToken]/page.tsx):
- Expert preview (name, description, avatar)
- "Shared by [User B]" indicator
- Monitoring notice (if enabled):
  "⚠️ User B can monitor your chat activity with this expert"
- <PERSON>sent checkbox: "I understand and agree to monitoring"
- "Login/Register to Chat" button (disabled until consent)
```

#### Step 3: Consent & Authentication
```
User C gives consent → Button enabled
↓
Click "Login/Register to Chat"
↓
Store in cookies/localStorage:
- shareToken: "abc123"
- expertId: "456"
- consentGiven: true
- sharedBy: "User B"
↓
Redirect to: /auth/login?redirect=shared
```

#### Step 4: Post-Authentication Redirect
```
After successful login/register:
↓
Check cookies for shareToken
↓
If found:
- Redirect to: /chat/[expertId]?ref=[shareToken]
- Clear shareToken from cookies
- Track sharing analytics
- Enable monitoring (if consented)
```

#### Step 5: Chat with Monitoring
```
User C chats with expert:
- Normal chat functionality
- Monitoring indicator (if enabled): "🔍 Monitored by User B"
- User B can see analytics:
  - Chat count
  - Last activity
  - Chat history (if monitoring enabled)
```

### Scenario 2: Re-consent for New Share Links

#### When User C Gets Different Share Link
```
User C already used share from User B
↓
User D shares same expert to User C
↓
User C clicks: https://app.com/shared/xyz789
↓
New consent dialog appears:
"This expert is shared by User D. User D can monitor your chat activity."
- New consent required
- Cannot proceed without consent
- Previous consent with User B doesn't apply
```

## 🎭 Use Case Scenarios

### Scenario A: Expert Creator Sharing
```
User A (creator) → Creates expert → Shares own expert
- Full control over sharing settings
- Can enable/disable monitoring
- Receives analytics for all shares
```

### Scenario B: Third-Party Sharing
```
User B (not creator) → Uses expert → Shares to others
- Can create share link for any expert
- Can enable monitoring for their shares
- Only sees analytics for their specific shares
- Original creator (User A) not notified
```

### Scenario C: Multiple Sharers
```
Same expert shared by multiple users:
- User B shares to User C (monitoring enabled)
- User D shares same expert to User E (monitoring disabled)
- Each share link is independent
- Separate consent and analytics
```

## 🔒 Privacy & Consent Logic

### Consent Requirements
```
1. First-time access to any share link:
   → Consent dialog required
   
2. Same user, same sharer, same expert:
   → No new consent needed
   
3. Same user, different sharer, same expert:
   → New consent required
   
4. Same user, same sharer, different expert:
   → New consent required
```

### Consent Storage
```
Database table: share_consents
- user_id (after registration)
- share_token
- expert_id
- shared_by_user_id
- consent_given: boolean
- consent_date: timestamp
- ip_address: string
```

### Monitoring Disclosure
```
When monitoring enabled:
"⚠️ Privacy Notice:
- [Sharer Name] can see your chat activity
- This includes message count and chat history
- You can revoke access anytime in settings
- Your data is protected according to our privacy policy"

Consent checkbox:
"☐ I understand and agree to chat monitoring"
```

## 🚫 Edge Cases & Error Handling

### Case 1: User Doesn't Consent
```
User C doesn't check consent box:
- "Login/Register" button remains disabled
- Show message: "Consent required to proceed"
- Provide alternative: "Find other experts" link
- Option to "Contact sharer" (if enabled)
```

### Case 2: Invalid Share Token
```
Share link expired or invalid:
- Show error page: "Share link not found or expired"
- Suggest: "Browse public experts instead"
- Provide search functionality
```

### Case 3: Expert Deleted
```
Expert no longer exists:
- Show error: "Expert is no longer available"
- Suggest similar experts
- Contact sharer option
```

### Case 4: User Already Logged In
```
User C already logged in when clicking share link:
- Skip login step
- Show consent dialog directly
- After consent: redirect to chat immediately
```

## 📊 Analytics & Tracking

### Share Analytics (for User B)
```
Dashboard shows:
- Total shares created
- Click-through rate
- Conversion rate (visitors → users)
- Active chat sessions
- Total messages sent
- Last activity per user
```

### Monitoring Data (if enabled)
```
User B can see:
- Who accessed the expert
- When they last chatted
- Number of messages sent
- Chat history (with timestamps)
- User activity patterns
```

### Privacy Controls
```
User C can:
- View who is monitoring their chats
- Revoke monitoring consent
- Delete chat history
- Block specific sharers
- Export their data
```

## 🔄 Data Flow Architecture

### Cookie/LocalStorage Structure
```javascript
// Stored when user clicks share link (before auth)
const shareData = {
  shareToken: "abc123",
  expertId: "456",
  sharedBy: "User B",
  consentGiven: true,
  timestamp: "2024-12-01T10:00:00Z",
  expiresAt: "2024-12-01T11:00:00Z" // 1 hour expiry
};
```

### URL Parameters
```
Share link: /shared/[shareToken]
Post-auth redirect: /chat/[expertId]?ref=[shareToken]
Analytics tracking: ?utm_source=share&utm_medium=link&utm_campaign=[shareToken]
```

### Database Relationships
```
expert_shares:
- share_token (unique)
- expert_id
- shared_by_user_id
- monitoring_enabled
- created_at

share_consents:
- user_id
- share_token
- consent_given
- consent_date

share_analytics:
- share_token
- user_id (who accessed)
- action_type (view, consent, chat)
- timestamp
```

---

## 🎯 Implementation Priority

### Phase 1: Core Flow (Week 1-2)
1. Landing page with expert preview
2. Consent dialog component
3. Cookie management for shareToken
4. Redirect logic after authentication

### Phase 2: Analytics (Week 3)
1. Share creation and management
2. Basic analytics tracking
3. Monitoring dashboard

### Phase 3: Privacy Controls (Week 4)
1. Consent management
2. Privacy settings
3. Data export/deletion

---

**Document Version:** 3.0 (Simplified Flow)  
**Last Updated:** December 2024  
**Status:** ✅ Final Flow Approved