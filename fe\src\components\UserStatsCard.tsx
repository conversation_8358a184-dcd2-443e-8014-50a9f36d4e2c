"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { api } from "@/lib/api";

interface UserStats {
  total_sessions: number;
  total_messages: number;
  total_tokens_used: number;
  total_cost_idr: number;
  total_cost_usd: number;
  total_cost_formatted_idr: string;
  total_cost_formatted_usd: string;
  last_chat_at: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export default function UserStatsCard() {
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadUserStats();
  }, []);

  const loadUserStats = async () => {
    try {
      setLoading(true);
      const result = await api.getUserStats();
      
      if (result.success) {
        setStats(result.stats);
      } else {
        setError(result.error || 'Failed to load user statistics');
      }
    } catch (err) {
      console.error('Error loading user stats:', err);
      setError('Failed to load user statistics');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            <div className="h-3 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-red-600">
          <h3 className="font-semibold mb-2">Error</h3>
          <p className="text-sm">{error}</p>
        </div>
      </Card>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4 text-gray-900">Your Usage Statistics</h3>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{stats.total_sessions}</div>
          <div className="text-sm text-gray-600">Total Sessions</div>
        </div>
        
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{stats.total_messages}</div>
          <div className="text-sm text-gray-600">Total Messages</div>
        </div>
      </div>

      <div className="space-y-3 mb-4">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Tokens Used:</span>
          <span className="font-medium">{stats.total_tokens_used.toLocaleString()}</span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Total Cost (IDR):</span>
          <span className="font-bold text-orange-600">{stats.total_cost_formatted_idr}</span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Total Cost (USD):</span>
          <span className="font-medium text-gray-500">{stats.total_cost_formatted_usd}</span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Last Chat:</span>
          <span className="text-sm text-gray-500">{formatDate(stats.last_chat_at)}</span>
        </div>
      </div>

      <div className="text-xs text-gray-400 border-t pt-3">
        <div>Exchange Rate: $1 = Rp 20,000</div>
        <div>Statistics updated: {formatDate(stats.updated_at)}</div>
      </div>
    </Card>
  );
}
