"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert } from "@/components/ui/alert";
import StarRating from "@/components/ui/star-rating";
import { api } from "@/lib/api";
import { formatDistanceToNow } from "date-fns";
import { Flag, ChevronLeft, ChevronRight } from "lucide-react";

interface Review {
  id: number;
  user_id: number;
  expert_id: number;
  rating: number;
  review_text?: string;
  is_verified: boolean;
  is_hidden: boolean;
  reviewer_name: string;
  created_at: string;
  updated_at: string;
}

interface ReviewListProps {
  expertId: number;
  showPagination?: boolean;
  limit?: number;
  onReviewReport?: (reviewId: number) => void;
}

const ReviewList: React.FC<ReviewListProps> = ({
  expertId,
  showPagination = true,
  limit = 10,
  onReviewReport
}) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);

  const loadReviews = useCallback(async (page: number = 1) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await api.getExpertReviews(expertId, page, limit);
      
      if (result.success) {
        setReviews(result.reviews);
        setCurrentPage(result.pagination.page);
        setTotalPages(result.pagination.totalPages);
        setTotal(result.pagination.total);
      } else {
        setError(result.error || "Failed to load reviews");
      }
    } catch (err: any) {
      setError(err.message || "Failed to load reviews");
    } finally {
      setIsLoading(false);
    }
  }, [expertId, limit]);

  useEffect(() => {
    loadReviews(1);
  }, [expertId, loadReviews]);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      loadReviews(page);
    }
  };

  const handleReportReview = (reviewId: number) => {
    onReviewReport?.(reviewId);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        {error}
      </Alert>
    );
  }

  if (reviews.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-gray-500">No reviews yet. Be the first to review this expert!</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">
          Reviews ({total})
        </h3>
      </div>

      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id}>
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {review.reviewer_name.charAt(0).toUpperCase()}
                </div>
                
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">
                        {review.reviewer_name}
                        {review.is_verified && (
                          <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                            Verified
                          </span>
                        )}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <StarRating rating={review.rating} size="sm" />
                        <span className="text-xs text-gray-500">
                          {formatDistanceToNow(new Date(review.created_at), { addSuffix: true })}
                        </span>
                      </div>
                    </div>
                    
                    {onReviewReport && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleReportReview(review.id)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <Flag className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                  
                  {review.review_text && (
                    <p className="text-gray-700 text-sm leading-relaxed">
                      {review.review_text}
                    </p>
                  )}
                  
                  {review.updated_at !== review.created_at && (
                    <p className="text-xs text-gray-400">
                      Updated {formatDistanceToNow(new Date(review.updated_at), { addSuffix: true })}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {showPagination && totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Showing {((currentPage - 1) * limit) + 1} to {Math.min(currentPage * limit, total)} of {total} reviews
          </p>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="w-4 h-4" />
              Previous
            </Button>
            
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, index) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = index + 1;
                } else if (currentPage <= 3) {
                  pageNum = index + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + index;
                } else {
                  pageNum = currentPage - 2 + index;
                }
                
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReviewList;