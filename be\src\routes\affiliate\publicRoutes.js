const express = require('express');
const affiliateController = require('../../controllers/affiliateController');

const router = express.Router();

// Track visit
/**
 * @swagger
 * /api/affiliate/track/{referralCode}:
 *   post:
 *     summary: Track affiliate referral visit
 *     tags: [Affiliate]
 *     parameters:
 *       - in: path
 *         name: referralCode
 *         schema:
 *           type: string
 *         required: true
 *         description: Referral code to track
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               visitorId:
 *                 type: string
 *                 description: Existing visitor ID (optional, will generate new if not provided)
 *                 example: "visitor-uuid-123"
 *     responses:
 *       200:
 *         description: Visit tracked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 visitorId:
 *                   type: string
 *                   description: Visitor ID for frontend to store
 *                 referralCode:
 *                   type: string
 *                 expiresAt:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Invalid referral code
 *       404:
 *         description: Referral code not found
 */
router.post('/track/:referralCode', affiliateController.trackVisit);

// Get visitor info
/**
 * @swagger
 * /api/affiliate/visitor-info:
 *   get:
 *     summary: Get visitor information via query parameter
 *     tags: [Affiliate]
 *     parameters:
 *       - in: query
 *         name: visitorId
 *         schema:
 *           type: string
 *         required: true
 *         description: Visitor ID to get info for
 *         example: "visitor-uuid-123"
 *     responses:
 *       200:
 *         description: Visitor information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 hasTracking:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 referralCode:
 *                   type: string
 *                 affiliateName:
 *                   type: string
 *                 expiresAt:
 *                   type: string
 *                   format: date-time
 *                 daysRemaining:
 *                   type: integer
 *   post:
 *     summary: Get visitor information via request body
 *     tags: [Affiliate]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               visitorId:
 *                 type: string
 *                 required: true
 *                 description: Visitor ID to get info for
 *                 example: "visitor-uuid-123"
 *     responses:
 *       200:
 *         description: Visitor information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 hasTracking:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 referralCode:
 *                   type: string
 *                 affiliateName:
 *                   type: string
 *                 expiresAt:
 *                   type: string
 *                   format: date-time
 *                 daysRemaining:
 *                   type: integer
 */
router.get('/visitor-info', affiliateController.getVisitorInfo);
router.post('/visitor-info', affiliateController.getVisitorInfo);

// Validate referral code
/**
 * @swagger
 * /api/affiliate/validate/{referralCode}:
 *   get:
 *     summary: Validate referral code
 *     tags: [Affiliate]
 *     parameters:
 *       - in: path
 *         name: referralCode
 *         schema:
 *           type: string
 *         required: true
 *         description: Referral code to validate
 *     responses:
 *       200:
 *         description: Valid referral code
 *       404:
 *         description: Invalid referral code
 */
router.get('/validate/:referralCode', affiliateController.validateReferralCode);

// Get top affiliates
/**
 * @swagger
 * /api/affiliate/top-affiliates:
 *   get:
 *     summary: Get top performing affiliates
 *     tags: [Affiliate]
 *     responses:
 *       200:
 *         description: List of top affiliates
 */
router.get('/top-affiliates', affiliateController.getTopAffiliates);

module.exports = router;
