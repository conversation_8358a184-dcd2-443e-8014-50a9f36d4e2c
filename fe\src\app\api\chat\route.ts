import { NextRequest, NextResponse } from 'next/server';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export async function POST(req: NextRequest) {
  try {
    const { threadId, message, expertId, expertContext } = await req.json();

    if (!message) {
      return new NextResponse('Missing message', { status: 400 });
    }

    // Get token from Authorization header
    const authorization = req.headers.get('authorization');
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return new NextResponse('Missing or invalid authorization header', { status: 401 });
    }

    // Proxy request to backend with token (userId will be extracted from token by auth middleware)
    const response = await fetch(`${API_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authorization,
      },
      body: JSON.stringify({
        threadId,
        message,
        expertId,
        expertContext,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      return new NextResponse(JSON.stringify(error), {
        status: response.status,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Chat API error:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
