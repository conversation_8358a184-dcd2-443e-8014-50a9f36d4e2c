# Database Interaction Rules

## Node.js Script-Based Database Operations

### Database Migration Workflow
When database changes are needed:

1. **Create Node.js Migration Scripts**: Always create numbered migration scripts in `be/migrations/` directory
   - Format: `XXX_descriptive_name.js` (e.g., `002_add_user_preferences.js`)
   - Use mysql2 connection from existing backend setup
   - Include both UP and DOWN migration functions

2. **Migration Script Structure**:
   ```javascript
   // Migration: Add user preferences table
   // Created: YYYY-MM-DD
   // Description: Brief description of changes
   
   const mysql = require('mysql2/promise');
   require('dotenv').config();
   
   const connection = mysql.createConnection({
     host: process.env.DB_HOST,
     user: process.env.DB_USER,
     password: process.env.DB_PASSWORD,
     database: process.env.DB_NAME
   });
   
   async function up() {
     const sql = `
       CREATE TABLE user_preferences (
         id INT PRIMARY KEY AUTO_INCREMENT,
         user_id INT NOT NULL,
         preferences JSON,
         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
         FOREIGN KEY (user_id) REFERENCES users(id)
       )
     `;
     await connection.execute(sql);
     console.log('✅ Created user_preferences table');
   }
   
   async function down() {
     await connection.execute('DROP TABLE user_preferences');
     console.log('✅ Dropped user_preferences table');
   }
   
   // Run migration
   if (require.main === module) {
     up().then(() => {
       console.log('Migration completed');
       process.exit(0);
     }).catch(err => {
       console.error('Migration failed:', err);
       process.exit(1);
     });
   }
   
   module.exports = { up, down };
   ```

3. **Execution Process**:
   - Create the Node.js migration script
   - Test the script in development
   - Run: `node be/migrations/XXX_feature_name.js`
   - Update main `schema.sql` if needed

### Database Interaction Best Practices

1. **Always Use Transactions**: Wrap multiple operations in transactions
2. **Connection Management**: Properly close database connections
3. **Error Handling**: Include try-catch blocks and meaningful error messages
4. **Environment Variables**: Use .env for database credentials
5. **Logging**: Add console logs for migration progress

### Script Creation Pattern
When database work is needed:
```bash
# 1. Create migration script
touch be/migrations/XXX_feature_name.js

# 2. Edit with Node.js code using mysql2
# 3. Test the migration
node be/migrations/XXX_feature_name.js

# 4. Update schema.sql if needed
# 5. Restart backend if schema changes affect models
```

### Utility Scripts
Create utility scripts for common database operations:

```javascript
// be/scripts/db-utils.js
const mysql = require('mysql2/promise');
require('dotenv').config();

async function getConnection() {
  return mysql.createConnection({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  });
}

async function runQuery(sql, params = []) {
  const connection = await getConnection();
  try {
    const [results] = await connection.execute(sql, params);
    return results;
  } finally {
    await connection.end();
  }
}

module.exports = { getConnection, runQuery };
```

### Emergency Rollback
Include rollback functions in migration scripts:
```javascript
// To rollback: node -e "require('./XXX_migration.js').down()"
```

## Development Database Rules

- Use consistent naming conventions (snake_case for tables/columns)
- Always include created_at and updated_at timestamps
- Use appropriate data types and constraints
- Index foreign keys and frequently queried columns
- Keep migration scripts small and focused on single changes
- Use mysql2/promise for async/await syntax
- Always close database connections properly