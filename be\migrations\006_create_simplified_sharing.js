// Migration: Create simplified sharing system
// Created: December 2024
// Description: Add consent tracking and analytics for simplified shared link flow

const mysql = require('mysql2/promise');
require('dotenv').config();

async function getConnection() {
  return mysql.createConnection({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  });
}

async function up() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('🚀 Creating simplified sharing system tables...');
    
    // 1. Create share_consents table
    const createConsentsTable = `
      CREATE TABLE share_consents (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL COMMENT 'User who gave consent',
        share_token VARCHAR(255) NOT NULL COMMENT 'Reference to expert_shares.share_token',
        expert_id INT NOT NULL COMMENT 'Expert being accessed',
        shared_by_user_id INT NOT NULL COMMENT 'User who shared the expert',
        consent_given BOOLEAN DEFAULT FALSE COMMENT 'Whether user consented to monitoring',
        consent_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When consent was given',
        ip_address VARCHAR(45) COMMENT 'IP address when consent given',
        user_agent TEXT COMMENT 'Browser user agent',
        revoked_at TIMESTAMP NULL COMMENT 'When consent was revoked',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
        FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        
        UNIQUE KEY unique_user_share (user_id, share_token),
        
        INDEX idx_user_id (user_id),
        INDEX idx_share_token (share_token),
        INDEX idx_expert_id (expert_id),
        INDEX idx_shared_by_user (shared_by_user_id),
        INDEX idx_consent_date (consent_date)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.execute(createConsentsTable);
    console.log('✅ Created share_consents table');
    
    // 2. Create share_analytics table
    const createAnalyticsTable = `
      CREATE TABLE share_analytics (
        id INT PRIMARY KEY AUTO_INCREMENT,
        share_token VARCHAR(255) NOT NULL COMMENT 'Reference to expert_shares.share_token',
        user_id INT NULL COMMENT 'User who performed action (NULL for anonymous)',
        action_type ENUM('view', 'consent', 'login', 'register', 'chat_start', 'chat_message') NOT NULL,
        expert_id INT NOT NULL COMMENT 'Expert being accessed',
        shared_by_user_id INT NOT NULL COMMENT 'User who created the share',
        session_id VARCHAR(255) NULL COMMENT 'Browser session identifier',
        ip_address VARCHAR(45) COMMENT 'User IP address',
        user_agent TEXT COMMENT 'Browser user agent',
        referer VARCHAR(500) COMMENT 'Referring page URL',
        metadata JSON COMMENT 'Additional action-specific data',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE SET NULL,
        FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
        FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        
        INDEX idx_share_token (share_token),
        INDEX idx_user_id (user_id),
        INDEX idx_action_type (action_type),
        INDEX idx_shared_by_user (shared_by_user_id),
        INDEX idx_created_at (created_at),
        INDEX idx_session_id (session_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.execute(createAnalyticsTable);
    console.log('✅ Created share_analytics table');
    
    // 3. Create share_access_logs table
    const createAccessLogsTable = `
      CREATE TABLE share_access_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        share_token VARCHAR(255) NOT NULL COMMENT 'Reference to expert_shares.share_token',
        user_id INT NOT NULL COMMENT 'User who accessed the chat',
        chat_session_id INT NOT NULL COMMENT 'Chat session being accessed',
        expert_id INT NOT NULL COMMENT 'Expert being chatted with',
        shared_by_user_id INT NOT NULL COMMENT 'User who can monitor this access',
        monitoring_enabled BOOLEAN DEFAULT FALSE COMMENT 'Whether monitoring was enabled',
        consent_given BOOLEAN DEFAULT FALSE COMMENT 'Whether user consented to monitoring',
        access_started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        message_count INT DEFAULT 0 COMMENT 'Number of messages sent in this session',
        session_duration INT DEFAULT 0 COMMENT 'Session duration in seconds',
        
        FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        FOREIGN KEY (chat_session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
        FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
        FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        
        INDEX idx_share_token (share_token),
        INDEX idx_user_id (user_id),
        INDEX idx_chat_session (chat_session_id),
        INDEX idx_shared_by_user (shared_by_user_id),
        INDEX idx_access_started (access_started_at),
        INDEX idx_monitoring (monitoring_enabled, consent_given)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.execute(createAccessLogsTable);
    console.log('✅ Created share_access_logs table');
    
    // 4. Add analytics columns to expert_shares table
    const addAnalyticsColumns = `
      ALTER TABLE expert_shares 
      ADD COLUMN IF NOT EXISTS click_count INT DEFAULT 0 COMMENT 'Number of times share link was clicked',
      ADD COLUMN IF NOT EXISTS conversion_count INT DEFAULT 0 COMMENT 'Number of successful chat conversions',
      ADD COLUMN IF NOT EXISTS last_accessed_at TIMESTAMP NULL COMMENT 'Last time share was accessed'
    `;
    
    await connection.execute(addAnalyticsColumns);
    console.log('✅ Enhanced expert_shares table with analytics columns');
    
    await connection.commit();
    console.log('🎉 Migration completed successfully!');
    
  } catch (error) {
    await connection.rollback();
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

async function down() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('🔄 Rolling back simplified sharing system...');
    
    // Drop tables in reverse order (due to foreign key constraints)
    await connection.execute('DROP TABLE IF EXISTS share_access_logs');
    console.log('✅ Dropped share_access_logs table');
    
    await connection.execute('DROP TABLE IF EXISTS share_analytics');
    console.log('✅ Dropped share_analytics table');
    
    await connection.execute('DROP TABLE IF EXISTS share_consents');
    console.log('✅ Dropped share_consents table');
    
    // Remove analytics columns from expert_shares
    const removeAnalyticsColumns = `
      ALTER TABLE expert_shares 
      DROP COLUMN IF EXISTS click_count,
      DROP COLUMN IF EXISTS conversion_count,
      DROP COLUMN IF EXISTS last_accessed_at
    `;
    
    await connection.execute(removeAnalyticsColumns);
    console.log('✅ Removed analytics columns from expert_shares table');
    
    await connection.commit();
    console.log('🎉 Rollback completed successfully!');
    
  } catch (error) {
    await connection.rollback();
    console.error('❌ Rollback failed:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run migration if called directly
if (require.main === module) {
  up().then(() => {
    console.log('Migration completed successfully');
    process.exit(0);
  }).catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
}

module.exports = { up, down };