const openai = require('../config/openai');
const expertService = require('./expertService');
const BalanceService = require('./balanceService');
const CurrencyUtils = require('../utils/currencyUtils');
const fs = require('fs');
const path = require('path');

class ChatService {
  // Clean response by removing citation patterns like 【1:1†source†L1-L5】
  cleanResponse(response) {
    return response.replace(/【\d+:\d+†.*?†L\d+-L\d+】/g, '');
  }

  // Calculate cost based on tokens using expert service pricing
  calculateCost(model, promptTokens = 0, completionTokens = 0, pricingPercentage = 0, currency = 'IDR') {
    if (!model) {
      // Fallback to old calculation if no model specified
      const totalTokens = promptTokens + completionTokens;
      const baseCostUSD = (totalTokens / 1000) * 0.03;
      const platformCommissionUSD = baseCostUSD;
      const expertCommissionUSD = (pricingPercentage / 100) * baseCostUSD;
      const totalCostUSD = baseCostUSD + platformCommissionUSD + expertCommissionUSD;
      
      return currency === 'IDR' ? totalCostUSD * 20000 : totalCostUSD;
    }
    
    const result = expertService.calculateExpertTokenCost(model, promptTokens, completionTokens, pricingPercentage, currency);
    return result.totalPrice;
  }

  calculateDetailedCost(model, promptTokens = 0, completionTokens = 0, pricingPercentage = 0, currency = 'IDR') {
    if (!model) {
      // Fallback to old calculation if no model specified
      const totalTokens = promptTokens + completionTokens;
      const baseCostUSD = (totalTokens / 1000) * 0.03;
      const platformCommissionUSD = baseCostUSD;
      const expertCommissionUSD = (pricingPercentage / 100) * baseCostUSD;
      const totalCostUSD = baseCostUSD + platformCommissionUSD + expertCommissionUSD;
      
      if (currency === 'IDR') {
        return {
          basePrice: baseCostUSD * 20000,
          platformCommission: platformCommissionUSD * 20000,
          expertCommission: expertCommissionUSD * 20000,
          totalPrice: totalCostUSD * 20000,
          pricingPercentage: pricingPercentage
        };
      }
      
      return {
        basePrice: baseCostUSD,
        platformCommission: platformCommissionUSD,
        expertCommission: expertCommissionUSD,
        totalPrice: totalCostUSD,
        pricingPercentage: pricingPercentage
      };
    }
    
    return expertService.calculateExpertTokenCost(model, promptTokens, completionTokens, pricingPercentage, currency);
  }

  calculateCostInIDR(model, promptTokens = 0, completionTokens = 0, pricingPercentage = 0) {
    return this.calculateCost(model, promptTokens, completionTokens, pricingPercentage, 'IDR');
  }

  calculateCostInUSD(model, promptTokens = 0, completionTokens = 0, pricingPercentage = 0) {
    return this.calculateCost(model, promptTokens, completionTokens, pricingPercentage, 'USD');
  }

  async createThread() {
    const thread = await openai.beta.threads.create();
    return thread.id;
  }

  async addMessageToThread(threadId, message) {
    await openai.beta.threads.messages.create(threadId, {
      role: 'user',
      content: message,
    });
  }

  async runAssistant(threadId, assistantId = null) {
    if (!assistantId) {
      throw new Error('Assistant ID is required but not provided');
    }

    console.log('🔍 DEBUG - Running assistant with ID:', assistantId);

    const run = await openai.beta.threads.runs.create(threadId, {
      assistant_id: assistantId,
    });
    return run;
  }

  async waitForCompletion(threadId, runId) {
    let runStatus = await openai.beta.threads.runs.retrieve(threadId, runId);
    
    while (runStatus.status === 'queued' || runStatus.status === 'in_progress') {
      await new Promise(resolve => setTimeout(resolve, 1000));
      runStatus = await openai.beta.threads.runs.retrieve(threadId, runId);
    }
    
    return runStatus;
  }

  async getLastMessage(threadId) {
    const messages = await openai.beta.threads.messages.list(threadId);
    return messages.data[0];
  }

  async getThreadMessages(threadId, userId) {
    // TODO: Add user validation logic here
    // For now, we'll just log the userId for tracking
    console.log(`Getting messages for thread ${threadId} by user ${userId}`);
    
    const messages = await openai.beta.threads.messages.list(threadId);
    return messages.data.reverse().map(msg => ({
      id: msg.id,
      role: msg.role,
      content: msg.role === 'assistant' ? this.cleanResponse(msg.content[0].text.value) : msg.content[0].text.value,
      created_at: msg.created_at
    }));
  }

  async processChatStreaming(message, threadId = null, userId = null, expertContext = null, socket = null, streamId = null) {
    try {
      // Check user balance before processing
      if (userId) {
        // Calculate estimated cost
        const estimatedTokens = message.length * 1.5; // Rough estimation
        const model = expertContext?.model || 'gpt-4o-mini';
        const pricingPercentage = expertContext?.pricingPercentage || 0;
        const estimatedCost = this.calculateCost(model, estimatedTokens, estimatedTokens, pricingPercentage, 'IDR');
        
        // Check if user can afford
        const affordability = await BalanceService.canAfford(userId, estimatedCost);
        if (!affordability.canAfford) {
          if (socket && streamId) {
            socket.emit('stream_error', {
              streamId,
              error: 'Insufficient balance',
              shortfall: affordability.shortfall,
              requiredAmount: estimatedCost,
              availableBalance: affordability.totalAvailable,
              formatted: {
                shortfall: CurrencyUtils.formatIDR(affordability.shortfall),
                required: CurrencyUtils.formatIDR(estimatedCost),
                available: CurrencyUtils.formatIDR(affordability.totalAvailable)
              }
            });
          }
          return {
            success: false,
            error: 'Insufficient balance',
            shortfall: affordability.shortfall,
            requiredAmount: estimatedCost,
            availableBalance: affordability.totalAvailable
          };
        }
      }
      
      // Create thread if not provided (for OpenAI Assistant API)
      let openaiThreadId = threadId;
      let isNewThread = false;
      
      if (!openaiThreadId) {
        openaiThreadId = await this.createThread();
        isNewThread = true;
      } else {
        // Check if OpenAI thread exists
        try {
          await openai.beta.threads.retrieve(openaiThreadId);
        } catch (error) {
          if (error.status === 404) {
            // OpenAI thread doesn't exist, create new one
            openaiThreadId = await this.createThread();
            isNewThread = true;
          } else {
            throw error;
          }
        }
      }

      // Use expert's assistant ID if provided, otherwise use default
      let assistantId = process.env.ASSISTANT_ID;
      if (expertContext && expertContext.assistantId) {
        assistantId = expertContext.assistantId;
      }

      // Validate assistant ID
      if (!assistantId) {
        if (socket && streamId) {
          socket.emit('stream_error', {
            streamId,
            error: 'No assistant ID available',
            message: 'Expert does not have an associated assistant ID. Please contact support.'
          });
        }
        return {
          success: false,
          error: 'No assistant ID available',
          message: 'Expert does not have an associated assistant ID. Please contact support.'
        };
      }

      // Add message to thread
      await this.addMessageToThread(openaiThreadId, message);

      // Initialize cost tracking
      if (socket && streamId) {
        const streamingCostCalculator = require('../utils/streamingCostCalculator');
        const model = expertContext?.model || 'gpt-4o-mini';
        const pricingPercentage = expertContext?.pricingPercentage || 0;
        streamingCostCalculator.initializeStream(streamId, model, pricingPercentage);
      }

      // Create streaming run
      const stream = await openai.beta.threads.runs.stream(openaiThreadId, {
        assistant_id: assistantId,
      });

      let fullResponse = '';
      let totalTokens = 0;
      let promptTokens = 0;
      let completionTokens = 0;

      // Handle streaming events
      stream.on('textDelta', (delta, snapshot) => {
        const deltaText = delta.value || '';
        fullResponse += deltaText;
        
        if (socket && streamId) {
          socket.emit('stream_chunk', {
            streamId,
            type: 'text_delta',
            content: deltaText,
            timestamp: Date.now()
          });

          // Emit real-time cost estimate
          const streamingCostCalculator = require('../utils/streamingCostCalculator');
          const costUpdate = streamingCostCalculator.estimateCostFromResponse(streamId, fullResponse.length);
          if (costUpdate) {
            socket.emit('cost_update', {
              streamId,
              estimatedCost: costUpdate.estimatedCost,
              totalTokens: costUpdate.totalTokens,
              costBreakdown: costUpdate.costBreakdown,
              timestamp: Date.now()
            });
          }
        }
      });

      stream.on('runStepDelta', (delta, snapshot) => {
        // Handle any step-level deltas if needed
      });

      stream.on('messageDelta', (delta, snapshot) => {
        // Handle message-level deltas if needed
      });

      stream.on('error', (error) => {
        console.error('Streaming error:', error);
        if (socket && streamId) {
          socket.emit('stream_error', {
            streamId,
            error: error.message || 'Streaming failed',
            canRetry: true,
            timestamp: Date.now()
          });
        }
      });

      stream.on('end', () => {
        if (socket && streamId) {
          socket.emit('stream_ended', {
            streamId,
            timestamp: Date.now()
          });
        }
      });

      // Wait for stream to complete
      const finalRun = await stream.finalRun();
      
      if (finalRun.status === 'completed') {
        // Get usage information
        if (finalRun.usage) {
          totalTokens = finalRun.usage.total_tokens || 0;
          promptTokens = finalRun.usage.prompt_tokens || 0;
          completionTokens = finalRun.usage.completion_tokens || 0;
        }

        // Clean the response
        const cleanedResponse = this.cleanResponse(fullResponse);
        
        // Get model and pricing from expert context for accurate pricing
        const model = expertContext?.model || 'gpt-4o-mini';
        const pricingPercentage = expertContext?.pricingPercentage || 0;
        
        // Calculate detailed cost breakdown
        const costDetails = this.calculateDetailedCost(model, promptTokens, completionTokens, pricingPercentage, 'IDR');
        
        // Deduct balance if user is authenticated
        let balanceUsage = null;
        if (userId) {
          try {
            balanceUsage = await BalanceService.useBalance(
              userId,
              costDetails.totalPrice,
              `Chat with ${expertContext?.name || 'AI Expert'}`,
              openaiThreadId,
              'chat_session'
            );
          } catch (balanceError) {
            console.error('Failed to deduct balance:', balanceError);
            if (socket && streamId) {
              socket.emit('stream_error', {
                streamId,
                error: 'Failed to process payment',
                details: balanceError.message
              });
            }
            return {
              success: false,
              error: 'Failed to process payment',
              details: balanceError.message
            };
          }
        }

        // Finalize cost calculation
        if (socket && streamId) {
          const streamingCostCalculator = require('../utils/streamingCostCalculator');
          streamingCostCalculator.finalizeStream(streamId, {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_tokens: totalTokens
          });
        }

        // Emit completion event
        if (socket && streamId) {
          socket.emit('stream_complete', {
            streamId,
            response: cleanedResponse,
            threadId: openaiThreadId,
            isNewThread,
            totalTokens,
            cost: costDetails.totalPrice,
            costBreakdown: costDetails,
            balanceUsage: balanceUsage ? {
              pointsUsed: balanceUsage.pointsUsed,
              creditsUsed: balanceUsage.creditsUsed,
              generatesCommission: balanceUsage.generatesCommission
            } : null,
            usage: {
              prompt_tokens: promptTokens,
              completion_tokens: completionTokens,
              total_tokens: totalTokens
            },
            timestamp: Date.now()
          });
        }
        
        return {
          success: true,
          response: cleanedResponse,
          threadId: openaiThreadId,
          isNewThread: isNewThread,
          tokensUsed: totalTokens,
          cost: costDetails.totalPrice,
          costBreakdown: costDetails,
          balanceUsage: balanceUsage,
          usage: {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_tokens: totalTokens
          },
          model: model
        };
      } else {
        const errorMsg = `Assistant run failed with status: ${finalRun.status}`;
        if (socket && streamId) {
          socket.emit('stream_error', {
            streamId,
            error: errorMsg
          });
        }
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error('ChatService.processChatStreaming error:', error);
      if (socket && streamId) {
        socket.emit('stream_error', {
          streamId,
          error: error.message || 'Failed to process chat'
        });
      }
      return {
        success: false,
        error: error.message || 'Failed to process chat'
      };
    }
  }

  // Text-to-Speech functionality
  async textToSpeech(text, voice = 'alloy', userId = null) {
    try {
      // Check user balance if userId provided
      if (userId) {
        const estimatedCost = this.calculateTTSCost(text.length);
        const affordability = await BalanceService.canAfford(userId, estimatedCost);
        if (!affordability.canAfford) {
          return {
            success: false,
            error: 'Insufficient balance',
            shortfall: affordability.shortfall,
            requiredAmount: estimatedCost,
            availableBalance: affordability.totalAvailable
          };
        }
      }

      // Generate speech using OpenAI TTS
      // Ensure text is a string
      const textString = typeof text === 'string' ? text : String(text);
      
      const mp3 = await openai.audio.speech.create({
        model: 'tts-1',
        voice: voice, // alloy, echo, fable, onyx, nova, shimmer
        input: textString,
      });

      // Convert to buffer
      const buffer = Buffer.from(await mp3.arrayBuffer());
      
      // Calculate actual cost
      const actualCost = this.calculateTTSCost(text.length);
      
      // Deduct balance if user is authenticated
      let balanceUsage = null;
      if (userId) {
        try {
          balanceUsage = await BalanceService.useBalance(
            userId,
            actualCost,
            'Text-to-Speech generation',
            null,
            'tts_generation'
          );
        } catch (balanceError) {
          console.error('Failed to deduct balance for TTS:', balanceError);
          return {
            success: false,
            error: 'Failed to process payment',
            details: balanceError.message
          };
        }
      }

      // Log TTS generation to ai_generation_logs
      if (userId) {
        try {
          const db = require('../config/database');
          await db.execute(
            `INSERT INTO ai_generation_logs 
             (user_id, generation_type, cost, cost_idr, reference_type, prompt_used, model_used, status) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              userId,
              'voice_tts',
              actualCost / 20000, // USD cost
              actualCost, // IDR cost
              'chat_message',
              text.substring(0, 1000), // Store first 1000 chars of text
              'tts-1',
              'completed'
            ]
          );
        } catch (logError) {
          console.error('Failed to log TTS generation:', logError);
          // Don't fail the request if logging fails
        }
      }

      return {
        success: true,
        audioBuffer: buffer,
        cost: actualCost,
        balanceUsage: balanceUsage,
        voice: voice,
        textLength: text.length
      };
    } catch (error) {
      console.error('TTS error:', error);
      return {
        success: false,
        error: error.message || 'Failed to generate speech'
      };
    }
  }

  // Calculate TTS cost based on character count
  calculateTTSCost(characterCount, currency = 'IDR') {
    // OpenAI TTS pricing: $15.00 per 1M characters
    const baseCostUSD = (characterCount / 1000000) * 15.00;
    const platformCommissionUSD = baseCostUSD * 0.1; // 10% platform commission
    const totalCostUSD = baseCostUSD + platformCommissionUSD;
    
    return currency === 'IDR' ? totalCostUSD * 20000 : totalCostUSD;
  }

  async processChat(message, threadId = null, userId = null, expertContext = null) {
    try {
      // Check user balance before processing
      if (userId) {
        // Calculate estimated cost
        const estimatedTokens = message.length * 1.5; // Rough estimation
        const model = expertContext?.model || 'gpt-4o-mini';
        const pricingPercentage = expertContext?.pricingPercentage || 0;
        const estimatedCost = this.calculateCost(model, estimatedTokens, estimatedTokens, pricingPercentage, 'IDR');
        
        // Check if user can afford
        const affordability = await BalanceService.canAfford(userId, estimatedCost);
        if (!affordability.canAfford) {
          return {
            success: false,
            error: 'Insufficient balance',
            shortfall: affordability.shortfall,
            requiredAmount: estimatedCost,
            availableBalance: affordability.totalAvailable,
            formatted: {
              shortfall: CurrencyUtils.formatIDR(affordability.shortfall),
              required: CurrencyUtils.formatIDR(estimatedCost),
              available: CurrencyUtils.formatIDR(affordability.totalAvailable)
            }
          };
        }
      }
      
      // Create thread if not provided (for OpenAI Assistant API)
      let openaiThreadId = threadId;
      let isNewThread = false;
      
      if (!openaiThreadId) {
        openaiThreadId = await this.createThread();
        isNewThread = true;
      } else {
        // Check if OpenAI thread exists
        try {
          await openai.beta.threads.retrieve(openaiThreadId);
        } catch (error) {
          if (error.status === 404) {
            // OpenAI thread doesn't exist, create new one
            openaiThreadId = await this.createThread();
            isNewThread = true;
          } else {
            throw error;
          }
        }
      }

      // Use expert's assistant ID if provided, otherwise use default
      let assistantId = process.env.ASSISTANT_ID;
      if (expertContext && expertContext.assistantId) {
        assistantId = expertContext.assistantId;
      }

      console.log('🔍 DEBUG - Expert context:', expertContext);
      console.log('🔍 DEBUG - Assistant ID to use:', assistantId);

      // Validate assistant ID
      if (!assistantId) {
        return {
          success: false,
          error: 'No assistant ID available',
          message: 'Expert does not have an associated assistant ID. Please contact support.',
          code: 'MISSING_ASSISTANT_ID',
          debug: {
            expertContext: expertContext,
            hasExpertContext: !!expertContext,
            hasAssistantId: !!(expertContext && expertContext.assistantId)
          }
        };
      }

      // Add message to thread
      await this.addMessageToThread(openaiThreadId, message);

      // Run the assistant
      const run = await this.runAssistant(openaiThreadId, assistantId);

      // Wait for completion
      const runStatus = await this.waitForCompletion(openaiThreadId, run.id);

      if (runStatus.status === 'completed') {
        const lastMessage = await this.getLastMessage(openaiThreadId);
        const rawResponse = lastMessage.content[0].text.value;
        const cleanedResponse = this.cleanResponse(rawResponse);
        
        // Get model and pricing from expert context for accurate pricing
        const model = expertContext?.model || 'gpt-4o-mini';
        const pricingPercentage = expertContext?.pricingPercentage || 0;
        const promptTokens = runStatus.usage?.prompt_tokens || 0;
        const completionTokens = runStatus.usage?.completion_tokens || 0;
        const totalTokens = runStatus.usage?.total_tokens || 0;
        
        // Calculate detailed cost breakdown
        const costDetails = this.calculateDetailedCost(model, promptTokens, completionTokens, pricingPercentage, 'IDR');
        const costDetailsUSD = this.calculateDetailedCost(model, promptTokens, completionTokens, pricingPercentage, 'USD');
        
        // Deduct balance if user is authenticated
        let balanceUsage = null;
        if (userId) {
          try {
            balanceUsage = await BalanceService.useBalance(
              userId,
              costDetails.totalPrice,
              `Chat with ${expertContext?.name || 'AI Expert'}`,
              openaiThreadId,
              'chat_session'
            );
          } catch (balanceError) {
            console.error('Failed to deduct balance:', balanceError);
            return {
              success: false,
              error: 'Failed to process payment',
              details: balanceError.message
            };
          }
        }
        
        return {
          success: true,
          response: cleanedResponse,
          threadId: openaiThreadId,
          isNewThread: isNewThread,
          tokensUsed: totalTokens,
          cost: costDetails.totalPrice, // Return total cost in IDR for compatibility
          costBreakdown: {
            basePrice: costDetails.basePrice,
            platformCommission: costDetails.platformCommission,
            expertCommission: costDetails.expertCommission,
            totalPrice: costDetails.totalPrice,
            currency: 'IDR'
          },
          costBreakdownUSD: {
            basePrice: costDetailsUSD.basePrice,
            platformCommission: costDetailsUSD.platformCommission,
            expertCommission: costDetailsUSD.expertCommission,
            totalPrice: costDetailsUSD.totalPrice,
            currency: 'USD'
          },
          balanceUsage: balanceUsage ? {
            pointsUsed: balanceUsage.pointsUsed,
            creditsUsed: balanceUsage.creditsUsed,
            generatesCommission: balanceUsage.generatesCommission,
            breakdown: balanceUsage.breakdown
          } : null,
          pricingPercentage: pricingPercentage,
          status: 'success',
          usage: {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_tokens: totalTokens
          },
          model: model
        };
      } else {
        throw new Error(`Assistant run failed with status: ${runStatus.status}`);
      }
    } catch (error) {
      console.error('ChatService.processChat error:', error);
      return {
        success: false,
        error: error.message || 'Failed to process chat'
      };
    }
  }
}

module.exports = new ChatService();