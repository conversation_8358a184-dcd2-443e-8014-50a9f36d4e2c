// Simple logger utility
const logger = {
  info: (message, data) => {
    console.log(`[INFO] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  },
  error: (message, data) => {
    console.error(`[ERROR] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  },
  debug: (message, data) => {
    console.log(`[DEBUG] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  }
};

class SocketMonitor {
  constructor() {
    this.connections = new Map();
    this.stats = {
      totalConnections: 0,
      currentConnections: 0,
      totalMessages: 0,
      totalErrors: 0,
      startTime: Date.now()
    };
  }

  // Track new connection
  trackConnection(socket) {
    this.connections.set(socket.id, {
      socketId: socket.id,
      userId: socket.userId,
      userEmail: socket.userEmail,
      connectedAt: Date.now(),
      messagesCount: 0,
      errorsCount: 0,
      lastActivity: Date.now()
    });

    this.stats.totalConnections++;
    this.stats.currentConnections++;

    logger.info('Socket connection tracked', {
      socketId: socket.id,
      userId: socket.userId,
      totalConnections: this.stats.totalConnections,
      currentConnections: this.stats.currentConnections
    });
  }

  // Track disconnection
  trackDisconnection(socket) {
    const connection = this.connections.get(socket.id);
    if (connection) {
      const sessionDuration = Date.now() - connection.connectedAt;
      
      logger.info('Socket disconnection tracked', {
        socketId: socket.id,
        userId: socket.userId,
        sessionDuration,
        messagesCount: connection.messagesCount,
        errorsCount: connection.errorsCount
      });

      this.connections.delete(socket.id);
      this.stats.currentConnections--;
    }
  }

  // Track message activity
  trackMessage(socket, eventType) {
    const connection = this.connections.get(socket.id);
    if (connection) {
      connection.messagesCount++;
      connection.lastActivity = Date.now();
      this.stats.totalMessages++;
    }

    logger.debug('Socket message tracked', {
      socketId: socket.id,
      userId: socket.userId,
      eventType,
      totalMessages: this.stats.totalMessages
    });
  }

  // Track errors
  trackError(socket, error) {
    const connection = this.connections.get(socket.id);
    if (connection) {
      connection.errorsCount++;
      this.stats.totalErrors++;
    }

    logger.error('Socket error tracked', {
      socketId: socket.id,
      userId: socket.userId,
      error: error.message,
      totalErrors: this.stats.totalErrors
    });
  }

  // Get current statistics
  getStats() {
    const uptime = Date.now() - this.stats.startTime;
    return {
      ...this.stats,
      uptime,
      connections: Array.from(this.connections.values())
    };
  }

  // Get connection info for a specific user
  getUserConnections(userId) {
    const userConnections = [];
    for (const connection of this.connections.values()) {
      if (connection.userId === userId) {
        userConnections.push(connection);
      }
    }
    return userConnections;
  }

  // Check for inactive connections
  getInactiveConnections(timeoutMs = 300000) { // 5 minutes default
    const now = Date.now();
    const inactive = [];
    
    for (const connection of this.connections.values()) {
      if (now - connection.lastActivity > timeoutMs) {
        inactive.push(connection);
      }
    }
    
    return inactive;
  }

  // Log periodic statistics
  logPeriodicStats() {
    const stats = this.getStats();
    const inactiveConnections = this.getInactiveConnections();
    
    logger.info('Socket server statistics', {
      currentConnections: stats.currentConnections,
      totalConnections: stats.totalConnections,
      totalMessages: stats.totalMessages,
      totalErrors: stats.totalErrors,
      uptime: stats.uptime,
      inactiveConnections: inactiveConnections.length
    });
  }
}

// Create singleton instance
const socketMonitor = new SocketMonitor();

// Log stats every 5 minutes
setInterval(() => {
  socketMonitor.logPeriodicStats();
}, 5 * 60 * 1000);

module.exports = socketMonitor;