const express = require('express');
const chatController = require('../../controllers/chatController');

const router = express.Router();

// Health check endpoint
/**
 * @swagger
 * /health:
 *   get:
 *     summary: Health check
 *     tags: [System]
 *     responses:
 *       200:
 *         description: Server is running
 */
router.get('/health', chatController.healthCheck);

// Database test endpoint
/**
 * @swagger
 * /test-db:
 *   get:
 *     summary: Test database connection
 *     tags: [System]
 *     responses:
 *       200:
 *         description: Database connection successful
 *       500:
 *         description: Database connection failed
 */
router.get('/test-db', chatController.testDatabase);

module.exports = router;
