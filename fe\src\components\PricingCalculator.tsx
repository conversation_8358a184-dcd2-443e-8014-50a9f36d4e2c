"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { api } from "@/lib/api";

interface CostBreakdown {
  basePrice: number;
  platformCommission: number;
  expertCommission: number;
  totalPrice: number;
  breakdown?: any;
}

interface CostCalculation {
  model: string;
  inputTokens: number;
  outputTokens: number;
  pricingPercentage: number;
  costBreakdown: {
    IDR: CostBreakdown;
    USD: CostBreakdown;
  };
}

export default function PricingCalculator() {
  const [model, setModel] = useState('gpt-4o-mini');
  const [inputTokens] = useState(1000);
  const [outputTokens] = useState(500);
  const [pricingPercentage, setPricingPercentage] = useState(50);
  const [calculation, setCalculation] = useState<CostCalculation | null>(null);
  const [models, setModels] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    try {
      const result = await api.getAvailableModels();
      if (result.success) {
        setModels(result.models);
      }
    } catch (error) {
      console.error('Error loading models:', error);
    }
  };

  const calculateCost = async () => {
    try {
      setLoading(true);
      const data = await api.calculateCost(model, inputTokens, outputTokens, pricingPercentage);
      if (data.success) {
        setCalculation(data);
      }
    } catch (error) {
      console.error('Error calculating cost:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    if (currency === 'IDR') {
      return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    } else {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 6
      }).format(amount);
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">AI Pricing Calculator</h3>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-2">Model</label>
            <select 
              value={model} 
              onChange={(e) => setModel(e.target.value)}
              className="w-full p-2 border rounded-md"
            >
              {models.map((m) => (
                <option key={m.name} value={m.name}>
                  {m.displayName}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Expert Commission (%)</label>
            <Input
              type="number"
              value={pricingPercentage}
              onChange={(e) => setPricingPercentage(Number(e.target.value))}
              min="0"
              max="100"
              placeholder="Expert commission percentage"
            />
          </div>
        </div>

        <Button 
          onClick={calculateCost} 
          disabled={loading}
          className="w-full"
        >
          {loading ? 'Calculating...' : 'Calculate Cost'}
        </Button>
      </Card>

      {calculation && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* IDR Breakdown */}
          <Card className="p-6">
            <h4 className="text-lg font-semibold mb-4 text-orange-600">Cost in Rupiah (IDR)</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Expert Commission ({pricingPercentage}%):</span>
                <span className="font-medium text-green-600">{formatCurrency(calculation.costBreakdown.IDR.expertCommission, 'IDR')}</span>
              </div>
              <hr className="border-gray-200" />
              <div className="flex justify-between text-lg">
                <span className="font-semibold">Total Price:</span>
                <span className="font-bold text-red-600">{formatCurrency(calculation.costBreakdown.IDR.totalPrice, 'IDR')}</span>
              </div>
            </div>
          </Card>

          {/* USD Breakdown */}
          <Card className="p-6">
            <h4 className="text-lg font-semibold mb-4 text-green-600">Cost in US Dollar (USD)</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Expert Commission ({pricingPercentage}%):</span>
                <span className="font-medium text-green-600">{formatCurrency(calculation.costBreakdown.USD.expertCommission, 'USD')}</span>
              </div>
              <hr className="border-gray-200" />
              <div className="flex justify-between text-lg">
                <span className="font-semibold">Total Price:</span>
                <span className="font-bold text-red-600">{formatCurrency(calculation.costBreakdown.USD.totalPrice, 'USD')}</span>
              </div>
            </div>
          </Card>
        </div>
      )}

      {calculation && (
        <Card className="p-6 bg-blue-50">
          <h4 className="font-semibold mb-2">Pricing Formula</h4>
          <div className="text-sm text-gray-700 space-y-1">
            <p><strong>Total Price = Base Price + Platform Commission + Expert Commission</strong></p>
            <p>• Expert Commission: {pricingPercentage}% × Base Price</p>
            <p>• Exchange Rate: $1 = Rp 20,000</p>
          </div>
        </Card>
      )}
    </div>
  );
}
