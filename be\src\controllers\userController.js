const userService = require('../services/userService');

class UserController {
  // Register new user
  async register(req, res) {
    try {
      const { name, email, phone, password, referralCode, affiliateVisitorId } = req.body;

      // Validate required fields
      if (!name || !email || !phone || !password) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Name, email, phone, and password are required'
        });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          error: 'Invalid email format',
          message: 'Please provide a valid email address'
        });
      }

      // Validate phone format (basic validation)
      const phoneRegex = /^\+?[1-9]\d{1,14}$/;
      if (!phoneRegex.test(phone)) {
        return res.status(400).json({
          error: 'Invalid phone format',
          message: 'Please provide a valid phone number'
        });
      }

      // Validate password strength
      if (password.length < 6) {
        return res.status(400).json({
          error: 'Password too weak',
          message: 'Password must be at least 6 characters long'
        });
      }

      // Register user with optional affiliate visitor ID from frontend
      const user = await userService.registerUser({ name, email, phone, password, referralCode }, affiliateVisitorId || null);

      // Generate OTP
      const otpCode = await userService.generateAndStoreOTP(phone);

      // Generate WhatsApp link
      const whatsappLink = userService.generateWhatsAppLink(phone, otpCode);

      res.status(201).json({
        message: 'User registered successfully. Please verify your phone number.',
        user,
        verification: {
          whatsappLink,
          message: 'Click the WhatsApp link to send OTP to admin for verification'
        }
      });

    } catch (error) {
      console.error('Registration error:', error);
      res.status(400).json({
        error: 'Registration failed',
        message: error.message
      });
    }
  }

  // Verify OTP
  async verifyOTP(req, res) {
    try {
      const { phone, code } = req.body;

      if (!phone || !code) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Phone and OTP code are required'
        });
      }

      const result = await userService.verifyOTP(phone, code);

      res.json({
        message: 'Phone number verified successfully',
        user: result
      });

    } catch (error) {
      console.error('OTP verification error:', error);
      res.status(400).json({
        error: 'Verification failed',
        message: error.message
      });
    }
  }

  // Login user
  async login(req, res) {
    try {
      const { phone, password } = req.body;

      if (!phone || !password) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Phone and password are required'
        });
      }

      const result = await userService.loginUser(phone, password);

      res.json({
        message: 'Login successful',
        user: result
      });

    } catch (error) {
      console.error('Login error:', error);
      res.status(401).json({
        error: 'Login failed',
        message: error.message
      });
    }
  }

  // Get current user profile
  async getProfile(req, res) {
    try {
      // User info is already available from auth middleware
      res.json({
        user: {
          user_id: req.user.user_id,
          phone: req.user.phone,
          name: req.user.name,
          email: req.user.email
        }
      });

    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        error: 'Failed to get profile',
        message: error.message
      });
    }
  }

  // Resend OTP
  async resendOTP(req, res) {
    try {
      const { phone } = req.body;

      if (!phone) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Phone number is required'
        });
      }

      // Check if user exists
      const user = await userService.getUserByPhone(phone);
      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'No user found with this phone number'
        });
      }

      if (user.is_verified) {
        return res.status(400).json({
          error: 'Already verified',
          message: 'This phone number is already verified'
        });
      }

      // Generate new OTP
      const otpCode = await userService.generateAndStoreOTP(phone);

      // Generate WhatsApp link
      const whatsappLink = userService.generateWhatsAppLink(phone, otpCode);

      res.json({
        message: 'OTP sent successfully',
        verification: {
          whatsappLink,
          message: 'Click the WhatsApp link to send OTP to admin for verification'
        }
      });

    } catch (error) {
      console.error('Resend OTP error:', error);
      res.status(500).json({
        error: 'Failed to resend OTP',
        message: error.message
      });
    }
  }

  // Forgot password - Step 1: Request OTP
  async forgotPassword(req, res) {
    try {
      const { phone } = req.body;

      if (!phone) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Phone number is required'
        });
      }

      // Check if user exists
      const user = await userService.getUserByPhone(phone);
      if (!user) {
        return res.status(404).json({
          error: 'User not found',
          message: 'No user found with this phone number'
        });
      }

      // Generate OTP for password reset
      const otpCode = await userService.generateAndStoreOTP(phone);

      // Generate WhatsApp link
      const whatsappLink = userService.generateWhatsAppLink(phone, otpCode);

      res.json({
        message: 'Password reset OTP sent successfully',
        verification: {
          whatsappLink,
          message: 'Click the WhatsApp link to send OTP to admin for password reset verification'
        }
      });

    } catch (error) {
      console.error('Forgot password error:', error);
      res.status(500).json({
        error: 'Failed to process forgot password request',
        message: error.message
      });
    }
  }

  // Reset password - Step 2: Reset with OTP
  async resetPassword(req, res) {
    try {
      const { phone, code, newPassword } = req.body;

      if (!phone || !code || !newPassword) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Phone, OTP code, and new password are required'
        });
      }

      // Validate password strength
      if (newPassword.length < 6) {
        return res.status(400).json({
          error: 'Password too weak',
          message: 'Password must be at least 6 characters long'
        });
      }

      // Verify OTP first
      await userService.verifyOTP(phone, code);

      // Reset password
      await userService.resetPassword(phone, newPassword);

      res.json({
        message: 'Password reset successfully'
      });

    } catch (error) {
      console.error('Reset password error:', error);
      res.status(400).json({
        error: 'Failed to reset password',
        message: error.message
      });
    }
  }

  // Logout user
  async logout(req, res) {
    try {
      // Remove token from database
      await userService.getUserByToken(req.user.token);
      
      res.json({
        message: 'Logout successful'
      });

    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        error: 'Logout failed',
        message: error.message
      });
    }
  }

  // Update user profile
  async updateProfile(req, res) {
    try {
      const { name, email, bank_name, account_holder_name, account_number } = req.body;
      const userId = req.user.user_id;

      // Validate required fields
      if (!name || !email) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Name and email are required'
        });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          error: 'Invalid email format',
          message: 'Please provide a valid email address'
        });
      }

      // Update user profile
      const updatedUser = await userService.updateUserProfile(userId, {
        name,
        email,
        bank_name,
        account_holder_name,
        account_number
      });

      res.json({
        message: 'Profile updated successfully',
        user: updatedUser
      });

    } catch (error) {
      console.error('Update profile error:', error);
      
      if (error.message.includes('email already exists')) {
        return res.status(409).json({
          error: 'Email already exists',
          message: 'This email is already registered to another account'
        });
      }

      res.status(500).json({
        error: 'Update profile failed',
        message: error.message
      });
    }
  }

  // Change password
  async changePassword(req, res) {
    try {
      const { currentPassword, newPassword } = req.body;
      const userId = req.user.user_id;

      // Validate required fields
      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'Current password and new password are required'
        });
      }

      // Validate new password strength
      if (newPassword.length < 6) {
        return res.status(400).json({
          error: 'Password too weak',
          message: 'New password must be at least 6 characters long'
        });
      }

      // Change password
      await userService.changePassword(userId, currentPassword, newPassword);

      res.json({
        message: 'Password changed successfully'
      });

    } catch (error) {
      console.error('Change password error:', error);
      
      if (error.message.includes('Invalid current password')) {
        return res.status(400).json({
          error: 'Invalid current password',
          message: 'The current password you entered is incorrect'
        });
      }

      res.status(500).json({
        error: 'Change password failed',
        message: error.message
      });
    }
  }
}

module.exports = new UserController();
