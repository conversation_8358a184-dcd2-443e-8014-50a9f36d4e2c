const reviewService = require('../services/reviewService');

class ReviewController {
  // Create a new review
  async createReview(req, res) {
    try {
      const { expertId, rating, reviewText } = req.body;
      const userId = req.user?.user_id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!expertId || !rating) {
        return res.status(400).json({
          success: false,
          error: 'Expert ID and rating are required'
        });
      }

      // Validate review data
      const validationErrors = reviewService.validateReviewData({ rating, review_text: reviewText });
      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: validationErrors
        });
      }

      const result = await reviewService.createReview(userId, expertId, rating, reviewText);
      res.status(201).json(result);

    } catch (error) {
      console.error('Create review error:', error);
      
      if (error.message.includes('already reviewed') || 
          error.message.includes('at least 3 interactions') ||
          error.message.includes('not found')) {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  // Update an existing review
  async updateReview(req, res) {
    try {
      const { reviewId } = req.params;
      const { rating, reviewText } = req.body;
      const userId = req.user?.user_id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      if (!rating) {
        return res.status(400).json({
          success: false,
          error: 'Rating is required'
        });
      }

      // Validate review data
      const validationErrors = reviewService.validateReviewData({ rating, review_text: reviewText });
      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: validationErrors
        });
      }

      const result = await reviewService.updateReview(reviewId, userId, rating, reviewText);
      res.json(result);

    } catch (error) {
      console.error('Update review error:', error);
      
      if (error.message.includes('not found') || error.message.includes('permission')) {
        return res.status(404).json({
          success: false,
          error: error.message
        });
      }
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  // Get reviews for an expert
  async getExpertReviews(req, res) {
    try {
      const { expertId } = req.params;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const result = await reviewService.getExpertReviews(expertId, page, limit);
      res.json(result);

    } catch (error) {
      console.error('Get expert reviews error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get expert reviews',
        message: error.message
      });
    }
  }

  // Get user's reviews
  async getUserReviews(req, res) {
    try {
      const userId = req.user?.user_id;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const result = await reviewService.getUserReviews(userId, page, limit);
      res.json(result);

    } catch (error) {
      console.error('Get user reviews error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get user reviews',
        message: error.message
      });
    }
  }

  // Get review by ID
  async getReview(req, res) {
    try {
      const { reviewId } = req.params;

      const review = await reviewService.getReviewById(reviewId);
      
      if (!review) {
        return res.status(404).json({
          success: false,
          error: 'Review not found'
        });
      }

      res.json({
        success: true,
        review
      });

    } catch (error) {
      console.error('Get review error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get review',
        message: error.message
      });
    }
  }

  // Check if user can review an expert
  async canUserReview(req, res) {
    try {
      const { expertId } = req.params;
      const userId = req.user?.user_id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const result = await reviewService.canUserReview(userId, expertId);
      res.json({
        success: true,
        ...result
      });

    } catch (error) {
      console.error('Can user review error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to check review eligibility',
        message: error.message
      });
    }
  }

  // Get expert rating statistics
  async getExpertRatingStats(req, res) {
    try {
      const { expertId } = req.params;

      const result = await reviewService.getExpertRatingStats(expertId);
      res.json(result);

    } catch (error) {
      console.error('Get expert rating stats error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get rating statistics',
        message: error.message
      });
    }
  }

  // Admin functions
  async hideReview(req, res) {
    try {
      const { reviewId } = req.params;
      const adminUserId = req.user?.user_id;

      if (!adminUserId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      // TODO: Add admin role check here
      // if (!req.user.isAdmin) {
      //   return res.status(403).json({
      //     success: false,
      //     error: 'Admin access required'
      //   });
      // }

      const result = await reviewService.hideReview(reviewId, adminUserId);
      res.json(result);

    } catch (error) {
      console.error('Hide review error:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: error.message
        });
      }
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  async showReview(req, res) {
    try {
      const { reviewId } = req.params;
      const adminUserId = req.user?.user_id;

      if (!adminUserId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      // TODO: Add admin role check here

      const result = await reviewService.showReview(reviewId, adminUserId);
      res.json(result);

    } catch (error) {
      console.error('Show review error:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: error.message
        });
      }
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  async deleteReview(req, res) {
    try {
      const { reviewId } = req.params;
      const adminUserId = req.user?.user_id;

      if (!adminUserId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      // TODO: Add admin role check here

      const result = await reviewService.deleteReview(reviewId, adminUserId);
      res.json(result);

    } catch (error) {
      console.error('Delete review error:', error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          error: error.message
        });
      }
      
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  async getPendingReviews(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const adminUserId = req.user?.user_id;

      if (!adminUserId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      // TODO: Add admin role check here

      const result = await reviewService.getPendingReviews(page, limit);
      res.json(result);

    } catch (error) {
      console.error('Get pending reviews error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get pending reviews',
        message: error.message
      });
    }
  }
}

module.exports = new ReviewController();