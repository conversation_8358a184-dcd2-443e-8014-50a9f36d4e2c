// sharingController.js - Controller for expert sharing functionality
const SharingService = require('../services/sharingService');
const ConsentService = require('../services/consentService');
const AnalyticsService = require('../services/analyticsService');
const { v4: uuidv4 } = require('uuid');

/**
 * Async handler wrapper for error handling
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * Extract session info from request
 */
const getSessionInfo = (req) => {
  return {
    sessionId: req.sessionID || req.headers['x-session-id'] || uuidv4(),
    ipAddress: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    referer: req.get('Referer')
  };
};

class SharingController {
  /**
   * Create a new share for an expert
   * POST /api/sharing/create
   */
  createShare = asyncHandler(async (req, res) => {
    const { expertId, monitorEnabled = false } = req.body;
    const userId = req.user.user_id;

    // Validate input
    if (!expertId) {
      return res.status(400).json({
        success: false,
        message: 'Expert ID is required'
      });
    }

    try {
      const shareData = await SharingService.createShare(
        expertId,
        userId,
        monitorEnabled
      );

      res.status(201).json({
        success: true,
        message: 'Share created successfully',
        data: shareData
      });
    } catch (error) {
      console.error('Error creating share:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to create share'
      });
    }
  });

  /**
   * Get share information by token
   * GET /api/sharing/:shareToken
   */
  getShare = asyncHandler(async (req, res) => {
    const { shareToken } = req.params;
    const sessionInfo = getSessionInfo(req);
    const userId = req.user?.user_id || null;

    try {
      const shareData = await SharingService.getShareByToken(shareToken);
      
      if (!shareData) {
        return res.status(404).json({
          success: false,
          message: 'Share not found or expired'
        });
      }

      // Log analytics for share view
      await AnalyticsService.logAction(
        shareToken,
        userId,
        'view',
        { source: 'api' },
        sessionInfo.sessionId,
        sessionInfo.ipAddress,
        sessionInfo.userAgent,
        sessionInfo.referer
      );

      // Update click count
      await SharingService.trackConversion(shareToken, 'click');

      res.json({
        success: true,
        data: shareData
      });
    } catch (error) {
      console.error('Error getting share:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve share'
      });
    }
  });

  /**
   * Get all shares created by the authenticated user
   * GET /api/sharing/my-shares
   */
  getMyShares = asyncHandler(async (req, res) => {
    const userId = req.user.user_id;
    const { page = 1, limit = 10, status = 'all' } = req.query;

    try {
      const shares = await SharingService.getUserShares(
        userId,
        parseInt(page),
        parseInt(limit),
        status
      );

      res.json({
        success: true,
        data: shares
      });
    } catch (error) {
      console.error('Error getting user shares:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve shares'
      });
    }
  });

  /**
   * Update share settings
   * PUT /api/sharing/:shareToken
   */
  updateShare = asyncHandler(async (req, res) => {
    const { shareToken } = req.params;
    const { monitorEnabled, isActive } = req.body;
    const userId = req.user.user_id;

    try {
      const success = await SharingService.updateShare(
        shareToken,
        userId,
        { monitorEnabled, isActive }
      );

      if (!updatedShare) {
        return res.status(404).json({
          success: false,
          message: 'Share not found or access denied'
        });
      }

      res.json({
        success: true,
        message: 'Share updated successfully',
        data: updatedShare
      });
    } catch (error) {
      console.error('Error updating share:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to update share'
      });
    }
  });

  /**
   * Delete a share
   * DELETE /api/sharing/:shareToken
   */
  deleteShare = asyncHandler(async (req, res) => {
    const { shareToken } = req.params;
    const userId = req.user.user_id;

    try {
      const deleted = await SharingService.deleteShare(shareToken, userId);

      if (!deleted) {
        return res.status(404).json({
          success: false,
          message: 'Share not found or access denied'
        });
      }

      res.json({
        success: true,
        message: 'Share deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting share:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete share'
      });
    }
  });

  /**
   * Record user consent for accessing shared expert
   * POST /api/sharing/:shareToken/consent
   */
  recordConsent = asyncHandler(async (req, res) => {
    const { shareToken } = req.params;
    const { consentGiven, consentType = 'data_usage' } = req.body;
    const userId = req.user?.user_id;
    const sessionInfo = getSessionInfo(req);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required for consent'
      });
    }

    try {
      // Record consent
      const consentData = await ConsentService.recordConsent(
        shareToken,
        userId,
        consentGiven,
        consentType,
        sessionInfo.ipAddress,
        sessionInfo.userAgent
      );

      // Log analytics
      await AnalyticsService.logAction(
        shareToken,
        userId,
        'consent',
        { 
          consentGiven, 
          consentType,
          source: 'api'
        },
        sessionInfo.sessionId,
        sessionInfo.ipAddress,
        sessionInfo.userAgent,
        sessionInfo.referer
      );

      res.json({
        success: true,
        message: 'Consent recorded successfully',
        data: consentData
      });
    } catch (error) {
      console.error('Error recording consent:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to record consent'
      });
    }
  });

  /**
   * Get consent status for a user and share
   * GET /api/sharing/:shareToken/consent
   */
  getConsentStatus = asyncHandler(async (req, res) => {
    const { shareToken } = req.params;
    const userId = req.user?.user_id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    try {
      const consentStatus = await ConsentService.getConsentStatus(
        shareToken,
        userId
      );

      res.json({
        success: true,
        data: consentStatus
      });
    } catch (error) {
      console.error('Error getting consent status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve consent status'
      });
    }
  });

  /**
   * Start chat session with shared expert
   * POST /api/sharing/:shareToken/chat/start
   */
  startChatSession = asyncHandler(async (req, res) => {
    const { shareToken } = req.params;
    const userId = req.user?.user_id;
    const sessionInfo = getSessionInfo(req);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required to start chat'
      });
    }

    try {
      // Check if consent is required and given
      const shareData = await SharingService.getShareByToken(shareToken);
      if (!shareData) {
        return res.status(404).json({
          success: false,
          message: 'Share not found or expired'
        });
      }

      // Check consent if monitoring is enabled
      if (shareData.monitorEnabled) {
        const consentStatus = await ConsentService.getConsentStatus(shareToken, userId);
        if (!consentStatus.hasConsent) {
          return res.status(403).json({
            success: false,
            message: 'Consent required to access this expert',
            requiresConsent: true
          });
        }
      }

      // Log chat start
      await ConsentService.logChatAccess(
        shareToken,
        userId,
        shareData.monitorEnabled ? true : null
      );

      // Log analytics
      await AnalyticsService.logAction(
        shareToken,
        userId,
        'chat_start',
        { source: 'api' },
        sessionInfo.sessionId,
        sessionInfo.ipAddress,
        sessionInfo.userAgent,
        sessionInfo.referer
      );

      // Track conversion
      await SharingService.trackConversion(shareToken, 'conversion');

      res.json({
        success: true,
        message: 'Chat session started successfully',
        data: {
          shareToken,
          expertId: shareData.expertId,
          expertName: shareData.expertName,
          canChat: true
        }
      });
    } catch (error) {
      console.error('Error starting chat session:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to start chat session'
      });
    }
  });

  /**
   * Record chat activity
   * POST /api/sharing/:shareToken/chat/activity
   */
  recordChatActivity = asyncHandler(async (req, res) => {
    const { shareToken } = req.params;
    const { messageCount = 1 } = req.body;
    const userId = req.user?.user_id;
    const sessionInfo = getSessionInfo(req);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    try {
      // Update chat activity
      await ConsentService.updateChatActivity(shareToken, userId, messageCount);

      // Log analytics for chat message
      await AnalyticsService.logAction(
        shareToken,
        userId,
        'chat_message',
        { 
          messageCount,
          source: 'api'
        },
        sessionInfo.sessionId,
        sessionInfo.ipAddress,
        sessionInfo.userAgent,
        sessionInfo.referer
      );

      res.json({
        success: true,
        message: 'Chat activity recorded'
      });
    } catch (error) {
      console.error('Error recording chat activity:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to record chat activity'
      });
    }
  });

  /**
   * Get analytics for a specific share
   * GET /api/sharing/:shareToken/analytics
   */
  getShareAnalytics = asyncHandler(async (req, res) => {
    const { shareToken } = req.params;
    const { days = 30 } = req.query;
    const userId = req.user.user_id;

    try {
      const analytics = await AnalyticsService.getShareAnalytics(
        shareToken,
        userId,
        parseInt(days)
      );

      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Error getting share analytics:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to retrieve analytics'
      });
    }
  });

  /**
   * Get analytics for all user's shares
   * GET /api/sharing/analytics/overview
   */
  getAnalyticsOverview = asyncHandler(async (req, res) => {
    const { days = 30 } = req.query;
    const userId = req.user.user_id;

    try {
      const analytics = await AnalyticsService.getUserSharesAnalytics(
        userId,
        parseInt(days)
      );

      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Error getting analytics overview:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve analytics overview'
      });
    }
  });

  /**
   * Get real-time analytics
   * GET /api/sharing/analytics/realtime
   */
  getRealTimeAnalytics = asyncHandler(async (req, res) => {
    const { hours = 24 } = req.query;
    const userId = req.user.user_id;

    try {
      const analytics = await AnalyticsService.getRealTimeAnalytics(
        userId,
        parseInt(hours)
      );

      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      console.error('Error getting real-time analytics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve real-time analytics'
      });
    }
  });

  /**
   * Generate analytics report
   * POST /api/sharing/analytics/report
   */
  generateAnalyticsReport = asyncHandler(async (req, res) => {
    const { startDate, endDate } = req.body;
    const userId = req.user.user_id;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
    }

    try {
      const report = await AnalyticsService.generateReport(
        userId,
        startDate,
        endDate
      );

      res.json({
        success: true,
        data: report
      });
    } catch (error) {
      console.error('Error generating analytics report:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate analytics report'
      });
    }
  });

  /**
   * Get share URL for frontend
   * GET /api/sharing/:shareToken/url
   */
  getShareUrl = asyncHandler(async (req, res) => {
    const { shareToken } = req.params;
    const userId = req.user.user_id;

    try {
      // Verify ownership
      const shareData = await SharingService.getShareByToken(shareToken);
      if (!shareData || shareData.sharedByUserId !== userId) {
        return res.status(404).json({
          success: false,
          message: 'Share not found or access denied'
        });
      }

      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
      const shareUrl = `${baseUrl}/share/${shareToken}`;

      res.json({
        success: true,
        data: {
          shareToken,
          shareUrl,
          expertName: shareData.expertName,
          privacyLevel: shareData.privacyLevel,
          isActive: shareData.isActive
        }
      });
    } catch (error) {
      console.error('Error getting share URL:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get share URL'
      });
    }
  });
}

module.exports = new SharingController();