import ExpertProfile from "@/components/ExpertProfile";

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function ExpertProfilePage({ params }: PageProps) {
  const { id } = await params;
  return <ExpertProfile expertId={id} />;
}

export async function generateMetadata({ params }: PageProps) {
  const { id } = await params;

  return {
    title: `Expert Profile - AI Trainer Hub`,
    description: `Connect with AI Expert ${id} for specialized assistance`,
  };
}
