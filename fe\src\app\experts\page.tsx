'use client';

import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';
import ExpertPanel from '@/components/ExpertPanel';

function ExpertsContent() {
  const searchParams = useSearchParams();
  const view = searchParams.get('view') as 'overview' | 'manage' || 'overview';

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8 px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {view === 'overview' ? 'Expert Overview' : 'Manage AI Experts'}
          </h1>
          <p className="text-gray-600">
            {view === 'overview' 
              ? 'View comprehensive statistics and performance metrics for your AI experts'
              : 'Create and manage your AI experts'
            }
          </p>
        </div>
        
        <ExpertPanel view={view} />
      </div>
    </div>
  );
}

export default function ExpertsPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <ExpertsContent />
    </Suspense>
  );
}