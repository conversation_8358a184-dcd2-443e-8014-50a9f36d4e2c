const express = require('express');
const chatController = require('../../controllers/chatController');
const { authenticateToken } = require('../../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get user sessions
/**
 * @swagger
 * /api/chat/sessions:
 *   get:
 *     summary: Get user's chat sessions
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of chat sessions
 *       401:
 *         description: Unauthorized
 */
router.get('/sessions', chatController.getUserSessions);

// Get session messages
/**
 * @swagger
 * /api/chat/sessions/{sessionId}/messages:
 *   get:
 *     summary: Get messages from a specific session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         schema:
 *           type: string
 *         required: true
 *         description: Session ID
 *     responses:
 *       200:
 *         description: Session messages
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Session not found
 */
router.get('/sessions/:sessionId/messages', chatController.getSessionMessages);

// Get active session for expert
/**
 * @swagger
 * /api/chat/sessions/expert/{expertId}:
 *   get:
 *     summary: Get active session for a specific expert
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: expertId
 *         schema:
 *           type: string
 *         required: true
 *         description: Expert ID
 *     responses:
 *       200:
 *         description: Active session data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: No active session found
 */
router.get('/sessions/expert/:expertId', chatController.getActiveSessionForExpert);

// Get user stats
/**
 * @swagger
 * /api/chat/stats:
 *   get:
 *     summary: Get user's chat statistics
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User chat statistics
 *       401:
 *         description: Unauthorized
 */
router.get('/stats', chatController.getUserStats);

// Update session title
/**
 * @swagger
 * /api/chat/sessions/{sessionId}/title:
 *   put:
 *     summary: Update session title
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         schema:
 *           type: string
 *         required: true
 *         description: Session ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *     responses:
 *       200:
 *         description: Session title updated
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Session not found
 */
router.put('/sessions/:sessionId/title', chatController.updateSessionTitle);

// Delete session
/**
 * @swagger
 * /api/chat/sessions/{sessionId}:
 *   delete:
 *     summary: Delete a chat session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         schema:
 *           type: string
 *         required: true
 *         description: Session ID
 *     responses:
 *       200:
 *         description: Session deleted
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Session not found
 */
router.delete('/sessions/:sessionId', chatController.deleteSession);

module.exports = router;
