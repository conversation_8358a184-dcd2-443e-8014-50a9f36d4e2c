const { Server } = require('socket.io');
// Simple logger utility
const logger = {
  info: (message, data) => {
    console.log(`[INFO] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  },
  error: (message, data) => {
    console.error(`[ERROR] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  },
  debug: (message, data) => {
    console.log(`[DEBUG] ${message}`, data ? JSON.stringify(data, null, 2) : '');
  }
};
const chatSocketHandler = require('./chatSocket');
const socketMonitor = require('../utils/socketMonitor');

let io;

const initializeSocket = (server) => {
  io = new Server(server, {
    cors: {
      origin: process.env.FRONTEND_URL || "http://localhost:3000",
      methods: ["GET", "POST"],
      credentials: true
    },
    transports: ['websocket', 'polling']
  });

  // Socket authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      logger.info('Socket authentication attempt', {
        socketId: socket.id,
        hasToken: !!token,
        tokenLength: token?.length,
        tokenStart: token?.substring(0, 20) + '...'
      });
      
      if (!token) {
        logger.error('Socket authentication failed: No token provided');
        return next(new Error('Authentication token required'));
      }

      // Use database-based token verification (same as regular auth middleware)
      const { pool } = require('../config/database');
      const [rows] = await pool.execute(
        'SELECT user_id, phone, name, email FROM user WHERE token = ? AND is_verified = TRUE',
        [token]
      );

      if (rows.length === 0) {
        logger.error('Socket authentication failed: Invalid token or user not verified', {
          socketId: socket.id,
          token: token?.substring(0, 20) + '...'
        });
        return next(new Error('Invalid authentication token'));
      }

      const user = rows[0];
      socket.userId = user.user_id;
      socket.userEmail = user.email;
      socket.userName = user.name;
      socket.userPhone = user.phone;
      
      logger.info('Socket authenticated successfully', {
        socketId: socket.id,
        userId: socket.userId,
        userEmail: socket.userEmail,
        userName: socket.userName
      });
      
      next();
    } catch (error) {
      logger.error('Socket authentication failed', {
        socketId: socket.id,
        error: error.message,
        errorName: error.name,
        tokenProvided: !!socket.handshake.auth.token
      });
      
      next(new Error('Authentication failed'));
    }
  });

  // Connection handling
  io.on('connection', (socket) => {
    logger.info('User connected', {
      socketId: socket.id,
      userId: socket.userId,
      userEmail: socket.userEmail
    });

    // Track connection
    socketMonitor.trackConnection(socket);

    // Join user to their personal room
    socket.join(`user_${socket.userId}`);

    // Handle chat room joining
    socket.on('join_chat', (data) => {
      const { expertId, sessionId } = data;
      const chatRoom = `chat_${socket.userId}_${expertId}`;
      
      socket.join(chatRoom);
      socket.currentChatRoom = chatRoom;
      socket.currentExpertId = expertId;
      socket.currentSessionId = sessionId;
      
      logger.info('User joined chat room', {
        socketId: socket.id,
        userId: socket.userId,
        chatRoom,
        expertId,
        sessionId
      });
      
      socket.emit('chat_joined', { 
        room: chatRoom, 
        expertId, 
        sessionId 
      });
    });

    // Handle leaving chat room
    socket.on('leave_chat', () => {
      if (socket.currentChatRoom) {
        socket.leave(socket.currentChatRoom);
        
        logger.info('User left chat room', {
          socketId: socket.id,
          userId: socket.userId,
          chatRoom: socket.currentChatRoom
        });
        
        socket.currentChatRoom = null;
        socket.currentExpertId = null;
        socket.currentSessionId = null;
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info('User disconnected', {
        socketId: socket.id,
        userId: socket.userId,
        reason
      });
      
      // Track disconnection
      socketMonitor.trackDisconnection(socket);
    });

    // Handle connection errors
    socket.on('error', (error) => {
      logger.error('Socket error', {
        socketId: socket.id,
        userId: socket.userId,
        error: error.message
      });
      
      // Track error
      socketMonitor.trackError(socket, error);
    });
  });

  // Initialize chat socket handlers
  chatSocketHandler.initialize(io);

  return io;
};

const getIO = () => {
  if (!io) {
    throw new Error('Socket.IO not initialized');
  }
  return io;
};

module.exports = {
  initializeSocket,
  getIO
};