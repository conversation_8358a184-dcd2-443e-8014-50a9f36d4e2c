'use client';

import { useParams } from 'next/navigation';
import ShareAnalytics from '@/components/sharing/ShareAnalytics';

function ShareAnalyticsPageContent() {
  const params = useParams();
  const shareToken = params.shareToken as string;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">Share Analytics</h1>
            <p className="text-gray-600 mt-1">
              Detailed performance insights for your shared expert
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ShareAnalytics shareToken={shareToken} />
      </div>
    </div>
  );
}

export default function ShareAnalyticsPage() {
  return <ShareAnalyticsPageContent />;
}