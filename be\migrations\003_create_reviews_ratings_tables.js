// Migration: Create Reviews and Ratings Tables
// Created: 2025-08-14
// Description: Implement reviews table with rating validation (1-5 stars), foreign key constraints, indexes, and triggers

const mysql = require('mysql2/promise');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

async function getConnection() {
  return mysql.createConnection({
    host: process.env.DB_HOST || '127.0.0.1',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'aitrainerhub'
  });
}

async function up() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('Creating reviews table...');
    
    // Create reviews table
    const createReviewsTableSQL = `
      CREATE TABLE IF NOT EXISTS reviews (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        expert_id INT NOT NULL,
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        review_text TEXT,
        is_verified BOOLEAN DEFAULT FALSE,
        is_hidden BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE,
        FOREIGN KEY (expert_id) REFERENCES experts(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_expert_review (user_id, expert_id),
        INDEX idx_expert_reviews (expert_id, created_at DESC),
        INDEX idx_rating (rating),
        INDEX idx_user_reviews (user_id, created_at DESC),
        INDEX idx_verified_reviews (is_verified, is_hidden, created_at DESC)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.execute(createReviewsTableSQL);
    console.log('✅ Created reviews table with constraints and indexes');
    
    // Create trigger to update expert average ratings automatically
    console.log('Creating trigger to update expert ratings...');
    
    const createTriggerSQL = `
      CREATE TRIGGER update_expert_rating_after_review_insert
      AFTER INSERT ON reviews
      FOR EACH ROW
      BEGIN
        UPDATE experts 
        SET 
          average_rating = (
            SELECT ROUND(AVG(rating), 2) 
            FROM reviews 
            WHERE expert_id = NEW.expert_id 
            AND is_hidden = FALSE
          ),
          total_reviews = (
            SELECT COUNT(*) 
            FROM reviews 
            WHERE expert_id = NEW.expert_id 
            AND is_hidden = FALSE
          )
        WHERE id = NEW.expert_id;
      END
    `;
    
    await connection.query(createTriggerSQL);
    console.log('✅ Created trigger for review insert');
    
    // Create trigger for review updates
    const createUpdateTriggerSQL = `
      CREATE TRIGGER update_expert_rating_after_review_update
      AFTER UPDATE ON reviews
      FOR EACH ROW
      BEGIN
        UPDATE experts 
        SET 
          average_rating = (
            SELECT ROUND(AVG(rating), 2) 
            FROM reviews 
            WHERE expert_id = NEW.expert_id 
            AND is_hidden = FALSE
          ),
          total_reviews = (
            SELECT COUNT(*) 
            FROM reviews 
            WHERE expert_id = NEW.expert_id 
            AND is_hidden = FALSE
          )
        WHERE id = NEW.expert_id;
      END
    `;
    
    await connection.query(createUpdateTriggerSQL);
    console.log('✅ Created trigger for review update');
    
    // Create trigger for review deletions
    const createDeleteTriggerSQL = `
      CREATE TRIGGER update_expert_rating_after_review_delete
      AFTER DELETE ON reviews
      FOR EACH ROW
      BEGIN
        UPDATE experts 
        SET 
          average_rating = COALESCE((
            SELECT ROUND(AVG(rating), 2) 
            FROM reviews 
            WHERE expert_id = OLD.expert_id 
            AND is_hidden = FALSE
          ), 0),
          total_reviews = (
            SELECT COUNT(*) 
            FROM reviews 
            WHERE expert_id = OLD.expert_id 
            AND is_hidden = FALSE
          )
        WHERE id = OLD.expert_id;
      END
    `;
    
    await connection.query(createDeleteTriggerSQL);
    console.log('✅ Created trigger for review delete');
    
    await connection.commit();
    console.log('✅ Migration completed successfully');
    
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    await connection.end();
  }
}

async function down() {
  const connection = await getConnection();
  
  try {
    await connection.beginTransaction();
    
    console.log('Removing reviews and ratings system...');
    
    // Drop triggers first
    const dropTriggers = [
      'DROP TRIGGER IF EXISTS update_expert_rating_after_review_insert',
      'DROP TRIGGER IF EXISTS update_expert_rating_after_review_update',
      'DROP TRIGGER IF EXISTS update_expert_rating_after_review_delete'
    ];
    
    for (const sql of dropTriggers) {
      try {
        await connection.query(sql);
      } catch (error) {
        console.log(`Trigger drop failed (might not exist): ${error.message}`);
      }
    }
    console.log('✅ Dropped rating update triggers');
    
    // Drop reviews table
    await connection.execute('DROP TABLE IF EXISTS reviews');
    console.log('✅ Dropped reviews table');
    
    await connection.commit();
    console.log('✅ Rollback completed successfully');
    
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    await connection.end();
  }
}

// Run migration
if (require.main === module) {
  up().then(() => {
    console.log('Migration completed');
    process.exit(0);
  }).catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
  });
}

module.exports = { up, down };