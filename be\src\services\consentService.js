// ConsentService.js - Service for consent tracking and management
const mysql = require('mysql2/promise');
const dbConfig = require('../config/database');

class ConsentService {
  constructor() {
    this.pool = mysql.createPool(dbConfig);
  }

  /**
   * Record user consent for a shared expert
   * @param {number} userId - User giving consent
   * @param {string} shareToken - Share token
   * @param {boolean} consentGiven - Whether consent was given
   * @param {string} ipAddress - User's IP address
   * @param {string} userAgent - User's browser user agent
   * @returns {Promise<Object>} Consent record
   */
  async recordConsent(userId, shareToken, consentGiven, ipAddress = null, userAgent = null) {
    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // Get share information
      const [shareRows] = await connection.execute(
        'SELECT expert_id, shared_by_user_id FROM expert_shares WHERE share_token = ? AND is_active = TRUE',
        [shareToken]
      );
      
      if (shareRows.length === 0) {
        throw new Error('Share not found or inactive');
      }
      
      const { expert_id, shared_by_user_id } = shareRows[0];
      
      // Check if consent already exists
      const [existingConsent] = await connection.execute(
        'SELECT id, consent_given FROM share_consents WHERE user_id = ? AND share_token = ?',
        [userId, shareToken]
      );
      
      let consentId;
      
      if (existingConsent.length > 0) {
        // Update existing consent
        await connection.execute(
          `UPDATE share_consents 
           SET consent_given = ?, consent_date = NOW(), ip_address = ?, user_agent = ?, revoked_at = NULL
           WHERE user_id = ? AND share_token = ?`,
          [consentGiven, ipAddress, userAgent, userId, shareToken]
        );
        consentId = existingConsent[0].id;
      } else {
        // Create new consent record
        const [result] = await connection.execute(
          `INSERT INTO share_consents 
           (user_id, share_token, expert_id, shared_by_user_id, consent_given, ip_address, user_agent)
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [userId, shareToken, expert_id, shared_by_user_id, consentGiven, ipAddress, userAgent]
        );
        consentId = result.insertId;
      }
      
      // Log analytics event
      const analyticsService = require('./analyticsService');
      await analyticsService.logAction(shareToken, userId, 'consent', {
        consentGiven,
        ipAddress,
        userAgent
      });
      
      await connection.commit();
      
      return {
        consentId,
        userId,
        shareToken,
        expertId: expert_id,
        sharedByUserId: shared_by_user_id,
        consentGiven,
        consentDate: new Date(),
        ipAddress,
        userAgent
      };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Get user's consent status for a share
   * @param {number} userId - User ID
   * @param {string} shareToken - Share token
   * @returns {Promise<Object|null>} Consent data or null if not found
   */
  async getUserConsent(userId, shareToken) {
    const connection = await this.pool.getConnection();
    
    try {
      const [rows] = await connection.execute(
        `SELECT 
           sc.id,
           sc.consent_given,
           sc.consent_date,
           sc.revoked_at,
           sc.expert_id,
           sc.shared_by_user_id,
           e.name as expert_name,
           u.username as shared_by_username
         FROM share_consents sc
         JOIN experts e ON sc.expert_id = e.id
         JOIN user u ON sc.shared_by_user_id = u.user_id
         WHERE sc.user_id = ? AND sc.share_token = ?`,
        [userId, shareToken]
      );
      
      if (rows.length === 0) {
        return null;
      }
      
      const consent = rows[0];
      
      return {
        consentId: consent.id,
        consentGiven: consent.consent_given,
        consentDate: consent.consent_date,
        revokedAt: consent.revoked_at,
        isActive: !consent.revoked_at,
        expert: {
          id: consent.expert_id,
          name: consent.expert_name
        },
        sharedBy: {
          userId: consent.shared_by_user_id,
          username: consent.shared_by_username
        }
      };
      
    } finally {
      connection.release();
    }
  }

  /**
   * Check if user needs to give consent for a share
   * @param {number} userId - User ID
   * @param {string} shareToken - Share token
   * @returns {Promise<Object>} Consent requirement info
   */
  async checkConsentRequired(userId, shareToken) {
    const connection = await this.pool.getConnection();
    
    try {
      // Get share information
      const [shareRows] = await connection.execute(
        `SELECT 
           es.monitor_enabled,
           es.shared_by_user_id,
           e.name as expert_name
         FROM expert_shares es
         JOIN experts e ON es.expert_id = e.id
         WHERE es.share_token = ? AND es.is_active = TRUE`,
        [shareToken]
      );
      
      if (shareRows.length === 0) {
        throw new Error('Share not found or inactive');
      }
      
      const share = shareRows[0];
      
      // If monitoring is not enabled, no consent required
      if (!share.monitor_enabled) {
        return {
          consentRequired: false,
          reason: 'monitoring_disabled',
          expertName: share.expert_name
        };
      }
      
      // If user is the share creator, no consent required
      if (share.shared_by_user_id === userId) {
        return {
          consentRequired: false,
          reason: 'share_owner',
          expertName: share.expert_name
        };
      }
      
      // Check existing consent
      const existingConsent = await this.getUserConsent(userId, shareToken);
      
      if (existingConsent && existingConsent.isActive) {
        return {
          consentRequired: false,
          reason: 'consent_already_given',
          expertName: share.expert_name,
          consentGiven: existingConsent.consentGiven,
          consentDate: existingConsent.consentDate
        };
      }
      
      // Consent is required
      return {
        consentRequired: true,
        reason: 'monitoring_enabled',
        expertName: share.expert_name,
        monitoringEnabled: true
      };
      
    } finally {
      connection.release();
    }
  }

  /**
   * Revoke user consent
   * @param {number} userId - User ID
   * @param {string} shareToken - Share token
   * @returns {Promise<boolean>} Success status
   */
  async revokeConsent(userId, shareToken) {
    const connection = await this.pool.getConnection();
    
    try {
      const [result] = await connection.execute(
        'UPDATE share_consents SET revoked_at = NOW() WHERE user_id = ? AND share_token = ? AND revoked_at IS NULL',
        [userId, shareToken]
      );
      
      if (result.affectedRows > 0) {
        // Log analytics event
        const analyticsService = require('./analyticsService');
        await analyticsService.logAction(shareToken, userId, 'consent_revoked');
      }
      
      return result.affectedRows > 0;
      
    } finally {
      connection.release();
    }
  }

  /**
   * Get all consents for shares created by a user (for monitoring dashboard)
   * @param {number} sharedByUserId - User who created the shares
   * @param {number} limit - Limit results
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Array>} List of consents
   */
  async getShareConsents(sharedByUserId, limit = 20, offset = 0) {
    const connection = await this.pool.getConnection();
    
    try {
      const [rows] = await connection.execute(
        `SELECT 
           sc.id,
           sc.user_id,
           sc.share_token,
           sc.consent_given,
           sc.consent_date,
           sc.revoked_at,
           u.username,
           u.email,
           e.name as expert_name,
           es.monitor_enabled
         FROM share_consents sc
         JOIN user u ON sc.user_id = u.user_id
         JOIN experts e ON sc.expert_id = e.id
         JOIN expert_shares es ON sc.share_token = es.share_token
         WHERE sc.shared_by_user_id = ?
         ORDER BY sc.consent_date DESC
         LIMIT ? OFFSET ?`,
        [sharedByUserId, limit, offset]
      );
      
      return rows.map(row => ({
        consentId: row.id,
        shareToken: row.share_token,
        consentGiven: row.consent_given,
        consentDate: row.consent_date,
        revokedAt: row.revoked_at,
        isActive: !row.revoked_at,
        monitoringEnabled: row.monitor_enabled,
        user: {
          id: row.user_id,
          username: row.username,
          email: row.email
        },
        expert: {
          name: row.expert_name
        }
      }));
      
    } finally {
      connection.release();
    }
  }

  /**
   * Get consent statistics for a user's shares
   * @param {number} sharedByUserId - User who created the shares
   * @returns {Promise<Object>} Consent statistics
   */
  async getConsentStats(sharedByUserId) {
    const connection = await this.pool.getConnection();
    
    try {
      const [stats] = await connection.execute(
        `SELECT 
           COUNT(*) as total_consents,
           SUM(CASE WHEN consent_given = TRUE AND revoked_at IS NULL THEN 1 ELSE 0 END) as active_consents,
           SUM(CASE WHEN consent_given = FALSE THEN 1 ELSE 0 END) as declined_consents,
           SUM(CASE WHEN revoked_at IS NOT NULL THEN 1 ELSE 0 END) as revoked_consents
         FROM share_consents 
         WHERE shared_by_user_id = ?`,
        [sharedByUserId]
      );
      
      const result = stats[0];
      
      return {
        totalConsents: result.total_consents,
        activeConsents: result.active_consents,
        declinedConsents: result.declined_consents,
        revokedConsents: result.revoked_consents,
        consentRate: result.total_consents > 0 
          ? ((result.active_consents / result.total_consents) * 100).toFixed(2)
          : 0
      };
      
    } finally {
      connection.release();
    }
  }

  /**
   * Log chat access with consent status
   * @param {string} shareToken - Share token
   * @param {number} userId - User accessing chat
   * @param {number} chatSessionId - Chat session ID
   * @param {boolean} consentGiven - Whether consent was given
   * @returns {Promise<number>} Access log ID
   */
  async logChatAccess(shareToken, userId, chatSessionId, consentGiven = false) {
    const connection = await this.pool.getConnection();
    
    try {
      // Get share information
      const [shareRows] = await connection.execute(
        'SELECT expert_id, shared_by_user_id, monitor_enabled FROM expert_shares WHERE share_token = ?',
        [shareToken]
      );
      
      if (shareRows.length === 0) {
        throw new Error('Share not found');
      }
      
      const { expert_id, shared_by_user_id, monitor_enabled } = shareRows[0];
      
      // Create access log
      const [result] = await connection.execute(
        `INSERT INTO share_access_logs 
         (share_token, user_id, chat_session_id, expert_id, shared_by_user_id, monitoring_enabled, consent_given)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [shareToken, userId, chatSessionId, expert_id, shared_by_user_id, monitor_enabled, consentGiven]
      );
      
      return result.insertId;
      
    } finally {
      connection.release();
    }
  }

  /**
   * Update chat access activity
   * @param {number} accessLogId - Access log ID
   * @param {number} messageCount - Number of messages
   * @param {number} sessionDuration - Session duration in seconds
   * @returns {Promise<void>}
   */
  async updateChatActivity(accessLogId, messageCount, sessionDuration) {
    const connection = await this.pool.getConnection();
    
    try {
      await connection.execute(
        'UPDATE share_access_logs SET message_count = ?, session_duration = ? WHERE id = ?',
        [messageCount, sessionDuration, accessLogId]
      );
      
    } finally {
      connection.release();
    }
  }
}

module.exports = new ConsentService();