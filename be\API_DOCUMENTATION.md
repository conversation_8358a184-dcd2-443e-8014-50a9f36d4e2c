# API Documentation untuk AI Trainer Hub Backend

## Overview
Dokumentasi API lengkap untuk AI Trainer Hub Backend dengan Swagger UI telah berhasil dibuat dan diorganisir secara modular.

## Struktur Modular Routes
```
routes/
├── users/
│   ├── authRoutes.js      # Register, login, OTP, password reset
│   ├── profileRoutes.js   # Profile management, logout
│   └── index.js           # Gabungan semua user routes
├── experts/
│   ├── publicRoutes.js    # Public endpoints (models, pricing, public experts)
│   ├── managementRoutes.js# Expert CRUD operations
│   └── index.js           # Gabungan semua expert routes
├── chat/
│   ├── systemRoutes.js    # Health check, database test
│   ├── chatRoutes.js      # Chat functionality
│   ├── sessionRoutes.js   # Session management
│   └── index.js           # Gabungan semua chat routes
├── assistants/
│   └── index.js           # Assistant CRUD operations
├── affiliate/
│   ├── publicRoutes.js    # Public affiliate endpoints
│   ├── protectedRoutes.js # Protected affiliate endpoints
│   └── index.js           # Gabungan semua affiliate routes
└── balance/
    ├── userRoutes.js      # User balance operations
    ├── adminRoutes.js     # Admin balance operations
    └── index.js           # Gabungan semua balance routes
```

## Swagger Configuration
- **File**: `src/config/swagger.js`
- **Endpoint dokumentasi**: `/api-docs`
- **Security scheme**: Bearer Token (JWT) authentication
- **Scan path**: `./src/routes/**/*.js` (mendukung struktur modular)

## Features
1. **Modular organization** - Routes dipisah berdasarkan fitur/domain
2. **Complete JSDoc annotations** - Semua endpoint terdokumentasi
3. **Security integration** - Bearer token authentication
4. **Interactive testing** - Swagger UI untuk testing langsung

## Cara Akses
1. Jalankan server: `node server.js` atau `npm start`
2. Buka browser: `http://localhost:3001/api-docs`
3. Dokumentasi interaktif siap digunakan

## Status Modularisasi
✅ Users routes (auth + profile)
✅ Experts routes (public + management)  
✅ Chat routes (system + chat + sessions)
✅ Assistant routes (CRUD operations)
✅ Affiliate routes (public + protected)
✅ Balance routes (user + admin)

## API Endpoints Terdokumentasi
### Users (/api/users)
- Register, login, OTP verification
- Profile management, password change, logout

### Experts (/api/experts, /api/models)
- Model pricing and availability
- Expert CRUD operations and statistics
- Public expert marketplace

### Chat (/api/chat)
- Real-time chat with AI experts
- Session management and history
- Chat statistics

### Assistants (/api/assistants)
- AI assistant creation and management
- Assistant configuration and retrieval

### Affiliate (/api/affiliate)
- Referral code generation and tracking
- Commission and statistics management
- Public affiliate validation

**⚠️ IMPORTANT CHANGE**: Affiliate system tidak lagi menggunakan cookies di backend. Frontend harus mengelola visitor ID sendiri.

### Balance (/api/balance)
- User balance and transaction history
- Affordability checks and cost estimation
- Admin balance management operations

## API Changes - Affiliate System

### 🔄 Migration dari Cookie ke Request Body/Query Parameters

#### 1. User Registration (/api/users/register)
**SEBELUM** (Cookie-based):
```javascript
// Backend otomatis membaca dari cookies
const visitorId = req.cookies?.affiliate_visitor_id;
```

**SESUDAH** (Request body):
```javascript
POST /api/users/register
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>", 
  "phone": "+1234567890",
  "password": "password123",
  "referralCode": "ABC123",           // Optional
  "affiliateVisitorId": "visitor-uuid" // Optional - dari frontend
}
```

#### 2. Track Affiliate Visit (/api/affiliate/track/:referralCode)
**SEBELUM** (Cookie-based):
```javascript
// Backend set cookies otomatis
res.cookie('affiliate_visitor_id', visitorId, cookieOptions);
```

**SESUDAH** (Frontend managed):
```javascript
POST /api/affiliate/track/ABC123
Content-Type: application/json

{
  "visitorId": "existing-visitor-id" // Optional - jika sudah ada
}

// Response:
{
  "success": true,
  "visitorId": "visitor-uuid",
  "referralCode": "ABC123", 
  "expiresAt": "2025-08-08T00:00:00.000Z"
}
```

#### 3. Get Visitor Info (/api/affiliate/visitor-info)
**SEBELUM** (Cookie-based):
```javascript
// Backend otomatis baca dari cookies
const visitorId = req.cookies.affiliate_visitor_id;
```

**SESUDAH** (Query parameter atau Request body):
```javascript
// Option 1: Query parameter
GET /api/affiliate/visitor-info?visitorId=visitor-uuid

// Option 2: Request body
POST /api/affiliate/visitor-info
Content-Type: application/json

{
  "visitorId": "visitor-uuid"
}
```

### 💡 Frontend Implementation Guide

Frontend sekarang bertanggung jawab untuk:

1. **Menyimpan visitor ID** (localStorage, sessionStorage, atau cookie di client)
2. **Mengirim visitor ID** saat registrasi user
3. **Mengelola affiliate tracking** secara eksplisit

**Contoh implementasi frontend**:
```javascript
// 1. Track affiliate visit
const trackAffiliate = async (referralCode) => {
  const existingVisitorId = localStorage.getItem('affiliate_visitor_id');
  
  const response = await fetch(`/api/affiliate/track/${referralCode}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ 
      visitorId: existingVisitorId 
    })
  });
  
  const data = await response.json();
  if (data.success) {
    localStorage.setItem('affiliate_visitor_id', data.visitorId);
    localStorage.setItem('affiliate_referral_code', data.referralCode);
  }
};

// 2. Register dengan affiliate tracking
const registerUser = async (userData) => {
  const affiliateVisitorId = localStorage.getItem('affiliate_visitor_id');
  
  const response = await fetch('/api/users/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      ...userData,
      affiliateVisitorId // Optional field
    })
  });
};
```

### ✅ Benefits dari Perubahan Ini

1. **Cleaner Backend**: Backend tidak perlu cookie-parser dependency
2. **Explicit Data Flow**: Lebih jelas data flow affiliate tracking
3. **Frontend Control**: Frontend punya kontrol penuh atas affiliate data
4. **Better Testing**: Lebih mudah untuk testing tanpa cookie dependencies
5. **API Consistency**: Semua data melalui request body/query params

## Next Steps
- Tambahkan response schemas yang lebih detail
- Implementasi error response standardization  
- Tambahkan example requests dan responses
- Setup environment-specific server URLs

## 📝 Recent Updates

### ✅ Affiliate System Migration (Cookie → Request-based)
- **Updated**: `userController.register()` - affiliate data via request body
- **Updated**: `affiliateController.trackVisit()` - POST method dengan visitor ID management
- **Updated**: `affiliateController.getVisitorInfo()` - query parameter/request body support
- **Updated**: Swagger/JSDoc documentation untuk semua affected endpoints
- **Updated**: API documentation dengan migration guide dan frontend examples

### 🔄 Breaking Changes
1. **User Registration**: Sekarang menerima `affiliateVisitorId` di request body (optional)
2. **Affiliate Tracking**: Method berubah dari GET ke POST, visitor ID dikelola frontend
3. **Visitor Info**: Perlu explicit visitor ID di query param atau request body

### 🛠️ Required Frontend Changes  
Frontend perlu update untuk:
- Simpan visitor ID di localStorage/sessionStorage
- Kirim visitor ID saat user registration
- Gunakan POST method untuk affiliate tracking
- Include visitor ID saat query visitor info
