const affiliateService = require('../services/affiliateService');

class AffiliateController {
  
  /**
   * Track affiliate visit and return visitor ID for frontend to manage
   */
  async trackVisit(req, res) {
    try {
      const { referralCode } = req.params;
      const { visitorId: providedVisitorId } = req.body; // Frontend can send existing visitor ID
      
      let visitorId = providedVisitorId;
      if (!visitorId) {
        visitorId = affiliateService.generateVisitorId();
      }

      const visitorData = {
        visitorId: visitorId,
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.headers['user-agent'],
        referer: req.headers.referer,
        landingPage: req.headers.origin + req.originalUrl
      };

      const result = await affiliateService.trackAffiliateVisit(referralCode, visitorData);

      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: result.error
        });
      }

      // Return visitor ID for frontend to store (localStorage, cookie, etc.)
      res.json({
        success: true,
        message: result.message,
        visitorId: visitorId,
        referralCode: referralCode,
        expiresAt: result.expiresAt
      });
    } catch (error) {
      console.error('Track affiliate visit error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Get visitor tracking info
   */
  async getVisitorInfo(req, res) {
    try {
      // Get visitor ID from query parameter or request body
      const visitorId = req.query.visitorId || req.body.visitorId;
      
      if (!visitorId) {
        return res.json({
          success: true,
          hasTracking: false,
          message: 'No visitor ID provided'
        });
      }

      // Get visit info
      const [visits] = await pool.execute(
        'SELECT av.*, u.name as affiliate_name FROM affiliate_visits av LEFT JOIN user u ON av.affiliate_user_id = u.user_id WHERE av.visitor_id = ? AND av.expires_at > NOW() ORDER BY av.created_at DESC LIMIT 1',
        [visitorId]
      );

      if (visits.length === 0) {
        return res.json({
          success: true,
          hasTracking: false,
          message: 'No active affiliate tracking found'
        });
      }

      const visit = visits[0];
      res.json({
        success: true,
        hasTracking: true,
        referralCode: visit.referral_code,
        affiliateName: visit.affiliate_name,
        expiresAt: visit.expires_at,
        daysRemaining: Math.ceil((new Date(visit.expires_at) - new Date()) / (1000 * 60 * 60 * 24))
      });
    } catch (error) {
      console.error('Get visitor info error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
  
  /**
   * Generate referral code for authenticated user
   */
  async generateReferralCode(req, res) {
    try {
      const userId = req.user.userId;
      
      const result = await affiliateService.generateReferralCode(userId);
      
      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: result.error
        });
      }

      res.json({
        success: true,
        referralCode: result.referralCode,
        message: 'Referral code generated successfully'
      });
    } catch (error) {
      console.error('Generate referral code error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Get affiliate stats for authenticated user
   */
  async getAffiliateStats(req, res) {
    try {
      const userId = req.user.userId;
      
      const result = await affiliateService.getAffiliateStats(userId);
      
      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: result.error
        });
      }

      res.json({
        success: true,
        stats: result.stats
      });
    } catch (error) {
      console.error('Get affiliate stats error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Get referral stats for authenticated user
   */
  async getReferralStats(req, res) {
    try {
      const userId = req.user.userId;
      
      const result = await affiliateService.getReferralStats(userId);
      
      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: result.error
        });
      }

      res.json({
        success: true,
        referrals: result.referrals
      });
    } catch (error) {
      console.error('Get referral stats error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Get commission history for authenticated user
   */
  async getCommissionHistory(req, res) {
    try {
      const userId = req.user.userId;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 50;
      
      const result = await affiliateService.getCommissionHistory(userId, page, limit);
      
      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: result.error
        });
      }

      res.json({
        success: true,
        commissions: result.commissions,
        pagination: result.pagination
      });
    } catch (error) {
      console.error('Get commission history error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Validate referral code
   */
  async validateReferralCode(req, res) {
    try {
      const { referralCode } = req.params;
      
      const result = await affiliateService.getUserByReferralCode(referralCode);
      
      if (!result.success) {
        return res.status(404).json({
          success: false,
          error: result.error
        });
      }

      res.json({
        success: true,
        valid: true,
        referrer: {
          name: result.user.name,
          referralCode: result.user.referral_code
        }
      });
    } catch (error) {
      console.error('Validate referral code error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Set referral for authenticated user
   */
  async setReferral(req, res) {
    try {
      const userId = req.user.userId;
      const { referralCode } = req.body;

      if (!referralCode) {
        return res.status(400).json({
          success: false,
          error: 'Referral code is required'
        });
      }
      
      const result = await affiliateService.setReferral(userId, referralCode);
      
      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: result.error
        });
      }

      res.json({
        success: true,
        message: result.message,
        referrerId: result.referrerId
      });
    } catch (error) {
      console.error('Set referral error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Get top affiliates (public endpoint)
   */
  async getTopAffiliates(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 10;
      
      const result = await affiliateService.getTopAffiliates(limit);
      
      if (!result.success) {
        return res.status(400).json({
          success: false,
          error: result.error
        });
      }

      res.json({
        success: true,
        affiliates: result.affiliates
      });
    } catch (error) {
      console.error('Get top affiliates error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Get affiliate dashboard data
   */
  async getDashboard(req, res) {
    try {
      const userId = req.user.userId;
      
      // Get affiliate stats
      const statsResult = await affiliateService.getAffiliateStats(userId);
      if (!statsResult.success) {
        return res.status(400).json({
          success: false,
          error: statsResult.error
        });
      }

      // Get referral stats
      const referralsResult = await affiliateService.getReferralStats(userId);
      if (!referralsResult.success) {
        return res.status(400).json({
          success: false,
          error: referralsResult.error
        });
      }

      // Get recent commissions
      const commissionsResult = await affiliateService.getCommissionHistory(userId, 1, 10);
      if (!commissionsResult.success) {
        return res.status(400).json({
          success: false,
          error: commissionsResult.error
        });
      }

      res.json({
        success: true,
        dashboard: {
          stats: statsResult.stats,
          referrals: referralsResult.referrals,
          recentCommissions: commissionsResult.commissions
        }
      });
    } catch (error) {
      console.error('Get affiliate dashboard error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}

module.exports = new AffiliateController();
