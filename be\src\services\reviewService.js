const { pool } = require('../config/database');

class ReviewService {
  // Create a new review
  async createReview(userId, expertId, rating, reviewText = null) {
    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // Check if user has already reviewed this expert
      const [existingReview] = await connection.execute(
        'SELECT id FROM reviews WHERE user_id = ? AND expert_id = ?',
        [userId, expertId]
      );
      
      if (existingReview.length > 0) {
        throw new Error('You have already reviewed this expert');
      }
      
      // Check if expert exists
      const [expert] = await connection.execute(
        'SELECT id FROM experts WHERE id = ?',
        [expertId]
      );
      
      if (expert.length === 0) {
        throw new Error('Expert not found');
      }
      
      // Check if user has interacted with this expert (at least 3 messages)
      const [interactions] = await connection.execute(`
        SELECT COUNT(*) as message_count
        FROM chat_messages cm
        JOIN chat_sessions cs ON cm.session_id = cs.id
        WHERE cs.user_id = ? AND cs.expert_id = ? AND cm.role = 'user'
      `, [userId, expertId]);
      
      if (interactions[0].message_count < 3) {
        throw new Error('You must have at least 3 interactions with this expert before reviewing');
      }
      
      // Create the review
      const [result] = await connection.execute(`
        INSERT INTO reviews (user_id, expert_id, rating, review_text, is_verified)
        VALUES (?, ?, ?, ?, ?)
      `, [userId, expertId, rating, reviewText, true]); // Auto-verify for now
      
      await connection.commit();
      
      // Get the created review with user info
      const review = await this.getReviewById(result.insertId);
      
      return {
        success: true,
        message: 'Review created successfully',
        review
      };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
  
  // Update an existing review
  async updateReview(reviewId, userId, rating, reviewText = null) {
    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // Check if review exists and belongs to user
      const [existingReview] = await connection.execute(
        'SELECT id, expert_id FROM reviews WHERE id = ? AND user_id = ?',
        [reviewId, userId]
      );
      
      if (existingReview.length === 0) {
        throw new Error('Review not found or you do not have permission to update it');
      }
      
      // Update the review
      await connection.execute(`
        UPDATE reviews 
        SET rating = ?, review_text = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [rating, reviewText, reviewId]);
      
      await connection.commit();
      
      // Get the updated review
      const review = await this.getReviewById(reviewId);
      
      return {
        success: true,
        message: 'Review updated successfully',
        review
      };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
  
  // Get reviews for an expert with pagination
  async getExpertReviews(expertId, page = 1, limit = 10, includeHidden = false) {
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE r.expert_id = ?';
    let params = [expertId];
    
    if (!includeHidden) {
      whereClause += ' AND r.is_hidden = FALSE';
    }
    
    const [reviews] = await pool.execute(`
      SELECT 
        r.id,
        r.user_id,
        r.rating,
        r.review_text,
        r.is_verified,
        r.is_hidden,
        r.created_at,
        r.updated_at,
        u.name as reviewer_name
      FROM reviews r
      JOIN user u ON r.user_id = u.user_id
      ${whereClause}
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, limit, offset]);
    
    // Get total count
    const [countResult] = await pool.execute(`
      SELECT COUNT(*) as total
      FROM reviews r
      ${whereClause}
    `, params);
    
    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);
    
    return {
      success: true,
      reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    };
  }
  
  // Get user's reviews with pagination
  async getUserReviews(userId, page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    
    const [reviews] = await pool.execute(`
      SELECT 
        r.id,
        r.expert_id,
        r.rating,
        r.review_text,
        r.is_verified,
        r.is_hidden,
        r.created_at,
        r.updated_at,
        e.name as expert_name,
        e.image_url as expert_image
      FROM reviews r
      JOIN experts e ON r.expert_id = e.id
      WHERE r.user_id = ?
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `, [userId, limit, offset]);
    
    // Get total count
    const [countResult] = await pool.execute(
      'SELECT COUNT(*) as total FROM reviews WHERE user_id = ?',
      [userId]
    );
    
    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);
    
    return {
      success: true,
      reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    };
  }
  
  // Get review by ID
  async getReviewById(reviewId) {
    const [reviews] = await pool.execute(`
      SELECT 
        r.id,
        r.user_id,
        r.expert_id,
        r.rating,
        r.review_text,
        r.is_verified,
        r.is_hidden,
        r.created_at,
        r.updated_at,
        u.name as reviewer_name,
        e.name as expert_name
      FROM reviews r
      JOIN user u ON r.user_id = u.user_id
      JOIN experts e ON r.expert_id = e.id
      WHERE r.id = ?
    `, [reviewId]);
    
    return reviews[0] || null;
  }
  
  // Check if user can review an expert
  async canUserReview(userId, expertId) {
    // Check if user has already reviewed
    const [existingReview] = await pool.execute(
      'SELECT id FROM reviews WHERE user_id = ? AND expert_id = ?',
      [userId, expertId]
    );
    
    if (existingReview.length > 0) {
      return {
        canReview: false,
        reason: 'already_reviewed',
        message: 'You have already reviewed this expert'
      };
    }
    
    // Check interaction count
    const [interactions] = await pool.execute(`
      SELECT COUNT(*) as message_count
      FROM chat_messages cm
      JOIN chat_sessions cs ON cm.session_id = cs.id
      WHERE cs.user_id = ? AND cs.expert_id = ? AND cm.role = 'user'
    `, [userId, expertId]);
    
    if (interactions[0].message_count < 3) {
      return {
        canReview: false,
        reason: 'insufficient_interaction',
        message: 'You must have at least 3 interactions with this expert before reviewing'
      };
    }
    
    return {
      canReview: true,
      message: 'You can review this expert'
    };
  }
  
  // Get expert rating statistics
  async getExpertRatingStats(expertId) {
    const [stats] = await pool.execute(`
      SELECT 
        COUNT(*) as total_reviews,
        AVG(rating) as average_rating,
        SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as five_star,
        SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as four_star,
        SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as three_star,
        SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as two_star,
        SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as one_star
      FROM reviews 
      WHERE expert_id = ? AND is_hidden = FALSE
    `, [expertId]);
    
    const result = stats[0];
    
    return {
      success: true,
      stats: {
        total_reviews: result.total_reviews,
        average_rating: parseFloat(result.average_rating || 0).toFixed(2),
        rating_distribution: {
          5: result.five_star,
          4: result.four_star,
          3: result.three_star,
          2: result.two_star,
          1: result.one_star
        }
      }
    };
  }
  
  // Admin functions
  async hideReview(reviewId, adminUserId) {
    const [result] = await pool.execute(
      'UPDATE reviews SET is_hidden = TRUE WHERE id = ?',
      [reviewId]
    );
    
    if (result.affectedRows === 0) {
      throw new Error('Review not found');
    }
    
    return {
      success: true,
      message: 'Review hidden successfully'
    };
  }
  
  async showReview(reviewId, adminUserId) {
    const [result] = await pool.execute(
      'UPDATE reviews SET is_hidden = FALSE WHERE id = ?',
      [reviewId]
    );
    
    if (result.affectedRows === 0) {
      throw new Error('Review not found');
    }
    
    return {
      success: true,
      message: 'Review shown successfully'
    };
  }
  
  async deleteReview(reviewId, adminUserId) {
    const [result] = await pool.execute(
      'DELETE FROM reviews WHERE id = ?',
      [reviewId]
    );
    
    if (result.affectedRows === 0) {
      throw new Error('Review not found');
    }
    
    return {
      success: true,
      message: 'Review deleted successfully'
    };
  }
  
  // Get reviews pending moderation
  async getPendingReviews(page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const [reviews] = await pool.execute(`
      SELECT 
        r.id,
        r.user_id,
        r.expert_id,
        r.rating,
        r.review_text,
        r.is_verified,
        r.created_at,
        u.name as reviewer_name,
        e.name as expert_name
      FROM reviews r
      JOIN user u ON r.user_id = u.user_id
      JOIN experts e ON r.expert_id = e.id
      WHERE r.is_verified = FALSE
      ORDER BY r.created_at ASC
      LIMIT ? OFFSET ?
    `, [limit, offset]);
    
    const [countResult] = await pool.execute(
      'SELECT COUNT(*) as total FROM reviews WHERE is_verified = FALSE'
    );
    
    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);
    
    return {
      success: true,
      reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    };
  }
  
  // Validate review data
  validateReviewData(data) {
    const errors = [];
    
    if (!data.rating || data.rating < 1 || data.rating > 5) {
      errors.push('Rating must be between 1 and 5');
    }
    
    if (data.review_text && data.review_text.length > 1000) {
      errors.push('Review text must be less than 1000 characters');
    }
    
    return errors;
  }
}

module.exports = new ReviewService();