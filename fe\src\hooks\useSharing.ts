'use client';

import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { api } from '@/lib/api';

export interface ShareData {
  shareToken: string;
  monitorEnabled: boolean;
  isActive: boolean;
  shareUrl: string;
}

export interface CreateShareParams {
  expertId: number;
  monitorEnabled?: boolean;
}

export interface SharedExpertData {
  expert: {
    id: number;
    name: string;
    description: string;
    imageUrl?: string;
    category?: string;
    tags?: string[];
    createdBy: {
      id: number;
      username: string;
      displayName?: string;
    };
  };
  share: {
    shareToken: string;
    monitorEnabled: boolean;
    isActive: boolean;
    clickCount: number;
    createdAt: string;
  };
}

export interface ConsentData {
  hasConsented: boolean;
  consentTimestamp?: string;
  trackingEnabled: boolean;
}

export function useSharing() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create a new share
  const createShare = useCallback(async (params: CreateShareParams): Promise<ShareData | null> => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.post('/sharing/shares', {
        body: params,
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        const shareData = response.data.data;
        const shareUrl = `${window.location.origin}/shared/${shareData.shareToken}`;
        
        toast({
          title: "Share Created!",
          description: "Your expert share link has been created successfully."
        });

        return {
          ...shareData,
          shareUrl
        };
      } else {
        throw new Error(response.data.message || 'Failed to create share');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to create share';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Get shared expert data
  const getSharedExpert = useCallback(async (shareToken: string): Promise<SharedExpertData | null> => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.get(`/sharing/shared/${shareToken}`);

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to load shared expert');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to load shared expert';
      setError(errorMessage);
      
      // Don't show toast for 404 errors (invalid share tokens)
      if (err.response?.status !== 404) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive"
        });
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Track share click
  const trackClick = useCallback(async (shareToken: string, metadata?: Record<string, any>): Promise<boolean> => {
    try {
      const response = await api.post(`/sharing/track/${shareToken}/click`, {
        body: {
          metadata: {
            userAgent: navigator.userAgent,
            referrer: document.referrer,
            timestamp: new Date().toISOString(),
            ...metadata
          }
        }
      });

      return response.data.success;
    } catch (err: any) {
      console.error('Failed to track click:', err);
      // Don't show error toast for tracking failures
      return false;
    }
  }, []);

  // Track conversion (when user starts chat)
  const trackConversion = useCallback(async (shareToken: string, metadata?: Record<string, any>): Promise<boolean> => {
    try {
      const response = await api.post(`/sharing/track/${shareToken}/conversion`, {
        body: {
          metadata: {
            timestamp: new Date().toISOString(),
            ...metadata
          }
        },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      return response.data.success;
    } catch (err: any) {
      console.error('Failed to track conversion:', err);
      // Don't show error toast for tracking failures
      return false;
    }
  }, []);

  // Get user consent status
  const getConsent = useCallback(async (shareToken: string): Promise<ConsentData | null> => {
    try {
      const response = await api.get(`/sharing/consent/${shareToken}`);

      if (response.data.success) {
        return response.data.data;
      }
      return null;
    } catch (err: any) {
      console.error('Failed to get consent:', err);
      return null;
    }
  }, []);

  // Set user consent
  const setConsent = useCallback(async (shareToken: string, consent: boolean, trackingEnabled: boolean = true): Promise<boolean> => {
    try {
      const response = await api.post(`/sharing/consent/${shareToken}`, {
        body: {
          consent,
          trackingEnabled
        }
      });

      if (response.data.success) {
        toast({
          title: consent ? "Consent Granted" : "Consent Withdrawn",
          description: consent 
            ? "Thank you for allowing us to improve your experience."
            : "Your privacy preferences have been updated."
        });
        return true;
      }
      return false;
    } catch (err: any) {
      console.error('Failed to set consent:', err);
      toast({
        title: "Error",
        description: "Failed to update consent preferences.",
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  // Update share settings
  const updateShare = useCallback(async (shareToken: string, updates: Partial<CreateShareParams>): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.put(`/sharing/shares/${shareToken}`, {
        body: updates,
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        toast({
          title: "Share Updated",
          description: "Your share settings have been updated successfully."
        });
        return true;
      } else {
        throw new Error(response.data.message || 'Failed to update share');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to update share';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Delete share
  const deleteShare = useCallback(async (shareToken: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.delete(`/sharing/shares/${shareToken}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        toast({
          title: "Share Deleted",
          description: "The share link has been deactivated successfully."
        });
        return true;
      } else {
        throw new Error(response.data.message || 'Failed to delete share');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to delete share';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Get user's shares
  const getUserShares = useCallback(async (): Promise<any[] | null> => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.get('/sharing/shares/my', {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to load shares');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to load shares';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Get share analytics
  const getAnalytics = useCallback(async (shareToken: string, timeRange: string = '7d'): Promise<any | null> => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.get(`/sharing/analytics/${shareToken}?timeRange=${timeRange}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to load analytics');
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to load analytics';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Copy share URL to clipboard
  const copyShareUrl = useCallback(async (shareToken: string): Promise<boolean> => {
    try {
      const shareUrl = `${window.location.origin}/shared/${shareToken}`;
      await navigator.clipboard.writeText(shareUrl);
      
      toast({
        title: "Copied!",
        description: "Share link copied to clipboard."
      });
      
      return true;
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
      toast({
        title: "Error",
        description: "Failed to copy link. Please copy manually.",
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  // Generate share URL
  const generateShareUrl = useCallback((shareToken: string): string => {
    return `${window.location.origin}/shared/${shareToken}`;
  }, []);

  // Clear error state
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    loading,
    error,
    
    // Actions
    createShare,
    getSharedExpert,
    trackClick,
    trackConversion,
    getConsent,
    setConsent,
    updateShare,
    deleteShare,
    getUserShares,
    getAnalytics,
    copyShareUrl,
    generateShareUrl,
    clearError
  };
}

// Helper hook for managing share state in components
export function useShareState(initialShareToken?: string) {
  const [shareToken, setShareToken] = useState<string | null>(initialShareToken || null);
  const [shareData, setShareData] = useState<SharedExpertData | null>(null);
  const [consentData, setConsentData] = useState<ConsentData | null>(null);
  const sharing = useSharing();

  const loadSharedExpert = useCallback(async (token: string) => {
    setShareToken(token);
    const data = await sharing.getSharedExpert(token);
    setShareData(data);
    return data;
  }, [sharing]);

  const loadConsent = useCallback(async (token: string) => {
    const consent = await sharing.getConsent(token);
    setConsentData(consent);
    return consent;
  }, [sharing]);

  const handleConsent = useCallback(async (token: string, consent: boolean, trackingEnabled: boolean = true) => {
    const success = await sharing.setConsent(token, consent, trackingEnabled);
    if (success) {
      setConsentData({
        hasConsented: consent,
        consentTimestamp: new Date().toISOString(),
        trackingEnabled
      });
    }
    return success;
  }, [sharing]);

  return {
    shareToken,
    shareData,
    consentData,
    loadSharedExpert,
    loadConsent,
    handleConsent,
    ...sharing
  };
}