'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  BarChart3, 
  Users, 
  MousePointer, 
  MessageCircle, 
  Calendar,
  Globe,
  Smartphone,
  Monitor,
  ArrowLeft,
  Download,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { api } from '@/lib/api';

import { useRouter } from 'next/navigation';

interface ShareAnalyticsData {
  share: {
    id: number;
    shareToken: string;
    shareType: 'public' | 'private';
    monitorEnabled: boolean;
    isActive: boolean;
  clickCount: number;
  conversionCount: number;
  createdAt: string;
  expert: {
      id: number;
      name: string;
      description: string;
      imageUrl?: string;
    };
  };
  analytics: {
    totalClicks: number;
    totalConversions: number;
    conversionRate: number;
    uniqueVisitors: number;
    returningVisitors: number;
    avgSessionDuration: number;
    topReferrers: Array<{
      source: string;
      clicks: number;
      percentage: number;
    }>;
    deviceBreakdown: {
      desktop: number;
      mobile: number;
      tablet: number;
    };
    geographicData: Array<{
      country: string;
      clicks: number;
      conversions: number;
    }>;
    timeSeriesData: Array<{
      date: string;
      clicks: number;
      conversions: number;
    }>;
    hourlyData: Array<{
      hour: number;
      clicks: number;
      conversions: number;
    }>;
  };
}

interface ShareAnalyticsProps {
  shareToken: string;
}

export default function ShareAnalytics({ shareToken }: ShareAnalyticsProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [data, setData] = useState<ShareAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [refreshing, setRefreshing] = useState(false);

  const loadAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get(`/sharing/analytics/${shareToken}?timeRange=${timeRange}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setData(response.data.data);
      }
    } catch (err: any) {
      console.error('Error loading analytics:', err);
      toast({
        title: "Error",
        description: "Failed to load analytics data. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [shareToken, timeRange, toast]);

  useEffect(() => {
    loadAnalytics();
  }, [shareToken, timeRange, loadAnalytics]);

  const refreshAnalytics = async () => {
    setRefreshing(true);
    await loadAnalytics();
    setRefreshing(false);
    toast({
      title: "Analytics Refreshed",
      description: "Analytics data has been updated with the latest information."
    });
  };

  const exportData = async () => {
    try {
      const response = await api.get(`/sharing/analytics/${shareToken}/export?timeRange=${timeRange}&format=csv`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `share-analytics-${shareToken}-${timeRange}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Export Complete",
        description: "Analytics data has been exported successfully."
      });
    } catch (err: any) {
      console.error('Error exporting data:', err);
      toast({
        title: "Export Failed",
        description: "Failed to export analytics data. Please try again.",
        variant: "destructive"
      });
    }
  };

  const getDeviceIcon = (device: string) => {
    switch (device) {
      case 'desktop': return <Monitor className="h-4 w-4" />;
      case 'mobile': return <Smartphone className="h-4 w-4" />;
      case 'tablet': return <Smartphone className="h-4 w-4" />;
      default: return <Monitor className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Analytics Data</h3>
        <p className="text-gray-600">Analytics data is not available for this share.</p>
        <Button onClick={() => router.back()} className="mt-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
      </div>
    );
  }

  const { share, analytics } = data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{share.expert.name}</h1>
            <p className="text-gray-600">Share Analytics</p>
          </div>
        </div>
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={refreshAnalytics} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Share Info */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div>
                <h3 className="font-semibold text-gray-900">{share.expert.name}</h3>
                <p className="text-sm text-gray-600">{share.expert.description}</p>
              </div>
              <div className="flex space-x-2">
                <Badge variant={share.isActive ? "default" : "secondary"}>
                  {share.isActive ? 'Active' : 'Inactive'}
                </Badge>
                <Badge variant="outline" className="capitalize">
                  {share.shareType}
                </Badge>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Created</p>
              <p className="font-medium">{new Date(share.createdAt).toLocaleDateString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 text-blue-600 mb-2">
              <MousePointer className="h-5 w-5" />
              <span className="font-medium">Total Clicks</span>
            </div>
            <p className="text-3xl font-bold text-blue-900">{analytics.totalClicks.toLocaleString()}</p>
            <p className="text-sm text-gray-600 mt-1">
              {analytics.uniqueVisitors} unique visitors
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 text-green-600 mb-2">
              <MessageCircle className="h-5 w-5" />
              <span className="font-medium">Conversions</span>
            </div>
            <p className="text-3xl font-bold text-green-900">{analytics.totalConversions.toLocaleString()}</p>
            <p className="text-sm text-gray-600 mt-1">
              {analytics.conversionRate.toFixed(1)}% conversion rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 text-purple-600 mb-2">
              <Users className="h-5 w-5" />
              <span className="font-medium">Returning Visitors</span>
            </div>
            <p className="text-3xl font-bold text-purple-900">{analytics.returningVisitors.toLocaleString()}</p>
            <p className="text-sm text-gray-600 mt-1">
              {((analytics.returningVisitors / analytics.uniqueVisitors) * 100).toFixed(1)}% return rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 text-orange-600 mb-2">
              <Calendar className="h-5 w-5" />
              <span className="font-medium">Avg. Session</span>
            </div>
            <p className="text-3xl font-bold text-orange-900">
              {Math.round(analytics.avgSessionDuration / 60)}m
            </p>
            <p className="text-sm text-gray-600 mt-1">
              {analytics.avgSessionDuration}s average
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="traffic" className="space-y-6">
        <TabsList>
          <TabsTrigger value="traffic">Traffic Sources</TabsTrigger>
          <TabsTrigger value="devices">Devices</TabsTrigger>
          <TabsTrigger value="geography">Geography</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="traffic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Referrers</CardTitle>
              <CardDescription>
                Sources that drive the most traffic to your shared expert
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.topReferrers.map((referrer, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Globe className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium">{referrer.source || 'Direct'}</p>
                        <p className="text-sm text-gray-600">{referrer.percentage.toFixed(1)}% of traffic</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">{referrer.clicks}</p>
                      <p className="text-sm text-gray-600">clicks</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="devices" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Device Breakdown</CardTitle>
              <CardDescription>
                How users access your shared expert across different devices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(analytics.deviceBreakdown).map(([device, count]) => {
                  const percentage = (count / analytics.totalClicks) * 100;
                  return (
                    <div key={device} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getDeviceIcon(device)}
                        <div>
                          <p className="font-medium capitalize">{device}</p>
                          <p className="text-sm text-gray-600">{percentage.toFixed(1)}% of traffic</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{count}</p>
                        <p className="text-sm text-gray-600">clicks</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="geography" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Geographic Distribution</CardTitle>
              <CardDescription>
                Where your shared expert is being accessed from around the world
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.geographicData.slice(0, 10).map((geo, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Globe className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium">{geo.country}</p>
                        <p className="text-sm text-gray-600">
                          {((geo.clicks / analytics.totalClicks) * 100).toFixed(1)}% of traffic
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">{geo.clicks} clicks</p>
                      <p className="text-sm text-gray-600">{geo.conversions} conversions</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performance Over Time</CardTitle>
              <CardDescription>
                Track how your shared expert performs across different time periods
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Daily Timeline */}
                <div>
                  <h4 className="font-medium mb-4">Daily Performance</h4>
                  <div className="space-y-2">
                    {analytics.timeSeriesData.slice(-7).map((day, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <p className="font-medium">{new Date(day.date).toLocaleDateString()}</p>
                        </div>
                        <div className="flex space-x-6 text-sm">
                          <div className="text-center">
                            <p className="font-bold text-blue-600">{day.clicks}</p>
                            <p className="text-gray-600">clicks</p>
                          </div>
                          <div className="text-center">
                            <p className="font-bold text-green-600">{day.conversions}</p>
                            <p className="text-gray-600">conversions</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Hourly Breakdown */}
                <div>
                  <h4 className="font-medium mb-4">Peak Hours</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {analytics.hourlyData
                      .sort((a, b) => b.clicks - a.clicks)
                      .slice(0, 8)
                      .map((hour, index) => (
                        <div key={index} className="text-center p-3 border rounded">
                          <p className="font-bold">{hour.hour}:00</p>
                          <p className="text-sm text-blue-600">{hour.clicks} clicks</p>
                          <p className="text-sm text-green-600">{hour.conversions} chats</p>
                        </div>
                      ))
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}