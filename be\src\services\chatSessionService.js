const { pool } = require('../config/database');
const CurrencyUtils = require('../utils/currencyUtils');
const affiliateService = require('./affiliateService');

// Database shorthand alias
const db = pool;

class ChatSessionService {
    // Create or get existing chat session
    static async createOrGetSession(userId, expertId = null, threadId = null) {
        try {
            // If threadId is provided, try to get existing session
            if (threadId) {
                const [existingSessions] = await db.execute(
                    'SELECT * FROM chat_sessions WHERE thread_id = ? AND user_id = ?',
                    [threadId, userId]
                );
                
                if (existingSessions.length > 0) {
                    const existingSession = existingSessions[0];
                    console.log('🔍 DEBUG - Found existing session:', existingSession.id);
                    
                    // Get expert context for existing session if expert_id exists
                    let expertContext = null;
                    if (existingSession.expert_id) {
                        console.log('🔍 DEBUG - Getting expert context for existing session, expert_id:', existingSession.expert_id);
                        const [experts] = await db.execute(
                            'SELECT name, model, assistant_id, pricing_percentage FROM experts WHERE id = ?',
                            [existingSession.expert_id]
                        );
                        
                        if (experts.length > 0) {
                            expertContext = {
                                assistantId: experts[0].assistant_id,
                                name: experts[0].name,
                                model: experts[0].model,
                                pricingPercentage: parseFloat(experts[0].pricing_percentage) || 0,
                                expertId: existingSession.expert_id
                            };
                            console.log('🔍 DEBUG - Existing session expertContext:', expertContext);
                        }
                    }
                    
                    return {
                        success: true,
                        session: existingSession,
                        isNew: false,
                        expertContext: expertContext
                    };
                }
            }

            // Get expert info if expertId provided
            let expertName = null;
            let expertModel = null;
            let assistantId = null;
            let pricingPercentage = 0;
            if (expertId) {
                console.log('🔍 DEBUG - Querying expert with ID:', expertId);
                const [experts] = await db.execute(
                    'SELECT name, model, assistant_id, pricing_percentage FROM experts WHERE id = ?',
                    [expertId]
                );
                console.log('🔍 DEBUG - Expert query result:', experts);
                if (experts.length > 0) {
                    expertName = experts[0].name;
                    expertModel = experts[0].model;
                    assistantId = experts[0].assistant_id;
                    pricingPercentage = parseFloat(experts[0].pricing_percentage) || 0;
                    console.log('🔍 DEBUG - Extracted values:', {
                        expertName,
                        expertModel,
                        assistantId,
                        pricingPercentage
                    });
                    console.log('🔍 DEBUG - Raw assistant_id from DB:', experts[0].assistant_id);
                    console.log('🔍 DEBUG - Type of assistant_id:', typeof experts[0].assistant_id);
                } else {
                    console.log('❌ DEBUG - No expert found with ID:', expertId);
                }
            }

            // We'll let the chatService create the OpenAI thread ID
            // For now, we'll use a placeholder that will be updated after first message
            const tempThreadId = threadId || `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // Create new session
            const [result] = await db.execute(
                `INSERT INTO chat_sessions (user_id, thread_id, expert_id, expert_name, expert_model, session_title) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [
                    userId, 
                    tempThreadId, 
                    expertId, 
                    expertName, 
                    expertModel,
                    expertName ? `Chat with ${expertName}` : 'General Chat'
                ]
            );

            // Update user stats
            await this.updateUserStats(userId);

            const [newSession] = await db.execute(
                'SELECT * FROM chat_sessions WHERE id = ?',
                [result.insertId]
            );

            const expertContext = expertId ? {
                assistantId,
                name: expertName,
                model: expertModel,
                pricingPercentage: pricingPercentage,
                expertId: expertId
            } : null;

            console.log('🔍 DEBUG - Created expertContext:', expertContext);

            return {
                success: true,
                session: newSession[0],
                isNew: true,
                expertContext: expertContext
            };

        } catch (error) {
            console.error('Error creating chat session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Save chat message with point/credit tracking
    static async saveMessage(sessionId, threadId, role, content, tokensUsed = 0, cost = 0, costDetails = null, balanceUsage = null) {
        try {
            // Ensure cost is in IDR (round to nearest Rupiah)
            const costIDR = CurrencyUtils.roundIDR(cost);
            
            // Get message order
            const [orderResult] = await db.execute(
                'SELECT COALESCE(MAX(message_order), 0) + 1 as next_order FROM chat_messages WHERE session_id = ?',
                [sessionId]
            );
            const messageOrder = orderResult[0].next_order;

            // Prepare cost breakdown data
            let baseCost = costIDR;
            let markupCost = 0;
            let inputTokens = 0;
            let outputTokens = 0;
            let pointsUsed = 0;
            let creditsUsed = 0;
            let generatesCommission = false;

            if (costDetails) {
                baseCost = CurrencyUtils.roundIDR(costDetails.basePrice || 0);
                markupCost = CurrencyUtils.roundIDR(costDetails.expertCommission || 0);
                inputTokens = costDetails.inputTokens || 0;
                outputTokens = costDetails.outputTokens || 0;
            }

            if (balanceUsage) {
                pointsUsed = CurrencyUtils.roundIDR(balanceUsage.pointsUsed || 0);
                creditsUsed = CurrencyUtils.roundIDR(balanceUsage.creditsUsed || 0);
                generatesCommission = balanceUsage.generatesCommission || false;
            }

            // Insert message with detailed cost and balance tracking
            const [result] = await db.execute(
                `INSERT INTO chat_messages (
                    session_id, thread_id, role, content, message_order, tokens_used, cost, 
                    input_tokens, output_tokens, base_cost, markup_cost, 
                    points_used, credits_used, generates_commission, commission_processed
                 ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    sessionId, threadId, role, content, messageOrder, tokensUsed, costIDR, 
                    inputTokens, outputTokens, baseCost, markupCost,
                    pointsUsed, creditsUsed, generatesCommission, 0
                ]
            );

            const messageId = result.insertId;

            // Process affiliate commission only if credits were used (generates commission)
            if (role === 'assistant' && costIDR > 0 && generatesCommission) {
                await this.processAffiliateCommission(sessionId, messageId, baseCost, markupCost, tokensUsed, inputTokens, outputTokens);
            }

            // Update session timestamp
            await db.execute(
                'UPDATE chat_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [sessionId]
            );

            // Update user stats
            const [session] = await db.execute(
                'SELECT user_id FROM chat_sessions WHERE id = ?',
                [sessionId]
            );
            
            if (session.length > 0) {
                await this.updateUserStats(session[0].user_id);
            }

            return {
                success: true,
                messageId: messageId
            };

        } catch (error) {
            console.error('Error saving message:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Process affiliate commission for a chat message
    static async processAffiliateCommission(sessionId, messageId, baseCostIDR, markupCostIDR, tokensUsed, inputTokens = 0, outputTokens = 0) {
        try {
            // Get session details
            const [sessionRows] = await db.execute(
                'SELECT user_id, expert_id FROM chat_sessions WHERE id = ?',
                [sessionId]
            );

            if (sessionRows.length === 0) {
                return;
            }

            const { user_id: userId, expert_id: expertId } = sessionRows[0];

            // Convert IDR to USD for commission calculation
            const baseCostUSD = baseCostIDR / 20000; // Assuming 1 USD = 20,000 IDR
            const markupCostUSD = markupCostIDR / 20000;

            // Record commission
            const commissionResult = await affiliateService.recordCommission({
                userId: userId,
                expertId: expertId,
                sessionId: sessionId,
                messageId: messageId,
                baseCostUSD: baseCostUSD,
                markupCostUSD: markupCostUSD,
                tokensUsed: tokensUsed,
                inputTokens: inputTokens,
                outputTokens: outputTokens
            });

            // Mark commission as processed
            if (commissionResult.success) {
                await db.execute(
                    'UPDATE chat_messages SET commission_processed = 1 WHERE id = ?',
                    [messageId]
                );
            }

        } catch (error) {
            console.error('Error processing affiliate commission:', error);
            // Don't throw error to prevent chat from failing
        }
    }


   

    // Get chat history by thread ID (for backward compatibility)
    static async getChatHistory(threadId, userId, limit = 50) {
        try {
            console.log('getChatHistory called with:', { threadId, userId, limit });
            
            // Ensure limit is a valid number and convert to integer
            const validLimit = Math.max(1, Math.min(parseInt(limit) || 50, 100));
            
            // First, find the session for this thread and user
            const [sessions] = await db.execute(
                'SELECT id FROM chat_sessions WHERE thread_id = ? AND user_id = ? AND is_active = TRUE',
                [threadId, userId]
            );
            
            if (sessions.length === 0) {
                return {
                    success: false,
                    error: 'Session not found for this thread'
                };
            }
            
            const sessionId = sessions[0].id;
            
            // Get messages for this session
            const [messages] = await db.execute(
                `SELECT cm.*, cs.expert_name 
                 FROM chat_messages cm
                 JOIN chat_sessions cs ON cm.session_id = cs.id
                 WHERE cm.session_id = ?
                 ORDER BY cm.message_order ASC
                 LIMIT ${validLimit}`,
                [sessionId]
            );

            console.log('Found messages for thread:', messages.length);

            return {
                success: true,
                messages: messages
            };

        } catch (error) {
            console.error('Error getting chat history by thread:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get chat history by session ID (more efficient)
    static async getChatHistoryBySessionId(sessionId, userId, limit = 50) {
        try {
            console.log('getChatHistoryBySessionId called with:', { sessionId, userId, limit });
            
            // Ensure limit is a valid number and convert to integer
            const validLimit = Math.max(1, Math.min(parseInt(limit) || 50, 100));
            
            // Verify user owns this session
            const [sessions] = await db.execute(
                'SELECT id FROM chat_sessions WHERE id = ? AND user_id = ?',
                [sessionId, userId]
            );
            
            if (sessions.length === 0) {
                return {
                    success: false,
                    error: 'Session not found or access denied'
                };
            }
            
            // Get messages for this session
            // Use string interpolation for LIMIT to avoid MySQL parameter binding issues
            const [messages] = await db.execute(
                `SELECT cm.*, cs.expert_name 
                 FROM chat_messages cm
                 JOIN chat_sessions cs ON cm.session_id = cs.id
                 WHERE cm.session_id = ?
                 ORDER BY cm.message_order ASC
                 LIMIT ${validLimit}`,
                [sessionId]
            );

            console.log('Found messages:', messages.length);

            return {
                success: true,
                messages: messages
            };

        } catch (error) {
            console.error('Error getting chat history by session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get user's chat sessions
    static async getUserSessions(userId, limit = 20) {
        try {
            console.log('ChatSessionService.getUserSessions called with:', { userId, limit });
            
            // Ensure limit is a valid number and convert to integer
            const validLimit = Math.max(1, Math.min(parseInt(limit) || 20, 100));
            
            // First, try simple query without complex joins
            // Use string interpolation for LIMIT to avoid MySQL parameter binding issues
            const [sessions] = await db.execute(
                `SELECT 
                    id,
                    user_id,
                    thread_id,
                    expert_id,
                    expert_name,
                    expert_model,
                    session_title,
                    created_at,
                    updated_at,
                    is_active
                 FROM chat_sessions 
                 WHERE user_id = ? AND is_active = TRUE
                 ORDER BY updated_at DESC
                 LIMIT ${validLimit}`,
                [userId]
            );

            console.log('Found basic sessions:', sessions.length);

            // Then get message counts for each session
            const enrichedSessions = [];
            for (const session of sessions) {
                try {
                    const [messageStats] = await db.execute(
                        `SELECT 
                            COUNT(*) as message_count,
                            MAX(created_at) as last_message_at,
                            SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as user_messages,
                            SUM(CASE WHEN role = 'assistant' THEN 1 ELSE 0 END) as assistant_messages,
                            COALESCE(SUM(tokens_used), 0) as total_tokens,
                            COALESCE(SUM(cost), 0) as total_cost
                         FROM chat_messages 
                         WHERE session_id = ?`,
                        [session.id]
                    );

                    enrichedSessions.push({
                        ...session,
                        message_count: messageStats[0].message_count || 0,
                        last_message_at: messageStats[0].last_message_at,
                        user_messages: messageStats[0].user_messages || 0,
                        assistant_messages: messageStats[0].assistant_messages || 0,
                        total_tokens: messageStats[0].total_tokens || 0,
                        total_cost: parseFloat(messageStats[0].total_cost || 0) // Already in IDR
                    });
                } catch (msgError) {
                    console.error('Error getting message stats for session', session.id, ':', msgError);
                    // Include session even if message stats fail
                    enrichedSessions.push({
                        ...session,
                        message_count: 0,
                        last_message_at: null,
                        user_messages: 0,
                        assistant_messages: 0,
                        total_tokens: 0,
                        total_cost: 0
                    });
                }
            }

            console.log('Enriched sessions:', enrichedSessions.length);

            return {
                success: true,
                sessions: enrichedSessions
            };

        } catch (error) {
            console.error('Error getting user sessions:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get user statistics with proper currency formatting
    static async getUserStats(userId) {
        try {
            const [stats] = await db.execute(
                `SELECT 
                    total_sessions,
                    total_messages,
                    total_tokens_used,
                    total_cost as total_cost_idr,
                    last_chat_at,
                    created_at,
                    updated_at
                 FROM user_chat_stats 
                 WHERE user_id = ?`,
                [userId]
            );

            if (stats.length === 0) {
                return {
                    success: true,
                    stats: {
                        total_sessions: 0,
                        total_messages: 0,
                        total_tokens_used: 0,
                        total_cost_idr: 0,
                        total_cost_usd: 0,
                        total_cost_formatted_idr: CurrencyUtils.formatIDR(0),
                        total_cost_formatted_usd: CurrencyUtils.formatUSD(0),
                        last_chat_at: null,
                        created_at: null,
                        updated_at: null
                    }
                };
            }

            const userStats = stats[0];
            const costIDR = parseFloat(userStats.total_cost_idr || 0);
            const costUSD = CurrencyUtils.convertIDRToUSD(costIDR);

            return {
                success: true,
                stats: {
                    total_sessions: userStats.total_sessions || 0,
                    total_messages: userStats.total_messages || 0,
                    total_tokens_used: userStats.total_tokens_used || 0,
                    total_cost_idr: costIDR,
                    total_cost_usd: CurrencyUtils.roundUSD(costUSD),
                    total_cost_formatted_idr: CurrencyUtils.formatIDR(costIDR),
                    total_cost_formatted_usd: CurrencyUtils.formatUSD(costUSD),
                    last_chat_at: userStats.last_chat_at,
                    created_at: userStats.created_at,
                    updated_at: userStats.updated_at
                }
            };

        } catch (error) {
            console.error('Error getting user stats:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Update session title
    static async updateSessionTitle(sessionId, userId, title) {
        try {
            await db.execute(
                'UPDATE chat_sessions SET session_title = ? WHERE id = ? AND user_id = ?',
                [title, sessionId, userId]
            );

            return {
                success: true
            };

        } catch (error) {
            console.error('Error updating session title:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Delete chat session
    static async deleteSession(sessionId, userId) {
        try {
            await db.execute(
                'UPDATE chat_sessions SET is_active = FALSE WHERE id = ? AND user_id = ?',
                [sessionId, userId]
            );

            return {
                success: true
            };

        } catch (error) {
            console.error('Error deleting session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Update user statistics
    static async updateUserStats(userId) {
        try {
            await db.execute(
                `INSERT INTO user_chat_stats (
                    user_id, 
                    total_sessions, 
                    total_messages, 
                    total_tokens_used, 
                    total_cost, 
                    last_chat_at
                ) 
                SELECT 
                    ? as user_id,
                    COUNT(DISTINCT cs.id) as total_sessions,
                    COUNT(cm.id) as total_messages,
                    COALESCE(SUM(cm.tokens_used), 0) as total_tokens_used,
                    COALESCE(SUM(cm.cost), 0) as total_cost,
                    MAX(cm.created_at) as last_chat_at
                FROM chat_sessions cs
                LEFT JOIN chat_messages cm ON cs.id = cm.session_id
                WHERE cs.user_id = ? AND cs.is_active = TRUE
                ON DUPLICATE KEY UPDATE
                    total_sessions = VALUES(total_sessions),
                    total_messages = VALUES(total_messages),
                    total_tokens_used = VALUES(total_tokens_used),
                    total_cost = VALUES(total_cost),
                    last_chat_at = VALUES(last_chat_at),
                    updated_at = CURRENT_TIMESTAMP`,
                [userId, userId]
            );

            return {
                success: true
            };

        } catch (error) {
            console.error('Error updating user stats:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Update thread ID after OpenAI thread creation
    static async updateThreadId(sessionId, newThreadId) {
        try {
            await db.execute(
                'UPDATE chat_sessions SET thread_id = ? WHERE id = ?',
                [newThreadId, sessionId]
            );

            return {
                success: true
            };

        } catch (error) {
            console.error('Error updating thread ID:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get session by thread ID
    static async getSessionByThreadId(threadId, userId) {
        try {
            const [sessions] = await db.execute(
                'SELECT * FROM chat_sessions WHERE thread_id = ? AND user_id = ?',
                [threadId, userId]
            );

            return {
                success: true,
                session: sessions.length > 0 ? sessions[0] : null
            };

        } catch (error) {
            console.error('Error getting session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get session by ID
    static async getSessionById(sessionId, userId) {
        try {
            const [sessions] = await db.execute(
                'SELECT * FROM chat_sessions WHERE id = ? AND user_id = ?',
                [sessionId, userId]
            );

            return {
                success: true,
                session: sessions.length > 0 ? sessions[0] : null
            };

        } catch (error) {
            console.error('Error getting session by ID:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get most recent active session for a specific expert and user
    static async getActiveSessionForExpert(userId, expertId) {
        try {
            const [sessions] = await db.execute(
                `SELECT * FROM chat_sessions 
                 WHERE user_id = ? AND expert_id = ? AND is_active = TRUE
                 ORDER BY updated_at DESC 
                 LIMIT 1`,
                [userId, expertId]
            );

            return {
                success: true,
                session: sessions.length > 0 ? sessions[0] : null
            };

        } catch (error) {
            console.error('Error getting active session for expert:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = ChatSessionService;
