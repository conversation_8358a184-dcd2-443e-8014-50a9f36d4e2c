# AI Trainer Hub Backend - System Prompt

## Project Overview

**AI Trainer Hub Backend** adalah sistem backend untuk platform pelatihan AI yang memungkinkan pengguna berinteraksi dengan AI experts, mengelola balance (points/credits), sistem affiliate, dan assistant management. Project ini dibangun dengan arsitektur modular menggunakan Node.js, Express.js, MySQL, dan OpenAI API.

## Architecture & Tech Stack

### Core Technologies
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL dengan connection pooling
- **AI Integration**: OpenAI API (GPT models, Assistants API)
- **Authentication**: Token-based authentication
- **Documentation**: Swagger/OpenAPI 3.0
- **File Upload**: Multer
- **Environment**: dotenv

### Project Structure
```
be/
├── server.js                 # Entry point
├── src/
│   ├── app.js                # Main application setup
│   ├── config/               # Configuration files
│   │   ├── database.js       # MySQL connection pool
│   │   ├── openai.js         # OpenAI client setup
│   │   └── swagger.js        # API documentation config
│   ├── controllers/          # Request handlers
│   ├── middleware/           # Custom middleware
│   │   ├── auth.js           # Token authentication
│   │   ├── cors.js           # CORS configuration
│   │   ├── errorHandler.js   # Global error handling
│   │   ├── logger.js         # Request/error logging
│   │   └── upload.js         # File upload handling
│   ├── routes/               # Modular route definitions
│   │   ├── users/            # User authentication & profile
│   │   ├── experts/          # AI expert management
│   │   ├── chat/             # Chat functionality
│   │   ├── assistants/       # OpenAI assistant management
│   │   ├── affiliate/        # Referral system
│   │   └── balance/          # Points & credits system
│   ├── services/             # Business logic layer
│   └── utils/                # Utility functions
└── uploads/                  # File storage directory
```

## Core Features & Modules

### 1. User Management
- **Registration/Login**: Email, phone, password with OTP verification
- **Profile Management**: Update profile, change password
- **Authentication**: Token-based auth stored in database
- **Password Reset**: Secure password reset flow

### 2. AI Expert System
- **Expert Creation**: Create custom AI experts with system prompts
- **Model Support**: Multiple OpenAI models (GPT-4o, GPT-4o-mini, etc.)
- **Pricing System**: Configurable expert commission percentages
- **Public Marketplace**: Public/private expert visibility
- **Knowledge Base**: File upload for expert training

### 3. Chat System
- **Real-time Chat**: Integration with OpenAI Assistants API
- **Session Management**: Persistent chat sessions with thread IDs
- **Cost Calculation**: Token-based pricing with expert commissions
- **Message History**: Complete chat history storage
- **Balance Integration**: Automatic balance deduction

### 4. Balance System
- **Dual Currency**: Points (earned) and Credits (purchased)
- **Smart Usage**: Points used first, then credits
- **Transaction History**: Complete audit trail
- **Commission Tracking**: Expert earnings from credit usage
- **Welcome Bonus**: Automatic bonus for new users

### 5. Affiliate System
- **Referral Codes**: Unique codes for each user
- **Visitor Tracking**: Frontend-managed visitor IDs
- **Commission Structure**: Earnings from referral activities
- **Statistics**: Comprehensive affiliate performance metrics

### 6. Assistant Management
- **OpenAI Integration**: Direct assistant creation/management
- **File Processing**: Knowledge base upload and processing
- **Model Configuration**: Flexible model selection

## Development Best Practices

### 1. Code Organization
- **Modular Architecture**: Separate concerns into distinct modules
- **Service Layer Pattern**: Business logic in services, not controllers
- **Route Modularity**: Feature-based route organization
- **Middleware Chain**: Proper middleware ordering and error handling

### 2. Database Practices
- **Connection Pooling**: Efficient database connection management
- **Prepared Statements**: SQL injection prevention
- **Stored Procedures**: Complex operations in database layer
- **Transaction Management**: ACID compliance for critical operations

### 3. Security
- **Token Authentication**: Secure token-based auth
- **Input Validation**: Comprehensive request validation
- **Error Handling**: Secure error messages without data leakage
- **CORS Configuration**: Proper cross-origin resource sharing

### 4. Performance Optimization
- **Database Indexing**: Proper index usage for queries
- **Connection Pooling**: Reuse database connections
- **Async/Await**: Non-blocking operations
- **Error Boundaries**: Graceful error handling

### 5. API Documentation
- **Swagger Integration**: Complete API documentation
- **JSDoc Comments**: Inline code documentation
- **Request/Response Examples**: Clear API usage examples
- **Authentication Schemes**: Documented security requirements

## Environment Configuration

### Required Environment Variables
```env
# Database
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=aitrainerhub

# OpenAI
OPENAI_API_KEY=your_openai_api_key
ASSISTANT_ID=default_assistant_id

# Server
PORT=3001
FRONTEND_URL=http://localhost:3000
NODE_ENV=development

# Authentication
JWT_SECRET=your_jwt_secret

# File Upload
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
```

## Development Workflow

### 1. Setup
```bash
npm install
cp .env.example .env  # Configure environment
npm run dev          # Start development server
```

### 2. Database Setup
- Create MySQL database
- Run initialization scripts
- Configure connection parameters

### 3. API Testing
- Access Swagger UI at `/api-docs`
- Use provided authentication tokens
- Test all endpoints interactively

### 4. File Structure Guidelines
- Controllers: Handle HTTP requests/responses only
- Services: Contain all business logic
- Routes: Define endpoints with proper documentation
- Middleware: Reusable request processing logic
- Utils: Helper functions and utilities

## Key Implementation Patterns

### 1. Controller Pattern
```javascript
class ExampleController {
  async methodName(req, res) {
    try {
      const result = await ExampleService.processData(req.body);
      res.json({ success: true, data: result });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
}
```

### 2. Service Pattern
```javascript
class ExampleService {
  static async processData(data) {
    // Business logic here
    const [rows] = await db.execute('SELECT * FROM table WHERE id = ?', [data.id]);
    return rows;
  }
}
```

### 3. Route Documentation
```javascript
/**
 * @swagger
 * /api/endpoint:
 *   post:
 *     summary: Description
 *     tags: [TagName]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 */
```

### 4. Error Handling
```javascript
try {
  // Operation
} catch (error) {
  console.error('Operation failed:', error);
  throw new Error('User-friendly error message');
}
```

## Performance Considerations

### 1. Database Optimization
- Use connection pooling
- Implement proper indexing
- Optimize query performance
- Use stored procedures for complex operations

### 2. API Optimization
- Implement request caching where appropriate
- Use pagination for large datasets
- Optimize file upload handling
- Implement rate limiting

### 3. Memory Management
- Proper connection cleanup
- Avoid memory leaks in async operations
- Efficient file handling

## Testing Strategy

### 1. API Testing
- Use Swagger UI for interactive testing
- Test all authentication scenarios
- Validate request/response formats
- Test error handling

### 2. Integration Testing
- Database connection testing
- OpenAI API integration testing
- File upload functionality
- Balance calculation accuracy

### 3. Security Testing
- Authentication bypass attempts
- SQL injection prevention
- File upload security
- CORS configuration validation

## Deployment Considerations

### 1. Environment Setup
- Production environment variables
- Database configuration
- File storage setup
- SSL/TLS configuration

### 2. Monitoring
- Request logging
- Error tracking
- Performance monitoring
- Database connection monitoring

### 3. Backup Strategy
- Database backups
- File storage backups
- Configuration backups

## Troubleshooting Guide

### Common Issues
1. **Database Connection**: Check connection parameters and network
2. **OpenAI API**: Verify API key and rate limits
3. **File Upload**: Check permissions and storage space
4. **Authentication**: Verify token format and database storage

### Debug Tools
- Console logging with structured format
- Database query logging
- Request/response logging
- Error stack traces in development

## Future Enhancements

### Planned Features
1. **Real-time Notifications**: WebSocket integration
2. **Advanced Analytics**: User behavior tracking
3. **Payment Integration**: Credit purchase system
4. **Mobile API**: Mobile-specific endpoints
5. **Caching Layer**: Redis integration
6. **Rate Limiting**: API usage controls

---

**Note**: This system prompt provides comprehensive guidance for working with the AI Trainer Hub Backend. Always refer to the latest API documentation at `/api-docs` and follow the established patterns for consistency and maintainability.