"use client";

import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Alert } from "@/components/ui/alert";
import ReviewForm from "./ReviewForm";
import { api } from "@/lib/api";

interface ReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  expertId: number;
  expertName: string;
  onReviewSubmitted?: (review: any) => void;
}

const ReviewModal: React.FC<ReviewModalProps> = ({
  isOpen,
  onClose,
  expertId,
  expertName,
  onReviewSubmitted
}) => {
  const [canReview, setCanReview] = useState<boolean | null>(null);
  const [reviewEligibility, setReviewEligibility] = useState<{
    canReview: boolean;
    reason?: string;
    message: string;
  } | null>(null);
  const [existingReview, setExistingReview] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkReviewEligibility = useCallback(async () => {
    try {
      setIsLoading(true);
      
      const result = await api.canUserReview(expertId);
      
      if (result.success) {
        setReviewEligibility(result);
        setCanReview(result.canReview);
        
        // If user already reviewed, we might want to load their existing review
        if (result.reason === 'already_reviewed') {
          // Load user's reviews to find the existing one
          const userReviews = await api.getUserReviews(1, 100);
          if (userReviews.success) {
            const existingReview = userReviews.reviews.find(
              (review: any) => review.expert_id === expertId
            );
            if (existingReview) {
              setExistingReview(existingReview);
              setCanReview(true); // Allow editing existing review
            }
          }
        }
      } else {
        setCanReview(false);
        setReviewEligibility({
          canReview: false,
          message: result.error || "Unable to check review eligibility"
        });
      }
    } catch (error: any) {
      setCanReview(false);
      setReviewEligibility({
        canReview: false,
        message: error.message || "Failed to check review eligibility"
      });
    } finally {
      setIsLoading(false);
    }
  }, [expertId]);

  useEffect(() => {
    if (isOpen && expertId) {
      checkReviewEligibility();
    }
  }, [isOpen, expertId, checkReviewEligibility]);

  const handleReviewSuccess = (review: any) => {
    onReviewSubmitted?.(review);
    onClose();
  };

  const handleClose = () => {
    setCanReview(null);
    setReviewEligibility(null);
    setExistingReview(null);
    setIsLoading(true);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {existingReview ? "Update Your Review" : "Write a Review"}
          </DialogTitle>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : canReview ? (
          <ReviewForm
            expertId={expertId}
            expertName={expertName}
            existingReview={existingReview ? {
              id: existingReview.id,
              rating: existingReview.rating,
              review_text: existingReview.review_text
            } : undefined}
            onSuccess={handleReviewSuccess}
            onCancel={handleClose}
          />
        ) : (
          <div className="space-y-4">
            <Alert variant="destructive">
              {reviewEligibility?.message || "You cannot review this expert at this time."}
            </Alert>
            
            {reviewEligibility?.reason === 'insufficient_interaction' && (
              <div className="text-sm text-gray-600">
                <p>To maintain review quality, you need to have at least 3 conversations with this expert before you can write a review.</p>
                <p className="mt-2">Start chatting with {expertName} to unlock the ability to review!</p>
              </div>
            )}
            
            <div className="flex justify-end">
              <Button onClick={handleClose} variant="outline">
                Close
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ReviewModal;