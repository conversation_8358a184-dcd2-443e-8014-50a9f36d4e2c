# Task 5: Frontend Implementation

## 🎨 Frontend Architecture for Simplified Shared Link Flow

### Core Components
- **SharedExpertLanding** - Landing page with expert preview and consent
- **ConsentDialog** - Monitoring consent management
- **ShareManager** - Share creation and management
- **ShareAnalytics** - Analytics dashboard

### Page Structure
```
fe/src/app/
├── shared/
│   └── [shareToken]/
│       └── page.tsx              # Landing page with consent
├── dashboard/
│   └── shares/
│       ├── page.tsx              # Share management
│       └── [shareToken]/
│           └── analytics/
│               └── page.tsx      # Share analytics
└── chat/
    └── [expertId]/
        └── page.tsx              # Enhanced with ref tracking
```

## 📁 Component Structure

```
fe/src/components/
├── sharing/
│   ├── SharedExpertLanding.tsx   # Main landing component
│   ├── ConsentDialog.tsx         # Consent management
│   ├── ShareCreator.tsx          # Share creation form
│   ├── ShareList.tsx             # User's shares list
│   ├── ShareCard.tsx             # Individual share item
│   └── ShareAnalytics.tsx        # Analytics dashboard
├── ui/
│   ├── consent-banner.tsx        # Consent UI components
│   └── share-button.tsx          # Share action buttons
└── hooks/
    ├── useSharing.ts             # Share management hook
    ├── useConsent.ts             # Consent management hook
    └── useShareAnalytics.ts      # Analytics hook
```

## 🎯 Core Components Implementation

### SharedExpertLanding.tsx
```typescript
// fe/src/app/shared/[shareToken]/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { AlertCircle, MessageCircle, User, Calendar } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

interface SharedExpert {
  shareToken: string;
  expert: {
    id: number;
    name: string;
    description: string;
    avatar: string;
    category: string;
  };
  sharedBy: {
    id: number;
    username: string;
  };
  monitorEnabled: boolean;
  shareType: string;
  createdAt: string;
}

interface ConsentStatus {
  needsConsent: boolean;
}

export default function SharedExpertPage({ params }: { params: { shareToken: string } }) {
  const { shareToken } = params;
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  
  const [expert, setExpert] = useState<SharedExpert | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [consentGiven, setConsentGiven] = useState(false);
  const [needsConsent, setNeedsConsent] = useState(false);
  const [submittingConsent, setSubmittingConsent] = useState(false);

  // Load expert data
  useEffect(() => {
    loadExpertData();
  }, [shareToken]);

  // Check consent status when user is authenticated
  useEffect(() => {
    if (isAuthenticated && expert?.monitorEnabled) {
      checkConsentStatus();
    }
  }, [isAuthenticated, expert]);

  // Handle redirect after login
  useEffect(() => {
    const handleRedirectAfterLogin = () => {
      const storedShareToken = localStorage.getItem('pendingShareToken');
      const storedExpertId = localStorage.getItem('pendingExpertId');
      const storedConsent = localStorage.getItem('pendingConsent');
      
      if (storedShareToken === shareToken && storedExpertId && isAuthenticated) {
        // Clear stored data
        localStorage.removeItem('pendingShareToken');
        localStorage.removeItem('pendingExpertId');
        localStorage.removeItem('pendingConsent');
        
        // If consent was given, proceed to chat
        if (storedConsent === 'true') {
          router.push(`/chat/${storedExpertId}?ref=${shareToken}`);
        }
      }
    };

    handleRedirectAfterLogin();
  }, [isAuthenticated, shareToken, router]);

  const loadExpertData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/shared/${shareToken}`);
      
      if (!response.ok) {
        throw new Error('Failed to load expert data');
      }
      
      const data = await response.json();
      setExpert(data.data);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load expert');
    } finally {
      setLoading(false);
    }
  };

  const checkConsentStatus = async () => {
    try {
      const response = await fetch(`/api/shared/${shareToken}/consent-status`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setNeedsConsent(data.data.needsConsent);
      }
    } catch (err) {
      console.error('Error checking consent status:', err);
    }
  };

  const handleProceedToChat = async () => {
    if (!expert) return;

    // If user is not authenticated, store data and redirect to login
    if (!isAuthenticated) {
      localStorage.setItem('pendingShareToken', shareToken);
      localStorage.setItem('pendingExpertId', expert.expert.id.toString());
      localStorage.setItem('pendingConsent', consentGiven.toString());
      
      router.push('/login?redirect=shared');
      return;
    }

    // If monitoring is enabled and consent is needed, record consent first
    if (expert.monitorEnabled && needsConsent) {
      await recordConsent();
    }

    // Redirect to chat
    router.push(`/chat/${expert.expert.id}?ref=${shareToken}`);
  };

  const recordConsent = async () => {
    try {
      setSubmittingConsent(true);
      
      const response = await fetch(`/api/shared/${shareToken}/consent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ consentGiven })
      });
      
      if (!response.ok) {
        throw new Error('Failed to record consent');
      }
      
      setNeedsConsent(false);
      
      toast({
        title: 'Consent recorded',
        description: 'Your consent preference has been saved.'
      });
      
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to record consent. Please try again.',
        variant: 'destructive'
      });
      throw err;
    } finally {
      setSubmittingConsent(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !expert) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Share Not Found
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              {error || 'This share link is invalid or has expired.'}
            </p>
            <Button onClick={() => router.push('/')} className="w-full">
              Go to Homepage
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const canProceed = !expert.monitorEnabled || !needsConsent || consentGiven;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Header */}
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-bold">AI Expert Shared</h1>
            <p className="text-muted-foreground">
              {expert.sharedBy.username} shared an AI expert with you
            </p>
          </div>

          {/* Expert Card */}
          <Card className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex items-start gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={expert.expert.avatar} alt={expert.expert.name} />
                  <AvatarFallback>
                    {expert.expert.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-xl">{expert.expert.name}</CardTitle>
                    <Badge variant="secondary">{expert.expert.category}</Badge>
                  </div>
                  <CardDescription className="text-sm">
                    {expert.expert.description}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Share Info */}
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  <span>Shared by {expert.sharedBy.username}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(expert.createdAt).toLocaleDateString()}</span>
                </div>
              </div>

              {/* Monitoring Notice */}
              {expert.monitorEnabled && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Privacy Notice:</strong> The creator of this expert has enabled 
                    monitoring to track usage and improve the service. Your conversations 
                    may be monitored for analytics purposes.
                  </AlertDescription>
                </Alert>
              )}

              {/* Consent Checkbox */}
              {expert.monitorEnabled && needsConsent && (
                <div className="space-y-3">
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="consent"
                      checked={consentGiven}
                      onCheckedChange={(checked) => setConsentGiven(checked as boolean)}
                    />
                    <label
                      htmlFor="consent"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      I consent to monitoring of my conversations with this AI expert 
                      for analytics and improvement purposes.
                    </label>
                  </div>
                  
                  {!consentGiven && (
                    <p className="text-xs text-muted-foreground">
                      You must provide consent to proceed with monitoring enabled.
                    </p>
                  )}
                </div>
              )}

              {/* Action Button */}
              <Button
                onClick={handleProceedToChat}
                disabled={!canProceed || submittingConsent}
                className="w-full"
                size="lg"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                {!isAuthenticated 
                  ? 'Login to Chat with Expert'
                  : submittingConsent
                  ? 'Recording Consent...'
                  : 'Start Chatting'
                }
              </Button>

              {/* Alternative Action */}
              {expert.monitorEnabled && !consentGiven && (
                <div className="text-center">
                  <Button
                    variant="outline"
                    onClick={() => router.push('/')}
                    className="w-full"
                  >
                    Browse Other Experts Instead
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center text-sm text-muted-foreground">
            <p>
              Powered by AI Trainer Hub • 
              <Button variant="link" className="p-0 h-auto" onClick={() => router.push('/')}>
                Create your own AI expert
              </Button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### ShareCreator.tsx
```typescript
// fe/src/components/sharing/ShareCreator.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Share2, Copy, Check, Eye, BarChart3 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ShareCreatorProps {
  expertId: number;
  expertName: string;
  onShareCreated?: (share: any) => void;
}

export function ShareCreator({ expertId, expertName, onShareCreated }: ShareCreatorProps) {
  const { toast } = useToast();
  const [creating, setCreating] = useState(false);
  const [monitorEnabled, setMonitorEnabled] = useState(false);
  const [shareUrl, setShareUrl] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  const createShare = async () => {
    try {
      setCreating(true);
      
      const response = await fetch(`/api/experts/${expertId}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ monitorEnabled })
      });
      
      if (!response.ok) {
        throw new Error('Failed to create share link');
      }
      
      const data = await response.json();
      setShareUrl(data.data.shareUrl);
      
      toast({
        title: 'Share link created',
        description: 'Your share link is ready to use!'
      });
      
      onShareCreated?.(data.data);
      
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to create share link. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setCreating(false);
    }
  };

  const copyToClipboard = async () => {
    if (!shareUrl) return;
    
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      
      toast({
        title: 'Copied!',
        description: 'Share link copied to clipboard'
      });
      
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to copy link',
        variant: 'destructive'
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Share2 className="h-5 w-5" />
          Share {expertName}
        </CardTitle>
        <CardDescription>
          Create a shareable link for this AI expert
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {!shareUrl ? (
          <>
            {/* Monitoring Option */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="monitoring">Enable Monitoring</Label>
                  <p className="text-sm text-muted-foreground">
                    Track usage analytics and user interactions
                  </p>
                </div>
                <Switch
                  id="monitoring"
                  checked={monitorEnabled}
                  onCheckedChange={setMonitorEnabled}
                />
              </div>
              
              {monitorEnabled && (
                <Alert>
                  <Eye className="h-4 w-4" />
                  <AlertDescription>
                    Users will be asked for consent before chatting. You'll be able to 
                    view analytics and conversation metrics.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Create Button */}
            <Button
              onClick={createShare}
              disabled={creating}
              className="w-full"
            >
              {creating ? 'Creating...' : 'Create Share Link'}
            </Button>
          </>
        ) : (
          <>
            {/* Share URL */}
            <div className="space-y-2">
              <Label>Share Link</Label>
              <div className="flex gap-2">
                <Input
                  value={shareUrl}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  onClick={copyToClipboard}
                  variant="outline"
                  size="icon"
                >
                  {copied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <Button
                onClick={() => {
                  setShareUrl(null);
                  setCopied(false);
                }}
                variant="outline"
                className="flex-1"
              >
                Create Another
              </Button>
              
              {monitorEnabled && (
                <Button
                  onClick={() => window.open(`/dashboard/shares/${shareUrl.split('/').pop()}/analytics`, '_blank')}
                  variant="outline"
                  className="flex-1"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analytics
                </Button>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
```

### ShareList.tsx
```typescript
// fe/src/components/sharing/ShareList.tsx
'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Share2, 
  MoreVertical, 
  Copy, 
  BarChart3, 
  Settings, 
  Trash2, 
  Eye, 
  Users, 
  MessageCircle 
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';

interface Share {
  id: number;
  shareToken: string;
  shareUrl: string;
  expert: {
    id: number;
    name: string;
    avatar: string;
  };
  monitorEnabled: boolean;
  shareType: string;
  stats: {
    clickCount: number;
    conversionCount: number;
    uniqueVisitors: number;
    chatSessions: number;
  };
  createdAt: string;
  lastAccessedAt: string;
}

export function ShareList() {
  const { toast } = useToast();
  const [shares, setShares] = useState<Share[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadShares();
  }, []);

  const loadShares = async () => {
    try {
      const response = await fetch('/api/shares/my', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setShares(data.data);
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to load shares',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const copyShareUrl = async (shareUrl: string) => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      toast({
        title: 'Copied!',
        description: 'Share link copied to clipboard'
      });
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to copy link',
        variant: 'destructive'
      });
    }
  };

  const deactivateShare = async (shareToken: string) => {
    try {
      const response = await fetch(`/api/shares/${shareToken}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        setShares(shares.filter(share => share.shareToken !== shareToken));
        toast({
          title: 'Share deactivated',
          description: 'The share link has been deactivated'
        });
      }
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to deactivate share',
        variant: 'destructive'
      });
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (shares.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Share2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No shares yet</h3>
          <p className="text-muted-foreground mb-4">
            Create your first share link to start sharing your AI experts
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {shares.map((share) => (
        <Card key={share.id}>
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4 flex-1">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={share.expert.avatar} alt={share.expert.name} />
                  <AvatarFallback>
                    {share.expert.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold">{share.expert.name}</h3>
                    <Badge variant={share.shareType === 'creator' ? 'default' : 'secondary'}>
                      {share.shareType}
                    </Badge>
                    {share.monitorEnabled && (
                      <Badge variant="outline">
                        <Eye className="h-3 w-3 mr-1" />
                        Monitored
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span>{share.stats.clickCount} views</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MessageCircle className="h-4 w-4" />
                      <span>{share.stats.chatSessions} chats</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span>Created {formatDistanceToNow(new Date(share.createdAt), { addSuffix: true })}</span>
                    </div>
                  </div>
                  
                  {share.lastAccessedAt && (
                    <p className="text-xs text-muted-foreground">
                      Last accessed {formatDistanceToNow(new Date(share.lastAccessedAt), { addSuffix: true })}
                    </p>
                  )}
                </div>
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => copyShareUrl(share.shareUrl)}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Link
                  </DropdownMenuItem>
                  
                  {share.monitorEnabled && (
                    <DropdownMenuItem 
                      onClick={() => window.open(`/dashboard/shares/${share.shareToken}/analytics`, '_blank')}
                    >
                      <BarChart3 className="h-4 w-4 mr-2" />
                      View Analytics
                    </DropdownMenuItem>
                  )}
                  
                  <DropdownMenuItem onClick={() => deactivateShare(share.shareToken)}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Deactivate
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
```

### Enhanced Chat Page
```typescript
// fe/src/app/chat/[expertId]/page.tsx (Enhanced)
'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
// ... other imports

export default function ChatPage({ params }: { params: { expertId: string } }) {
  const { expertId } = params;
  const searchParams = useSearchParams();
  const shareToken = searchParams.get('ref');
  const { user } = useAuth();
  
  // ... existing chat logic
  
  useEffect(() => {
    // Track conversion if user came from share link
    if (shareToken && user) {
      trackConversion();
    }
  }, [shareToken, user]);
  
  const trackConversion = async () => {
    try {
      // Update conversion count
      await fetch(`/api/shared/${shareToken}/convert`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
    } catch (err) {
      console.error('Error tracking conversion:', err);
    }
  };
  
  // ... rest of chat component
}
```

## 🎣 Custom Hooks

### useSharing.ts
```typescript
// fe/src/hooks/useSharing.ts
import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

interface CreateShareParams {
  expertId: number;
  monitorEnabled?: boolean;
}

interface Share {
  id: number;
  shareToken: string;
  shareUrl: string;
  expertId: number;
  monitorEnabled: boolean;
  shareType: string;
  createdAt: string;
}

export function useSharing() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const createShare = useCallback(async ({ expertId, monitorEnabled = false }: CreateShareParams): Promise<Share | null> => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/experts/${expertId}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ monitorEnabled })
      });
      
      if (!response.ok) {
        throw new Error('Failed to create share');
      }
      
      const data = await response.json();
      
      toast({
        title: 'Share created',
        description: 'Your share link is ready!'
      });
      
      return data.data;
      
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create share link',
        variant: 'destructive'
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const deactivateShare = useCallback(async (shareToken: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/shares/${shareToken}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to deactivate share');
      }
      
      toast({
        title: 'Share deactivated',
        description: 'The share link has been deactivated'
      });
      
      return true;
      
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to deactivate share',
        variant: 'destructive'
      });
      return false;
    }
  }, [toast]);

  return {
    createShare,
    deactivateShare,
    loading
  };
}
```

### useConsent.ts
```typescript
// fe/src/hooks/useConsent.ts
import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface ConsentStatus {
  needsConsent: boolean;
  loading: boolean;
}

export function useConsent(shareToken: string) {
  const { isAuthenticated } = useAuth();
  const [consentStatus, setConsentStatus] = useState<ConsentStatus>({
    needsConsent: false,
    loading: true
  });

  const checkConsentStatus = useCallback(async () => {
    if (!isAuthenticated || !shareToken) {
      setConsentStatus({ needsConsent: true, loading: false });
      return;
    }

    try {
      const response = await fetch(`/api/shared/${shareToken}/consent-status`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConsentStatus({
          needsConsent: data.data.needsConsent,
          loading: false
        });
      } else {
        setConsentStatus({ needsConsent: true, loading: false });
      }
    } catch (error) {
      console.error('Error checking consent status:', error);
      setConsentStatus({ needsConsent: true, loading: false });
    }
  }, [isAuthenticated, shareToken]);

  const recordConsent = useCallback(async (consentGiven: boolean): Promise<boolean> => {
    try {
      const response = await fetch(`/api/shared/${shareToken}/consent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ consentGiven })
      });
      
      if (response.ok) {
        setConsentStatus(prev => ({ ...prev, needsConsent: false }));
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error recording consent:', error);
      return false;
    }
  }, [shareToken]);

  useEffect(() => {
    checkConsentStatus();
  }, [checkConsentStatus]);

  return {
    ...consentStatus,
    recordConsent,
    recheckConsent: checkConsentStatus
  };
}
```

## 📱 Pages Implementation

### Share Management Dashboard
```typescript
// fe/src/app/dashboard/shares/page.tsx
'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ShareList } from '@/components/sharing/ShareList';
import { Button } from '@/components/ui/button';
import { Plus, Share2, BarChart3 } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function SharesPage() {
  const router = useRouter();
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Share Management</h1>
            <p className="text-muted-foreground">
              Manage and track your shared AI experts
            </p>
          </div>
          
          <Button onClick={() => router.push('/experts')}>
            <Plus className="h-4 w-4 mr-2" />
            Create New Share
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Shares</CardTitle>
              <Share2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">
                +2 from last month
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Views</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,234</div>
              <p className="text-xs text-muted-foreground">
                +15% from last month
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Conversions</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">89</div>
              <p className="text-xs text-muted-foreground">
                7.2% conversion rate
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Shares List */}
        <Card>
          <CardHeader>
            <CardTitle>Your Shares</CardTitle>
            <CardDescription>
              All your active share links and their performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ShareList />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
```

---

## 🔧 Utils and Helpers

### Cookie Management
```typescript
// fe/src/lib/cookies.ts
export const shareStorage = {
  setShareData(shareToken: string, expertId: string, consent: boolean) {
    localStorage.setItem('pendingShareToken', shareToken);
    localStorage.setItem('pendingExpertId', expertId);
    localStorage.setItem('pendingConsent', consent.toString());
  },
  
  getShareData() {
    return {
      shareToken: localStorage.getItem('pendingShareToken'),
      expertId: localStorage.getItem('pendingExpertId'),
      consent: localStorage.getItem('pendingConsent') === 'true'
    };
  },
  
  clearShareData() {
    localStorage.removeItem('pendingShareToken');
    localStorage.removeItem('pendingExpertId');
    localStorage.removeItem('pendingConsent');
  }
};
```

### Share URL Generator
```typescript
// fe/src/lib/shareUtils.ts
export function generateShareUrl(shareToken: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || window.location.origin;
  return `${baseUrl}/shared/${shareToken}`;
}

export function extractShareToken(url: string): string | null {
  const match = url.match(/\/shared\/([a-f0-9]{32})/);
  return match ? match[1] : null;
}

export function validateShareToken(token: string): boolean {
  return /^[a-f0-9]{32}$/.test(token);
}
```

---

## 📋 Implementation Checklist

### Core Components
- [ ] `SharedExpertLanding.tsx` - Landing page with consent
- [ ] `ConsentDialog.tsx` - Consent management component
- [ ] `ShareCreator.tsx` - Share creation form
- [ ] `ShareList.tsx` - User's shares management
- [ ] `ShareAnalytics.tsx` - Analytics dashboard

### Pages
- [ ] `/shared/[shareToken]/page.tsx` - Public landing page
- [ ] `/dashboard/shares/page.tsx` - Share management
- [ ] `/dashboard/shares/[shareToken]/analytics/page.tsx` - Analytics
- [ ] Enhanced `/chat/[expertId]/page.tsx` - Ref tracking

### Hooks
- [ ] `useSharing.ts` - Share management
- [ ] `useConsent.ts` - Consent handling
- [ ] `useShareAnalytics.ts` - Analytics data

### Utils
- [ ] Cookie/localStorage management
- [ ] Share URL utilities
- [ ] Token validation

### Integration
- [ ] Enhanced auth context for redirect handling
- [ ] API integration with backend endpoints
- [ ] Error handling and loading states
- [ ] Responsive design and accessibility

---

**Document Version:** 3.0 (Simplified Frontend)  
**Last Updated:** December 2024  
**Status:** ✅ Ready for Implementation