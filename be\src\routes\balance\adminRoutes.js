const express = require('express');
const BalanceController = require('../../controllers/balanceController');
const { authenticateToken } = require('../../middleware/auth');

const router = express.Router();

// Apply authentication to all admin routes
router.use(authenticateToken);

// Add points (admin)
/**
 * @swagger
 * /api/balance/admin/add-points:
 *   post:
 *     summary: Add points to user account (admin only)
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *               points:
 *                 type: number
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Points added successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 */
router.post('/admin/add-points', BalanceController.addPoints);

// Process top-up (admin)
/**
 * @swagger
 * /api/balance/admin/top-up:
 *   post:
 *     summary: Process balance top-up (admin only)
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *               amount:
 *                 type: number
 *               currency:
 *                 type: string
 *                 enum: [points, credits]
 *               transactionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Top-up processed successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 */
router.post('/admin/top-up', BalanceController.processTopUp);

module.exports = router;
