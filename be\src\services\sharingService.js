// SharingService.js - Service for expert sharing functionality
const mysql = require('mysql2/promise');
const crypto = require('crypto');
const dbConfig = require('../config/database');

class SharingService {
  constructor() {
    this.pool = mysql.createPool(dbConfig);
  }

  /**
   * Create a new share link for an expert
   * @param {number} expertId - Expert ID to share
   * @param {number} userId - User creating the share
   * @param {boolean} monitorEnabled - Whether to enable monitoring
   * @returns {Promise<Object>} Share data with token
   */
  async createShare(expertId, userId, monitorEnabled = false) {
    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // Generate unique share token
      const shareToken = this.generateShareToken();
      
      // Check if expert exists and user has access
      const [expertRows] = await connection.execute(
        'SELECT id, name, description, created_by FROM experts WHERE id = ?',
        [expertId]
      );
      
      if (expertRows.length === 0) {
        throw new Error('Expert not found');
      }
      
      const expert = expertRows[0];
      
      // Create share record
      const [shareResult] = await connection.execute(
        `INSERT INTO expert_shares 
         (expert_id, shared_by_user_id, share_token, monitor_enabled, is_active) 
         VALUES (?, ?, ?, ?, TRUE)`,
        [expertId, userId, shareToken, monitorEnabled]
      );
      
      await connection.commit();
      
      return {
        id: shareResult.insertId,
        shareToken,
        expertId,
        expertName: expert.name,
        monitorEnabled,
        shareUrl: `${process.env.FRONTEND_URL}/shared/${shareToken}`,
        createdAt: new Date()
      };
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Get share information by token
   * @param {string} shareToken - Share token
   * @returns {Promise<Object>} Share and expert data
   */
  async getShareByToken(shareToken) {
    const connection = await this.pool.getConnection();
    
    try {
      const [rows] = await connection.execute(
        `SELECT 
           es.id as share_id,
           es.expert_id,
           es.shared_by_user_id,
           es.share_token,
           es.monitor_enabled,
           es.is_active,
           es.click_count,
           es.conversion_count,
           es.created_at as share_created_at,
           e.name as expert_name,
           e.description as expert_description,
           e.labels as expert_labels,
           e.first_message,
           e.voice_enabled,
           e.average_rating,
           e.total_reviews,
           u.username as shared_by_username
         FROM expert_shares es
         JOIN experts e ON es.expert_id = e.id
         JOIN user u ON es.shared_by_user_id = u.user_id
         WHERE es.share_token = ? AND es.is_active = TRUE`,
        [shareToken]
      );
      
      if (rows.length === 0) {
        return null;
      }
      
      const shareData = rows[0];
      
      // Update click count
      await connection.execute(
        'UPDATE expert_shares SET click_count = click_count + 1, last_accessed_at = NOW() WHERE share_token = ?',
        [shareToken]
      );
      
      return {
        shareId: shareData.share_id,
        shareToken: shareData.share_token,
        monitorEnabled: shareData.monitor_enabled,
        clickCount: shareData.click_count + 1,
        conversionCount: shareData.conversion_count,
        expert: {
          id: shareData.expert_id,
          name: shareData.expert_name,
          description: shareData.expert_description,
          labels: shareData.expert_labels ? JSON.parse(shareData.expert_labels) : [],
          firstMessage: shareData.first_message,
          voiceEnabled: shareData.voice_enabled,
          averageRating: shareData.average_rating,
          totalReviews: shareData.total_reviews
        },
        sharedBy: {
          userId: shareData.shared_by_user_id,
          username: shareData.shared_by_username
        },
        createdAt: shareData.share_created_at
      };
      
    } finally {
      connection.release();
    }
  }

  /**
   * Get all shares created by a user
   * @param {number} userId - User ID
   * @param {number} limit - Limit results
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Array>} List of shares
   */
  async getUserShares(userId, limit = 20, offset = 0) {
    const connection = await this.pool.getConnection();
    
    try {
      const [rows] = await connection.execute(
        `SELECT 
           es.id as share_id,
           es.expert_id,
           es.share_token,
           es.monitor_enabled,
           es.is_active,
           es.click_count,
           es.conversion_count,
           es.last_accessed_at,
           es.created_at as share_created_at,
           e.name as expert_name,
           e.description as expert_description
         FROM expert_shares es
         JOIN experts e ON es.expert_id = e.id
         WHERE es.shared_by_user_id = ?
         ORDER BY es.created_at DESC
         LIMIT ? OFFSET ?`,
        [userId, limit, offset]
      );
      
      return rows.map(row => ({
        shareId: row.share_id,
        shareToken: row.share_token,
        shareUrl: `${process.env.FRONTEND_URL}/shared/${row.share_token}`,
        monitorEnabled: row.monitor_enabled,
        isActive: row.is_active,
        clickCount: row.click_count,
        conversionCount: row.conversion_count,
        lastAccessedAt: row.last_accessed_at,
        expert: {
          id: row.expert_id,
          name: row.expert_name,
          description: row.expert_description
        },
        createdAt: row.share_created_at
      }));
      
    } finally {
      connection.release();
    }
  }

  /**
   * Update share settings
   * @param {string} shareToken - Share token
   * @param {number} userId - User ID (must be owner)
   * @param {Object} updateData - Data to update (monitorEnabled, isActive)
   * @returns {Promise<boolean>} Success status
   */
  async updateShare(shareToken, userId, updateData) {
    const connection = await this.pool.getConnection();
    
    try {
      const updateFields = [];
      const updateValues = [];
      
      if (updateData.monitorEnabled !== undefined) {
        updateFields.push('monitor_enabled = ?');
        updateValues.push(updateData.monitorEnabled);
      }
      
      if (updateData.isActive !== undefined) {
        updateFields.push('is_active = ?');
        updateValues.push(updateData.isActive);
      }
      
      if (updateFields.length === 0) {
        return false;
      }
      
      updateValues.push(shareToken, userId);
      
      const [result] = await connection.execute(
        `UPDATE expert_shares SET ${updateFields.join(', ')} WHERE share_token = ? AND shared_by_user_id = ?`,
        updateValues
      );
      
      return result.affectedRows > 0;
      
    } finally {
      connection.release();
    }
  }

  /**
   * Delete a share
   * @param {string} shareToken - Share token
   * @param {number} userId - User ID (must be owner)
   * @returns {Promise<boolean>} Success status
   */
  async deleteShare(shareToken, userId) {
    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // Delete related records first (due to foreign key constraints)
      await connection.execute(
        'DELETE FROM share_access_logs WHERE share_token = ?',
        [shareToken]
      );
      
      await connection.execute(
        'DELETE FROM share_analytics WHERE share_token = ?',
        [shareToken]
      );
      
      await connection.execute(
        'DELETE FROM share_consents WHERE share_token = ?',
        [shareToken]
      );
      
      // Delete the share itself
      const [result] = await connection.execute(
        'DELETE FROM expert_shares WHERE share_token = ? AND shared_by_user_id = ?',
        [shareToken, userId]
      );
      
      await connection.commit();
      return result.affectedRows > 0;
      
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Track conversion (when user starts chatting)
   * @param {string} shareToken - Share token
   * @param {number} userId - User who converted
   * @returns {Promise<void>}
   */
  async trackConversion(shareToken, userId) {
    const connection = await this.pool.getConnection();
    
    try {
      // Update conversion count
      await connection.execute(
        'UPDATE expert_shares SET conversion_count = conversion_count + 1 WHERE share_token = ?',
        [shareToken]
      );
      
      // Log analytics event
      const analyticsService = require('./analyticsService');
      await analyticsService.logAction(shareToken, userId, 'chat_start');
      
    } finally {
      connection.release();
    }
  }

  /**
   * Generate a unique share token
   * @returns {string} Random token
   */
  generateShareToken() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Get share analytics summary
   * @param {string} shareToken - Share token
   * @param {number} userId - User ID (must be owner)
   * @returns {Promise<Object>} Analytics data
   */
  async getShareAnalytics(shareToken, userId) {
    const connection = await this.pool.getConnection();
    
    try {
      // Verify ownership
      const [ownerCheck] = await connection.execute(
        'SELECT id FROM expert_shares WHERE share_token = ? AND shared_by_user_id = ?',
        [shareToken, userId]
      );
      
      if (ownerCheck.length === 0) {
        throw new Error('Share not found or access denied');
      }
      
      // Get basic stats
      const [shareStats] = await connection.execute(
        'SELECT click_count, conversion_count, created_at FROM expert_shares WHERE share_token = ?',
        [shareToken]
      );
      
      // Get analytics breakdown
      const [analyticsBreakdown] = await connection.execute(
        `SELECT 
           action_type,
           COUNT(*) as count,
           DATE(created_at) as date
         FROM share_analytics 
         WHERE share_token = ? 
         GROUP BY action_type, DATE(created_at)
         ORDER BY date DESC`,
        [shareToken]
      );
      
      return {
        totalClicks: shareStats[0].click_count,
        totalConversions: shareStats[0].conversion_count,
        conversionRate: shareStats[0].click_count > 0 
          ? (shareStats[0].conversion_count / shareStats[0].click_count * 100).toFixed(2)
          : 0,
        createdAt: shareStats[0].created_at,
        analyticsBreakdown
      };
      
    } finally {
      connection.release();
    }
  }
}

module.exports = new SharingService();