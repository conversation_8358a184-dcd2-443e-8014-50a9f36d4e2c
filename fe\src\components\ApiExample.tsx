'use client';

import { useState, useEffect } from 'react';
import { api } from '@/lib/api';

export default function ApiExample() {
  const [message, setMessage] = useState('');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [userToken, setUserToken] = useState<string | null>(null);
  
  // Get configuration values
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

  useEffect(() => {
    // Get token from localStorage
    if (typeof window !== 'undefined') {
      setUserToken(localStorage.getItem('token'));
    }
  }, []);

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    setLoading(true);
    setError('');
    setResponse('');

    try {
      // Semua panggilan API otomatis menggunakan token dari environment
      const result = await api.chat(message);
      setResponse(JSON.stringify(result, null, 2));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const testHealthCheck = async () => {
    setLoading(true);
    setError('');
    setResponse('');

    try {
      const result = await api.health();
      setResponse(JSON.stringify(result, null, 2));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const testAssistant = async () => {
    setLoading(true);
    setError('');
    setResponse('');

    try {
      // Create thread
      const thread = await api.createThread();
      setResponse(`Thread created: ${JSON.stringify(thread, null, 2)}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">API Test dengan Token Authentication</h2>
      
      {/* Configuration Display */}
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded">
        <h3 className="font-semibold text-blue-800 mb-2">Current Configuration:</h3>
        <div className="text-blue-700 text-sm space-y-1">
          <div><strong>Backend URL:</strong> {apiUrl}</div>
          <div><strong>User Token:</strong> {userToken ? userToken.substring(0, 3) + '***' : 'Not logged in'}</div>
          <div><strong>Full Chat URL:</strong> {apiUrl}/api/chat</div>
        </div>
      </div>
      
      <div className="space-y-4">
        {/* Health Check Test */}
        <div>
          <button
            onClick={testHealthCheck}
            disabled={loading}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
          >
            Test Health Check ({apiUrl}/health)
          </button>
        </div>

        {/* Chat Test */}
        <div>
          <div className="flex gap-2 mb-2">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter your message..."
              className="flex-1 border border-gray-300 rounded px-3 py-2"
              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
            />
            <button
              onClick={handleSendMessage}
              disabled={loading || !message.trim()}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              Send Chat ({apiUrl}/api/chat)
            </button>
          </div>
        </div>

        {/* Assistant Test */}
        <div>
          <button
            onClick={testAssistant}
            disabled={loading}
            className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
          >
            Test Assistant ({apiUrl}/assistant/thread)
          </button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-blue-500 font-medium">Loading...</div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong>Error:</strong> {error}
          </div>
        )}

        {/* Response Display */}
        {response && (
          <div className="bg-gray-100 border border-gray-300 rounded p-4">
            <h3 className="font-semibold mb-2">Response:</h3>
            <pre className="whitespace-pre-wrap text-sm overflow-x-auto">{response}</pre>
          </div>
        )}
      </div>

      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h3 className="font-semibold text-yellow-800">Troubleshooting:</h3>
        <ul className="text-yellow-700 text-sm mt-2 space-y-1">
          <li>• Pastikan backend berjalan di: <code className="bg-yellow-100 px-1 rounded">cd be && npm run dev</code></li>
          <li>• Pastikan database MySQL berjalan dengan database 'aitrainerhub'</li>
          <li>• Check browser console untuk debug logs</li>
          <li>• Pastikan tidak ada CORS issues</li>
        </ul>
      </div>
    </div>
  );
}