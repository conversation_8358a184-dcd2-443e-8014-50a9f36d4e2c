const express = require('express');
const router = express.Router();
const socketMonitor = require('../utils/socketMonitor');
const chatSocketHandler = require('../sockets/chatSocket');
const { authenticateToken } = require('../middleware/auth');

/**
 * @swagger
 * /api/socket/stats:
 *   get:
 *     summary: Get WebSocket server statistics
 *     tags: [Socket]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Socket server statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     currentConnections:
 *                       type: number
 *                     totalConnections:
 *                       type: number
 *                     totalMessages:
 *                       type: number
 *                     totalErrors:
 *                       type: number
 *                     uptime:
 *                       type: number
 *                     activeStreams:
 *                       type: number
 */
router.get('/stats', authenticateToken, (req, res) => {
  try {
    const stats = socketMonitor.getStats();
    const activeStreams = chatSocketHandler.getActiveStreamsCount();
    
    res.json({
      success: true,
      data: {
        ...stats,
        activeStreams,
        connections: stats.connections.map(conn => ({
          socketId: conn.socketId,
          userId: conn.userId,
          connectedAt: conn.connectedAt,
          messagesCount: conn.messagesCount,
          errorsCount: conn.errorsCount,
          lastActivity: conn.lastActivity
        }))
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to get socket statistics',
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/socket/user-connections:
 *   get:
 *     summary: Get current user's socket connections
 *     tags: [Socket]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User's socket connections
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 */
router.get('/user-connections', authenticateToken, (req, res) => {
  try {
    const userConnections = socketMonitor.getUserConnections(req.user.user_id);
    const userStreams = chatSocketHandler.getUserActiveStreams(req.user.user_id);
    
    res.json({
      success: true,
      data: {
        connections: userConnections,
        activeStreams: userStreams
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to get user connections',
      message: error.message
    });
  }
});

module.exports = router;