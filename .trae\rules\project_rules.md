# Product Overview

AI Trainer Hub is a marketplace platform that connects users with specialized AI experts. The platform enables users to create, customize, and monetize AI assistants while providing a comprehensive ecosystem for AI-powered conversations.

## Core Features

- **AI Expert Creation**: Users can create custom AI assistants with specialized knowledge, system prompts, and file uploads
- **Marketplace**: Browse and discover AI experts created by the community
- **Real-time Chat**: Interactive conversations with AI experts using OpenAI's API
- **Economic System**: Points/credits balance system with commission distribution
- **Affiliate Program**: Referral system with commission tracking
- **Voice Capabilities**: Speech-to-text and text-to-speech integration
- **Image Generation**: AI-powered expert profile images and in-chat image generation

## Target Users

- **Expert Creators**: Users who build and monetize specialized AI assistants
- **End Users**: People seeking specialized AI assistance for various domains
- **Affiliates**: Users who refer others and earn commissions

## Revenue Model

- Platform commission from expert usage
- Credit purchases by users
- Affiliate commission structure
- Expert creator revenue sharing



# Project Structure

## Root Directory
```
/
├── be/                 # Backend application
├── fe/                 # Frontend application  
├── .kiro/              # Kiro IDE configuration
├── prompts/            # AI role prompts
├── schema.sql          # Database schema
├── role_*.prompt       # Individual role prompt files
└── README.md           # Project documentation
```

## Backend Structure (be/)
```
be/
├── src/                # Source code (main application logic)
├── uploads/            # File upload storage
├── logs/               # Application logs
├── node_modules/       # Dependencies
├── server.js           # Main server entry point
├── package.json        # Dependencies and scripts
├── .env                # Environment variables
├── .gitignore          # Git ignore rules
└── API_DOCUMENTATION.md # API documentation
```

## Frontend Structure (fe/)
```
fe/
├── src/                # Source code
├── public/             # Static assets
├── .next/              # Next.js build output
├── node_modules/       # Dependencies
├── package.json        # Dependencies and scripts
├── next.config.ts      # Next.js configuration
├── tsconfig.json       # TypeScript configuration
├── tailwind.config.js  # Tailwind CSS configuration
├── components.json     # UI components configuration
└── eslint.config.mjs   # ESLint configuration
```

## Key Conventions

### File Organization
- Backend logic organized in `be/src/` directory
- Frontend components and pages in `fe/src/` directory
- Shared database schema at root level
- Environment files kept at application root level

### Naming Conventions
- Use kebab-case for file and directory names
- Use camelCase for JavaScript/TypeScript variables and functions
- Use PascalCase for React components
- Use UPPER_CASE for environment variables

### Git Structure
- Separate git repositories for frontend and backend (submodules)
- Main repository contains both applications
- Individual .gitignore files for each application

### Configuration Files
- Environment variables in `.env` files at application level
- TypeScript configuration in `tsconfig.json`
- Package management with npm (package-lock.json present)
- ESLint and Prettier configurations at application level

### Development Workflow
- Backend runs on port 3001 (default)
- Frontend runs on port 3000 (default)
- Both applications can run simultaneously
- Hot reload enabled for development

### API Structure
- RESTful API endpoints from backend
- Swagger documentation available
- CORS configured for frontend-backend communication
- JWT authentication for protected routes

# Database Interaction Rules

## Node.js Script-Based Database Operations

### Database Migration Workflow
When database changes are needed:

1. **Create Node.js Migration Scripts**: Always create numbered migration scripts in `be/migrations/` directory
   - Format: `XXX_descriptive_name.js` (e.g., `002_add_user_preferences.js`)
   - Use mysql2 connection from existing backend setup
   - Include both UP and DOWN migration functions

2. **Migration Script Structure**:
   ```javascript
   // Migration: Add user preferences table
   // Created: YYYY-MM-DD
   // Description: Brief description of changes
   
   const mysql = require('mysql2/promise');
   require('dotenv').config();
   
   const connection = mysql.createConnection({
     host: process.env.DB_HOST,
     user: process.env.DB_USER,
     password: process.env.DB_PASSWORD,
     database: process.env.DB_NAME
   });
   
   async function up() {
     const sql = `
       CREATE TABLE user_preferences (
         id INT PRIMARY KEY AUTO_INCREMENT,
         user_id INT NOT NULL,
         preferences JSON,
         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
         FOREIGN KEY (user_id) REFERENCES users(id)
       )
     `;
     await connection.execute(sql);
     console.log('✅ Created user_preferences table');
   }
   
   async function down() {
     await connection.execute('DROP TABLE user_preferences');
     console.log('✅ Dropped user_preferences table');
   }
   
   // Run migration
   if (require.main === module) {
     up().then(() => {
       console.log('Migration completed');
       process.exit(0);
     }).catch(err => {
       console.error('Migration failed:', err);
       process.exit(1);
     });
   }
   
   module.exports = { up, down };
   ```

3. **Execution Process**:
   - Create the Node.js migration script
   - Test the script in development
   - Run: `node be/migrations/XXX_feature_name.js`
   - Update main `schema.sql` if needed

### Database Interaction Best Practices

1. **Always Use Transactions**: Wrap multiple operations in transactions
2. **Connection Management**: Properly close database connections
3. **Error Handling**: Include try-catch blocks and meaningful error messages
4. **Environment Variables**: Use .env for database credentials
5. **Logging**: Add console logs for migration progress

### Script Creation Pattern
When database work is needed:
```bash
# 1. Create migration script
touch be/migrations/XXX_feature_name.js

# 2. Edit with Node.js code using mysql2
# 3. Test the migration
node be/migrations/XXX_feature_name.js

# 4. Update schema.sql if needed
# 5. Restart backend if schema changes affect models
```

### Utility Scripts
Create utility scripts for common database operations:

```javascript
// be/scripts/db-utils.js
const mysql = require('mysql2/promise');
require('dotenv').config();

async function getConnection() {
  return mysql.createConnection({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  });
}

async function runQuery(sql, params = []) {
  const connection = await getConnection();
  try {
    const [results] = await connection.execute(sql, params);
    return results;
  } finally {
    await connection.end();
  }
}

module.exports = { getConnection, runQuery };
```

### Emergency Rollback
Include rollback functions in migration scripts:
```javascript
// To rollback: node -e "require('./XXX_migration.js').down()"
```

## Development Database Rules

- Use consistent naming conventions (snake_case for tables/columns)
- Always include created_at and updated_at timestamps
- Use appropriate data types and constraints
- Index foreign keys and frequently queried columns
- Keep migration scripts small and focused on single changes
- Use mysql2/promise for async/await syntax
- Always close database connections properly