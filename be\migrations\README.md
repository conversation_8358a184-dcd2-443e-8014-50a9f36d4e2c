# Database Schema Enhancements

This directory contains database migration scripts that implement the schema enhancements for the AI Trainer Hub platform as specified in the requirements.

## Migration Files

### 002_add_expert_enhancement_columns.js
**Purpose**: Add expert enhancement columns to support new features
**Changes**:
- Added `first_message` TEXT column for expert's initial message
- Added `voice_enabled` BOOLEAN column for voice capability flag
- Added `total_chats` INT column for chat statistics
- Added `total_revenue` DECIMAL column for revenue tracking
- Added `average_rating` DECIMAL column for rating display
- Added `total_reviews` INT column for review count
- Created performance indexes for all new columns

### 003_create_reviews_ratings_tables.js
**Purpose**: Implement reviews and ratings system
**Changes**:
- Created `reviews` table with rating validation (1-5 stars)
- Added foreign key constraints to user and experts tables
- Created indexes for performance optimization
- Implemented automatic triggers to update expert ratings:
  - `update_expert_rating_after_review_insert`
  - `update_expert_rating_after_review_update`
  - `update_expert_rating_after_review_delete`

### 004_create_expert_sharing_system.js
**Purpose**: Implement expert sharing functionality with privacy controls
**Changes**:
- Created `expert_shares` table for sharing functionality
- Added sharing columns to `chat_sessions` table:
  - `is_shared` BOOLEAN
  - `shared_by_user_id` INT
  - `share_token` VARCHAR(255)
  - `monitor_enabled` BOOLEAN
- Created `GenerateShareToken` stored procedure for secure token generation
- Added appropriate indexes and foreign key constraints

### 005_create_ai_generation_tracking.js
**Purpose**: Implement AI generation tracking and multimedia message support
**Changes**:
- Created `ai_generation_logs` table for cost tracking
- Added multimedia support columns to `chat_messages` table:
  - `message_type` ENUM('text', 'image', 'voice')
  - `file_url` VARCHAR(500)
  - `voice_duration` INT
  - `generation_log_id` INT
- Created `LogAIGeneration` stored procedure for logging AI operations
- Created `ai_generation_stats` view for analytics
- Added indexes for efficient querying

## Utility Scripts

### run_all_migrations.js
Executes all schema enhancement migrations in the correct order. Can also rollback all changes.

**Usage**:
```bash
# Run all migrations
node be/migrations/run_all_migrations.js

# Rollback all migrations
node be/migrations/run_all_migrations.js rollback
```

### test_schema_enhancements.js
Comprehensive test suite that validates all schema changes are working correctly.

**Usage**:
```bash
node be/migrations/test_schema_enhancements.js
```

## Requirements Mapping

The migrations implement the following requirements:

- **Requirement 2**: Expert creation with voice capabilities and first messages
- **Requirement 2.1**: AI-generated labels and content tracking
- **Requirement 4**: Real-time chat with multimedia support
- **Requirement 4.1**: Expert sharing with privacy controls
- **Requirement 12**: Rating and review system
- **Requirement 12.1**: Review quality and moderation

## Database Schema Changes Summary

### New Tables
1. `reviews` - User reviews and ratings for experts
2. `expert_shares` - Expert sharing functionality
3. `ai_generation_logs` - AI generation cost and usage tracking

### Modified Tables
1. `experts` - Added 6 new columns for enhanced functionality
2. `chat_sessions` - Added 4 columns for sharing functionality
3. `chat_messages` - Added 4 columns for multimedia support

### New Database Objects
1. **Stored Procedures**:
   - `GenerateShareToken` - Secure share token generation
   - `LogAIGeneration` - AI generation logging

2. **Views**:
   - `ai_generation_stats` - AI generation analytics

3. **Triggers**:
   - Rating update triggers for automatic expert rating calculation

### Indexes Added
- Performance indexes on all new columns
- Composite indexes for common query patterns
- Foreign key indexes for referential integrity

## Testing

All migrations have been tested and verified to work correctly. The test suite validates:
- Table and column creation
- Trigger functionality
- Stored procedure creation
- View creation
- Data insertion and constraint validation

## Rollback Support

All migrations include rollback functionality that can safely reverse the changes. The rollback process:
1. Drops created triggers and procedures
2. Removes added indexes
3. Drops foreign key constraints
4. Removes added columns
5. Drops created tables

## Performance Considerations

- All new columns have appropriate indexes
- Triggers are optimized for performance
- Views use efficient queries
- Foreign key constraints ensure data integrity

## Next Steps

After running these migrations, the database will be ready for:
1. Real-time chat streaming implementation
2. Rating and review system development
3. Expert sharing functionality
4. AI content generation features
5. Voice input/output capabilities

The schema enhancements provide a solid foundation for all the advanced features specified in the AI Trainer Hub platform requirements.