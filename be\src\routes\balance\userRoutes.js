const express = require('express');
const BalanceController = require('../../controllers/balanceController');
const { authenticateToken } = require('../../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Balance
 *   description: User balance and transaction management
 */

// Apply authentication to all routes
router.use(authenticateToken);

// Get current balance
/**
 * @swagger
 * /api/balance/balance:
 *   get:
 *     summary: Get user's current balance
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current balance information
 *       401:
 *         description: Unauthorized
 */
router.get('/balance', BalanceController.getBalance);

// Get balance summary
/**
 * @swagger
 * /api/balance/summary:
 *   get:
 *     summary: Get balance summary with transaction history
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Balance summary and history
 *       401:
 *         description: Unauthorized
 */
router.get('/summary', BalanceController.getBalanceSummary);

// Get point history
/**
 * @swagger
 * /api/balance/points/history:
 *   get:
 *     summary: Get point transaction history
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Point transaction history
 *       401:
 *         description: Unauthorized
 */
router.get('/points/history', BalanceController.getPointHistory);

// Get credit history
/**
 * @swagger
 * /api/balance/credits/history:
 *   get:
 *     summary: Get credit transaction history
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Credit transaction history
 *       401:
 *         description: Unauthorized
 */
router.get('/credits/history', BalanceController.getCreditHistory);

// Check affordability
/**
 * @swagger
 * /api/balance/check-affordability:
 *   post:
 *     summary: Check if user can afford a specific amount
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               amount:
 *                 type: number
 *               currency:
 *                 type: string
 *                 enum: [points, credits]
 *     responses:
 *       200:
 *         description: Affordability check result
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post('/check-affordability', BalanceController.checkAffordability);

// Get balance estimation
/**
 * @swagger
 * /api/balance/estimate:
 *   get:
 *     summary: Get balance with cost estimation
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Balance with cost estimates
 *       401:
 *         description: Unauthorized
 */
router.get('/estimate', BalanceController.getBalanceForCostEstimation);

module.exports = router;
