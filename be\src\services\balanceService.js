const db = require('../config/database');
const CurrencyUtils = require('../utils/currencyUtils');

class BalanceService {
  
  /**
   * Get user's current balances
   * @param {number} userId 
   * @returns {object} User balance information
   */
  static async getUserBalance(userId) {
    try {
      const [rows] = await db.execute(
        'SELECT point_balance, credit_balance, total_points_earned, total_credits_purchased FROM user WHERE user_id = ?',
        [userId]
      );

      if (rows.length === 0) {
        throw new Error('User not found');
      }

      const balance = rows[0];
      return {
        pointBalance: parseFloat(balance.point_balance),
        creditBalance: parseFloat(balance.credit_balance),
        totalBalance: parseFloat(balance.point_balance) + parseFloat(balance.credit_balance),
        totalPointsEarned: parseFloat(balance.total_points_earned),
        totalCreditsPurchased: parseFloat(balance.total_credits_purchased),
        formattedBalance: CurrencyUtils.formatBalance(balance.point_balance, balance.credit_balance)
      };
    } catch (error) {
      console.error('Error getting user balance:', error);
      throw error;
    }
  }

  /**
   * Add points to user account
   * @param {number} userId 
   * @param {number} amount 
   * @param {string} description 
   * @param {string} referenceId 
   * @param {string} referenceType 
   * @param {Date} expiresAt 
   * @param {number} createdBy 
   */
  static async addPoints(userId, amount, description, referenceId = null, referenceType = null, expiresAt = null, createdBy = null) {
    try {
      await db.execute(
        'CALL AddPointsToUser(?, ?, ?, ?, ?, ?, ?)',
        [userId, amount, description, referenceId, referenceType, expiresAt, createdBy]
      );

      return await this.getUserBalance(userId);
    } catch (error) {
      console.error('Error adding points:', error);
      throw error;
    }
  }

  /**
   * Add credits to user account
   * @param {number} userId 
   * @param {number} amount 
   * @param {string} description 
   * @param {string} referenceId 
   * @param {string} referenceType 
   * @param {string} paymentMethod 
   * @param {string} paymentReference 
   * @param {number} createdBy 
   */
  static async addCredits(userId, amount, description, referenceId = null, referenceType = null, paymentMethod = null, paymentReference = null, createdBy = null) {
    try {
      await db.execute(
        'CALL AddCreditsToUser(?, ?, ?, ?, ?, ?, ?, ?)',
        [userId, amount, description, referenceId, referenceType, paymentMethod, paymentReference, createdBy]
      );

      return await this.getUserBalance(userId);
    } catch (error) {
      console.error('Error adding credits:', error);
      throw error;
    }
  }

  /**
   * Use user balance (points first, then credits)
   * @param {number} userId 
   * @param {number} requiredAmount 
   * @param {string} description 
   * @param {string} referenceId 
   * @param {string} referenceType 
   * @returns {object} Usage result
   */
  static async useBalance(userId, requiredAmount, description, referenceId = null, referenceType = null) {
    try {
      const [result] = await db.execute(
        'CALL UseUserBalance(?, ?, ?, ?, ?, @points_used, @credits_used, @success, @message)',
        [userId, requiredAmount, description, referenceId, referenceType]
      );

      // Get the output variables
      const [outputRows] = await db.execute(
        'SELECT @points_used as points_used, @credits_used as credits_used, @success as success, @message as message'
      );

      const output = outputRows[0];
      
      if (!output.success) {
        throw new Error(output.message);
      }

      return {
        success: true,
        pointsUsed: parseFloat(output.points_used),
        creditsUsed: parseFloat(output.credits_used),
        message: output.message,
        generatesCommission: CurrencyUtils.shouldGenerateCommission(output.credits_used),
        breakdown: CurrencyUtils.formatBalanceBreakdown(
          output.points_used, 
          output.credits_used, 
          requiredAmount
        )
      };
    } catch (error) {
      console.error('Error using balance:', error);
      throw error;
    }
  }

  /**
   * Check if user can afford an amount
   * @param {number} userId 
   * @param {number} requiredAmount 
   * @returns {object} Affordability check result
   */
  static async canAfford(userId, requiredAmount) {
    try {
      const balance = await this.getUserBalance(userId);
      const usage = CurrencyUtils.calculateBalanceUsage(
        requiredAmount, 
        balance.pointBalance, 
        balance.creditBalance
      );

      return {
        canAfford: usage.canAfford,
        totalAvailable: balance.totalBalance,
        requiredAmount,
        shortfall: usage.shortfall || 0,
        pointsToUse: usage.pointsToUse,
        creditsToUse: usage.creditsToUse,
        willGenerateCommission: CurrencyUtils.shouldGenerateCommission(usage.creditsToUse)
      };
    } catch (error) {
      console.error('Error checking affordability:', error);
      throw error;
    }
  }

  /**
   * Get point transaction history
   * @param {number} userId 
   * @param {number} limit 
   * @param {number} offset 
   */
  static async getPointHistory(userId, limit = 50, offset = 0) {
    try {
      console.log('🔍 Debug getPointHistory - userId:', userId, 'type:', typeof userId);
      console.log('🔍 Debug getPointHistory - limit:', limit, 'type:', typeof limit);
      console.log('🔍 Debug getPointHistory - offset:', offset, 'type:', typeof offset);
      
      // Ensure parameters are proper types and validate
      const userIdNum = parseInt(userId);
      const limitNum = Math.max(1, Math.min(1000, parseInt(limit) || 50)); // Clamp between 1-1000
      const offsetNum = Math.max(0, parseInt(offset) || 0); // Minimum 0
      
      console.log('🔍 Debug getPointHistory - converted params:', [userIdNum, limitNum, offsetNum]);
      
      const [rows] = await db.execute(
        `SELECT pt.*, u.name as created_by_name 
         FROM point_transactions pt 
         LEFT JOIN user u ON pt.created_by = u.user_id 
         WHERE pt.user_id = ? 
         ORDER BY pt.created_at DESC 
         LIMIT ${limitNum} OFFSET ${offsetNum}`,
        [userIdNum]
      );

      return rows.map(row => ({
        ...row,
        formattedAmount: CurrencyUtils.formatIDR(row.amount),
        formattedBalanceBefore: CurrencyUtils.formatIDR(row.balance_before),
        formattedBalanceAfter: CurrencyUtils.formatIDR(row.balance_after)
      }));
    } catch (error) {
      console.error('Error getting point history:', error);
      throw error;
    }
  }

  /**
   * Get credit transaction history
   * @param {number} userId 
   * @param {number} limit 
   * @param {number} offset 
   */
  static async getCreditHistory(userId, limit = 50, offset = 0) {
    try {
      // Ensure parameters are proper types and validate
      const userIdNum = parseInt(userId);
      const limitNum = Math.max(1, Math.min(1000, parseInt(limit) || 50)); // Clamp between 1-1000
      const offsetNum = Math.max(0, parseInt(offset) || 0); // Minimum 0
      
      const [rows] = await db.execute(
        `SELECT ct.*, u.name as created_by_name 
         FROM credit_transactions ct 
         LEFT JOIN user u ON ct.created_by = u.user_id 
         WHERE ct.user_id = ? 
         ORDER BY ct.created_at DESC 
         LIMIT ${limitNum} OFFSET ${offsetNum}`,
        [userIdNum]
      );

      return rows.map(row => ({
        ...row,
        formattedAmount: CurrencyUtils.formatIDR(row.amount),
        formattedBalanceBefore: CurrencyUtils.formatIDR(row.balance_before),
        formattedBalanceAfter: CurrencyUtils.formatIDR(row.balance_after)
      }));
    } catch (error) {
      console.error('Error getting credit history:', error);
      throw error;
    }
  }

  /**
   * Get balance summary with recent transactions
   * @param {number} userId 
   */
  static async getBalanceSummary(userId) {
    try {
      const balance = await this.getUserBalance(userId);
      const recentPoints = await this.getPointHistory(userId, 10, 0);
      const recentCredits = await this.getCreditHistory(userId, 10, 0);

      return {
        balance,
        recentPointTransactions: recentPoints,
        recentCreditTransactions: recentCredits
      };
    } catch (error) {
      console.error('Error getting balance summary:', error);
      throw error;
    }
  }

  /**
   * Give welcome bonus to new user
   * @param {number} userId 
   */
  static async giveWelcomeBonus(userId) {
    try {
      const welcomeBonusAmount = 50000; // 50,000 IDR
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 1); // Expire in 1 year

      return await this.addPoints(
        userId,
        welcomeBonusAmount,
        'Welcome bonus for new user registration',
        `welcome_${userId}`,
        'welcome_bonus',
        expiryDate,
        null
      );
    } catch (error) {
      console.error('Error giving welcome bonus:', error);
      throw error;
    }
  }

  /**
   * Process top-up (add credits)
   * @param {number} userId 
   * @param {number} amount 
   * @param {string} paymentMethod 
   * @param {string} paymentReference 
   */
  static async processTopUp(userId, amount, paymentMethod, paymentReference) {
    try {
      return await this.addCredits(
        userId,
        amount,
        `Top-up via ${paymentMethod}`,
        paymentReference,
        'top_up',
        paymentMethod,
        paymentReference,
        null
      );
    } catch (error) {
      console.error('Error processing top-up:', error);
      throw error;
    }
  }
}

module.exports = BalanceService;
