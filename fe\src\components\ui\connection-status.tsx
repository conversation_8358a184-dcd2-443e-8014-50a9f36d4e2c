"use client";

import React from 'react';
import { WifiOff, AlertCircle, CheckCircle } from 'lucide-react';

interface ConnectionStatusProps {
  isConnected: boolean;
  connectionError: string | null;
  onReconnect?: () => void;
  className?: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  connectionError,
  onReconnect,
  className = ''
}) => {
  const getStatusConfig = () => {
    if (connectionError) {
      const isAuthError = connectionError.includes('log in') || connectionError.includes('Authentication');
      return {
        icon: AlertCircle,
        text: isAuthError ? 'Authentication Required' : 'Connection Error',
        color: isAuthError ? 'text-yellow-600' : 'text-red-600',
        bgColor: isAuthError ? 'bg-yellow-50' : 'bg-red-50',
        borderColor: isAuthError ? 'border-yellow-200' : 'border-red-200',
        detail: connectionError
      };
    }

    if (isConnected) {
      return {
        icon: CheckCircle,
        text: 'Connected',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        detail: 'Real-time chat enabled'
      };
    }

    return {
      icon: WifiOff,
      text: 'Connecting...',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      detail: 'Establishing connection'
    };
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg border ${config.bgColor} ${config.borderColor} ${className}`}>
      <Icon className={`w-4 h-4 ${config.color}`} />
      <div className="flex-1 min-w-0">
        <div className={`text-sm font-medium ${config.color}`}>
          {config.text}
        </div>
        <div className="text-xs text-gray-500 truncate">
          {config.detail}
        </div>
      </div>
      {!isConnected && !connectionError && (
        <div className="flex space-x-1">
          <div className="w-1 h-1 bg-yellow-500 rounded-full animate-bounce"></div>
          <div className="w-1 h-1 bg-yellow-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-1 h-1 bg-yellow-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      )}
      {connectionError && onReconnect && (
        <button
          onClick={onReconnect}
          className="text-xs px-2 py-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors"
        >
          Retry
        </button>
      )}
    </div>
  );
};