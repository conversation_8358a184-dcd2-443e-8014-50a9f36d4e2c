const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const pool = require('../config/database');
const affiliateService = require('./affiliateService');

class UserService {
  // Generate OTP code
  generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
  }

  // Generate unique token
  generateToken() {
    return uuidv4();
  }

  // Hash password
  async hashPassword(password) {
    return await bcrypt.hash(password, 10);
  }

  // Compare password
  async comparePassword(password, hash) {
    return await bcrypt.compare(password, hash);
  }

  // Check if user exists by phone
  async getUserByPhone(phone) {
    const [rows] = await pool.execute(
      'SELECT * FROM user WHERE phone = ?',
      [phone]
    );
    return rows[0] || null;
  }

  // Check if user exists by email
  async getUserByEmail(email) {
    const [rows] = await pool.execute(
      'SELECT * FROM user WHERE email = ?',
      [email]
    );
    return rows[0] || null;
  }

  // Register new user
  async registerUser(userData, visitorId = null) {
    const { name, email, phone, password, referralCode } = userData;

    // Check if user already exists
    const existingUserByPhone = await this.getUserByPhone(phone);
    if (existingUserByPhone) {
      throw new Error('User with this phone number already exists');
    }

    const existingUserByEmail = await this.getUserByEmail(email);
    if (existingUserByEmail) {
      throw new Error('User with this email already exists');
    }

    // Validate referral code if provided
    let referrerId = null;
    if (referralCode) {
      const referrerResult = await affiliateService.getUserByReferralCode(referralCode);
      if (!referrerResult.success) {
        throw new Error('Invalid referral code');
      }
      referrerId = referrerResult.user.user_id;
    }

    // Hash password
    const hashedPassword = await this.hashPassword(password);

    // Insert user with referral information
    const [result] = await pool.execute(
      'INSERT INTO user (phone, name, email, password, referred_by) VALUES (?, ?, ?, ?, ?)',
      [phone, name, email, hashedPassword, referrerId]
    );

    const newUserId = result.insertId;

    // Generate referral code for the new user
    await affiliateService.generateReferralCode(newUserId);

    // If visitor ID provided, convert the visitor to user
    if (visitorId) {
      const conversionResult = await affiliateService.convertVisitorToUser(visitorId, newUserId);
      if (conversionResult.success) {
        // Update user with affiliate referrer if conversion found a different referrer
        if (conversionResult.affiliateUserId && !referrerId) {
          await pool.execute(
            'UPDATE user SET referred_by = ? WHERE user_id = ?',
            [conversionResult.affiliateUserId, newUserId]
          );
          referrerId = conversionResult.affiliateUserId;
        }
      }
    }

    return {
      user_id: newUserId,
      phone,
      name,
      email,
      referred_by: referrerId
    };
  }

  // Generate and store OTP
  async generateAndStoreOTP(phone) {
    const code = this.generateOTP();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Delete any existing unused OTP for this phone
    await pool.execute(
      'DELETE FROM otp_codes WHERE phone = ? AND is_used = FALSE',
      [phone]
    );

    // Insert new OTP
    await pool.execute(
      'INSERT INTO otp_codes (phone, code, expires_at) VALUES (?, ?, ?)',
      [phone, code, expiresAt]
    );

    return code;
  }

  // Verify OTP
  async verifyOTP(phone, code) {
    const [rows] = await pool.execute(
      'SELECT * FROM otp_codes WHERE phone = ? AND code = ? AND is_used = FALSE AND expires_at > NOW()',
      [phone, code]
    );

    if (rows.length === 0) {
      throw new Error('Invalid or expired OTP');
    }

    // Mark OTP as used
    await pool.execute(
      'UPDATE otp_codes SET is_used = TRUE WHERE phone = ? AND code = ?',
      [phone, code]
    );

    // Mark user as verified and generate token
    const token = this.generateToken();
    await pool.execute(
      'UPDATE user SET is_verified = TRUE, token = ? WHERE phone = ?',
      [token, phone]
    );

    // Get updated user data
    const user = await this.getUserByPhone(phone);
    
    // Give welcome bonus for new verified users
    try {
      const BalanceService = require('./balanceService');
      await BalanceService.giveWelcomeBonus(user.user_id);
    } catch (error) {
      console.error('Failed to give welcome bonus:', error);
      // Don't fail the verification process if welcome bonus fails
    }
    
    return {
      user_id: user.user_id,
      phone: user.phone,
      name: user.name,
      email: user.email,
      token: user.token
    };
  }

  // Login user
  async loginUser(phone, password) {
    const user = await this.getUserByPhone(phone);
    if (!user) {
      throw new Error('User not found');
    }

    if (!user.is_verified) {
      throw new Error('Account not verified. Please verify your phone number first.');
    }

    const isPasswordValid = await this.comparePassword(password, user.password);
    if (!isPasswordValid) {
      throw new Error('Invalid password');
    }

    // Generate new token
    const token = this.generateToken();
    await pool.execute(
      'UPDATE user SET token = ? WHERE phone = ?',
      [token, phone]
    );

    return {
      user_id: user.user_id,
      phone: user.phone,
      name: user.name,
      email: user.email,
      token
    };
  }

  // Get user by token
  async getUserByToken(token) {
    const [rows] = await pool.execute(
      'SELECT user_id, phone, name, email FROM user WHERE token = ? AND is_verified = TRUE',
      [token]
    );
    return rows[0] || null;
  }

  // Reset password
  async resetPassword(phone, newPassword) {
    const user = await this.getUserByPhone(phone);
    if (!user) {
      throw new Error('User not found');
    }

    const hashedPassword = await this.hashPassword(newPassword);
    await pool.execute(
      'UPDATE user SET password = ? WHERE phone = ?',
      [hashedPassword, phone]
    );

    return { message: 'Password reset successfully' };
  }

  // Update user profile
  async updateUserProfile(userId, profileData) {
    const { name, email, bank_name, account_holder_name, account_number } = profileData;

    // Check if email is already used by another user
    if (email) {
      const existingUser = await this.getUserByEmail(email);
      if (existingUser && existingUser.user_id !== userId) {
        throw new Error('Email already exists');
      }
    }

    // Update user profile
    const [result] = await pool.execute(
      `UPDATE user SET 
       name = ?, 
       email = ?, 
       bank_name = ?, 
       account_holder_name = ?, 
       account_number = ?, 
       updated_at = CURRENT_TIMESTAMP 
       WHERE user_id = ?`,
      [name, email, bank_name, account_holder_name, account_number, userId]
    );

    if (result.affectedRows === 0) {
      throw new Error('User not found');
    }

    // Return updated user data
    const [rows] = await pool.execute(
      'SELECT user_id, phone, name, email, bank_name, account_holder_name, account_number, created_at, updated_at FROM user WHERE user_id = ?',
      [userId]
    );

    return rows[0];
  }

  // Change user password
  async changePassword(userId, currentPassword, newPassword) {
    // Get current user data
    const [rows] = await pool.execute(
      'SELECT password FROM user WHERE user_id = ?',
      [userId]
    );

    if (rows.length === 0) {
      throw new Error('User not found');
    }

    const user = rows[0];

    // Verify current password
    const isCurrentPasswordValid = await this.comparePassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new Error('Invalid current password');
    }

    // Hash new password
    const hashedNewPassword = await this.hashPassword(newPassword);

    // Update password
    const [result] = await pool.execute(
      'UPDATE user SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?',
      [hashedNewPassword, userId]
    );

    if (result.affectedRows === 0) {
      throw new Error('Failed to update password');
    }

    return { message: 'Password changed successfully' };
  }

  // Generate WhatsApp OTP link
  generateWhatsAppLink(phone, code) {
    const adminPhone = '6282139817939'; // Admin WhatsApp number
    const message = `Kode OTP verifikasi AI Trainer Hub: ${code}`;
    return `https://wa.me/${adminPhone}?text=${encodeURIComponent(message)}`;
  }
}

module.exports = new UserService();
