const express = require('express');
const affiliateController = require('../../controllers/affiliateController');
const { authenticateToken } = require('../../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Affiliate
 *   description: Affiliate program management
 */

// Generate referral code
/**
 * @swagger
 * /api/affiliate/generate-code:
 *   post:
 *     summary: Generate referral code for affiliate
 *     tags: [Affiliate]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Referral code generated
 *       401:
 *         description: Unauthorized
 */
router.post('/generate-code', authenticateToken, affiliateController.generateReferralCode);

// Get affiliate stats
/**
 * @swagger
 * /api/affiliate/stats:
 *   get:
 *     summary: Get affiliate statistics
 *     tags: [Affiliate]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Affiliate statistics
 *       401:
 *         description: Unauthorized
 */
router.get('/stats', authenticateToken, affiliateController.getAffiliateStats);

// Get referral stats
/**
 * @swagger
 * /api/affiliate/referrals:
 *   get:
 *     summary: Get referral statistics
 *     tags: [Affiliate]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Referral statistics
 *       401:
 *         description: Unauthorized
 */
router.get('/referrals', authenticateToken, affiliateController.getReferralStats);

// Get commission history
/**
 * @swagger
 * /api/affiliate/commissions:
 *   get:
 *     summary: Get commission history
 *     tags: [Affiliate]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Commission history
 *       401:
 *         description: Unauthorized
 */
router.get('/commissions', authenticateToken, affiliateController.getCommissionHistory);

// Set referral
/**
 * @swagger
 * /api/affiliate/set-referral:
 *   post:
 *     summary: Set referral for user
 *     tags: [Affiliate]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               referralCode:
 *                 type: string
 *     responses:
 *       200:
 *         description: Referral set successfully
 *       400:
 *         description: Invalid referral code
 *       401:
 *         description: Unauthorized
 */
router.post('/set-referral', authenticateToken, affiliateController.setReferral);

// Get dashboard
/**
 * @swagger
 * /api/affiliate/dashboard:
 *   get:
 *     summary: Get affiliate dashboard data
 *     tags: [Affiliate]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard data
 *       401:
 *         description: Unauthorized
 */
router.get('/dashboard', authenticateToken, affiliateController.getDashboard);

module.exports = router;
