// Utility functions for affiliate tracking
export class AffiliateTracker {
  static COOKIE_NAMES = {
    VISITOR_ID: 'affiliate_visitor_id',
    REFERRAL_CODE: 'affiliate_referral_code'
  };

  /**
   * Track affiliate visit from URL parameter
   */
  static async trackFromUrl() {
    if (typeof window === 'undefined') return;

    const urlParams = new URLSearchParams(window.location.search);
    const refCode = urlParams.get('ref');

    if (refCode) {
      try {
        const response = await fetch(`/api/affiliate/track/${refCode}`, {
          method: 'GET',
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();
          console.log('Affiliate tracking successful:', data);
          
          // Show notification to user (optional)
          this.showReferralNotification(refCode);
        }
      } catch (error) {
        console.error('Affiliate tracking failed:', error);
      }
    }
  }

  /**
   * Get current visitor tracking info
   */
  static async getVisitorInfo() {
    if (typeof window === 'undefined') return null;

    try {
      const response = await fetch('/api/affiliate/visitor-info', {
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      }
    } catch (error) {
      console.error('Failed to get visitor info:', error);
    }

    return null;
  }

  /**
   * Check if user has active referral tracking
   */
  static async hasActiveReferral() {
    const info = await this.getVisitorInfo();
    return info?.hasTracking || false;
  }

  /**
   * Get referral code from tracking
   */
  static async getReferralCode() {
    const info = await this.getVisitorInfo();
    return info?.referralCode || null;
  }

  /**
   * Show referral notification
   */
  static showReferralNotification(referralCode: string) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'affiliate-notification';
    notification.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 16px 24px;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 350px;
        animation: slideIn 0.3s ease-out;
      ">
        <div style="display: flex; align-items: center; gap: 12px;">
          <div style="
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
          ">🎁</div>
          <div>
            <div style="font-weight: 600; font-size: 14px; margin-bottom: 4px;">
              Welcome! You're referred by ${referralCode}
            </div>
            <div style="font-size: 12px; opacity: 0.9;">
              Register now to get special benefits!
            </div>
          </div>
          <button onclick="this.parentElement.parentElement.parentElement.remove()" style="
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 4px;
            opacity: 0.7;
          ">×</button>
        </div>
      </div>
    `;

    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(notification);

    // Auto remove after 10 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 10000);
  }

  /**
   * Initialize tracking on page load
   */
  static init() {
    if (typeof window === 'undefined') return;

    // Track on page load
    this.trackFromUrl();

    // Also track on navigation changes (for SPAs)
    window.addEventListener('popstate', () => {
      this.trackFromUrl();
    });
  }
}

// Auto-initialize if in browser
if (typeof window !== 'undefined') {
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => AffiliateTracker.init());
  } else {
    AffiliateTracker.init();
  }
}
