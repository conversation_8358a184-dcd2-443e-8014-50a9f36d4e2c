// Currency conversion utilities
class CurrencyUtils {
  static USD_TO_IDR = 20000; // $1 = Rp 20,000

  static convertUSDToIDR(amountUSD) {
    return amountUSD * this.USD_TO_IDR;
  }

  static convertIDRToUSD(amountIDR) {
    return amountIDR / this.USD_TO_IDR;
  }

  static formatIDR(amount) {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  static formatUSD(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount);
  }

  static roundIDR(amount) {
    // Round to the nearest Rupiah
    return Math.round(amount);
  }

  static roundUSD(amount) {
    // Round to 6 decimal places for USD
    return Math.round(amount * 1000000) / 1000000;
  }

  static formatPricingBreakdown(costBreakdown, currency = 'IDR') {
    const formatFn = currency === 'IDR' ? this.formatIDR : this.formatUSD;
    
    return {
      basePrice: formatFn(costBreakdown.basePrice),
      platformCommission: formatFn(costBreakdown.platformCommission),
      expertCommission: formatFn(costBreakdown.expertCommission),
      totalPrice: formatFn(costBreakdown.totalPrice),
      pricingPercentage: `${costBreakdown.pricingPercentage}%`,
      currency: currency
    };
  }

  static calculateMarkup(basePrice, pricingPercentage) {
    const platformCommission = basePrice; // 100% platform commission
    const expertCommission = (pricingPercentage / 100) * basePrice;
    const totalPrice = basePrice + platformCommission + expertCommission;
    
    return {
      basePrice,
      platformCommission,
      expertCommission,
      totalPrice,
      markup: ((totalPrice - basePrice) / basePrice) * 100 // Total markup percentage
    };
  }

  // Point & Credit System Methods
  static formatBalance(points, credits) {
    const total = points + credits;
    return `${this.formatIDR(total)} (${this.formatIDR(points)} points + ${this.formatIDR(credits)} credits)`;
  }

  static calculateBalanceUsage(requiredAmount, pointBalance, creditBalance) {
    const totalAvailable = pointBalance + creditBalance;
    
    if (totalAvailable < requiredAmount) {
      return {
        pointsToUse: 0,
        creditsToUse: 0,
        canAfford: false,
        shortfall: requiredAmount - totalAvailable
      };
    }
    
    let pointsToUse = 0;
    let creditsToUse = 0;
    let remaining = requiredAmount;
    
    // Use points first
    if (pointBalance > 0 && remaining > 0) {
      pointsToUse = Math.min(pointBalance, remaining);
      remaining -= pointsToUse;
    }
    
    // Use credits for remaining amount
    if (remaining > 0) {
      creditsToUse = remaining;
    }
    
    return {
      pointsToUse,
      creditsToUse,
      canAfford: true,
      shortfall: 0
    };
  }

  static shouldGenerateCommission(creditsUsed) {
    return creditsUsed > 0;
  }

  static formatBalanceBreakdown(pointsUsed, creditsUsed, totalCost) {
    return {
      pointsUsed: this.formatIDR(pointsUsed),
      creditsUsed: this.formatIDR(creditsUsed),
      totalCost: this.formatIDR(totalCost),
      generatesCommission: this.shouldGenerateCommission(creditsUsed)
    };
  }
}

module.exports = CurrencyUtils;
