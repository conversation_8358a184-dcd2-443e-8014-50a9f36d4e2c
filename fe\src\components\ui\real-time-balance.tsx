"use client";

import React, { useState, useEffect } from 'react';
import { Wallet, TrendingUp, AlertCircle } from 'lucide-react';

interface BalanceUpdate {
  pointsUsed: number;
  creditsUsed: number;
  generatesCommission: boolean;
  breakdown: {
    pointsUsed: number;
    creditsUsed: number;
    totalCostIDR: number;
  };
}

interface RealTimeBalanceProps {
  currentCost: number;
  totalTokens: number;
  balanceUpdate: BalanceUpdate | null;
  isStreaming: boolean;
  className?: string;
}

export const RealTimeBalance: React.FC<RealTimeBalanceProps> = ({
  currentCost,
  totalTokens,
  balanceUpdate,
  isStreaming,
  className = ''
}) => {
  const [animatedCost, setAnimatedCost] = useState(0);
  const [animatedTokens, setAnimatedTokens] = useState(0);

  // Animate cost and token updates
  useEffect(() => {
    if (currentCost > animatedCost) {
      const increment = (currentCost - animatedCost) / 10;
      const timer = setInterval(() => {
        setAnimatedCost(prev => {
          const next = prev + increment;
          if (next >= currentCost) {
            clearInterval(timer);
            return currentCost;
          }
          return next;
        });
      }, 50);
      return () => clearInterval(timer);
    } else {
      setAnimatedCost(currentCost);
    }
  }, [currentCost, animatedCost]);

  useEffect(() => {
    if (totalTokens > animatedTokens) {
      const increment = (totalTokens - animatedTokens) / 10;
      const timer = setInterval(() => {
        setAnimatedTokens(prev => {
          const next = prev + increment;
          if (next >= totalTokens) {
            clearInterval(timer);
            return totalTokens;
          }
          return next;
        });
      }, 50);
      return () => clearInterval(timer);
    } else {
      setAnimatedTokens(totalTokens);
    }
  }, [totalTokens, animatedTokens]);

  const formatIDR = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatTokens = (tokens: number) => {
    return Math.round(tokens).toLocaleString();
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 shadow-sm ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Wallet className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium text-gray-700">
            {isStreaming ? 'Live Cost' : 'Session Cost'}
          </span>
        </div>
        {isStreaming && (
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-green-600">Live</span>
          </div>
        )}
      </div>

      <div className="space-y-2">
        {/* Current Cost */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Current Cost:</span>
          <div className="flex items-center space-x-1">
            {isStreaming && animatedCost > 0 && (
              <TrendingUp className="w-3 h-3 text-orange-500" />
            )}
            <span className={`text-sm font-semibold ${
              isStreaming ? 'text-orange-600' : 'text-gray-900'
            }`}>
              {formatIDR(animatedCost)}
            </span>
          </div>
        </div>

        {/* Token Count */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Tokens Used:</span>
          <span className="text-sm font-medium text-gray-700">
            {formatTokens(animatedTokens)}
          </span>
        </div>

        {/* Balance Usage (when available) */}
        {balanceUpdate && (
          <div className="pt-2 border-t border-gray-100">
            <div className="space-y-1">
              {balanceUpdate.pointsUsed > 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">Points Used:</span>
                  <span className="text-xs font-medium text-red-600">
                    -{balanceUpdate.pointsUsed.toLocaleString()}
                  </span>
                </div>
              )}
              {balanceUpdate.creditsUsed > 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">Credits Used:</span>
                  <span className="text-xs font-medium text-red-600">
                    -{formatIDR(balanceUpdate.creditsUsed)}
                  </span>
                </div>
              )}
              {balanceUpdate.generatesCommission && (
                <div className="flex items-center space-x-1 mt-1">
                  <TrendingUp className="w-3 h-3 text-green-500" />
                  <span className="text-xs text-green-600">
                    Generates commission
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Warning for high costs */}
        {animatedCost > 50000 && (
          <div className="flex items-center space-x-1 pt-2 border-t border-orange-100">
            <AlertCircle className="w-3 h-3 text-orange-500" />
            <span className="text-xs text-orange-600">
              High usage detected
            </span>
          </div>
        )}
      </div>

      {/* Streaming indicator */}
      {isStreaming && (
        <div className="mt-3 pt-2 border-t border-gray-100">
          <div className="flex items-center justify-center space-x-2">
            <div className="flex space-x-1">
              <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce"></div>
              <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span className="text-xs text-blue-600">Processing...</span>
          </div>
        </div>
      )}
    </div>
  );
};