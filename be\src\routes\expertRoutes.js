const express = require('express');
const expertController = require('../controllers/expertController');
const { upload, handleUploadError } = require('../middleware/upload');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Experts
 *   description: AI Expert management and marketplace
 */

/**
 * @swagger
 * /api/experts:
 *   post:
 *     summary: Create a new AI expert
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               avatar:
 *                 type: string
 *                 format: binary
 *     responses:
 *       201:
 *         description: Expert created
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post('/', authenticateToken, upload, handleUploadError, expertController.createExpert.bind(expertController));

/**
 * @swagger
 * /api/models:
 *   get:
 *     summary: Get available AI models
 *     tags: [Experts]
 *     responses:
 *       200:
 *         description: List of models
 */
router.get('/models', expertController.getAvailableModels.bind(expertController));

/**
 * @swagger
 * /api/models/{model}/pricing:
 *   get:
 *     summary: Get pricing for a specific model
 *     tags: [Experts]
 *     parameters:
 *       - in: path
 *         name: model
 *         schema:
 *           type: string
 *         required: true
 *         description: Model name
 *     responses:
 *       200:
 *         description: Model pricing
 *       404:
 *         description: Model not found
 */
router.get('/models/:model/pricing', expertController.getModelPricing.bind(expertController));

/**
 * @swagger
 * /api/calculate-cost:
 *   post:
 *     summary: Calculate cost for a model usage
 *     tags: [Experts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               model:
 *                 type: string
 *               tokens:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Cost calculated
 *       400:
 *         description: Invalid input
 */
router.post('/calculate-cost', expertController.calculateCost.bind(expertController));

/**
 * @swagger
 * /api/experts/public:
 *   get:
 *     summary: Get public AI experts
 *     tags: [Experts]
 *     responses:
 *       200:
 *         description: List of public experts
 */
router.get('/public', expertController.getPublicExperts.bind(expertController));

/**
 * @swagger
 * /api/experts:
 *   get:
 *     summary: List all experts for authenticated user
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of experts
 *       401:
 *         description: Unauthorized
 */
router.get('/', authenticateToken, expertController.listExperts.bind(expertController));

/**
 * @swagger
 * /api/experts/{expertId}:
 *   get:
 *     summary: Get a specific expert by ID
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: expertId
 *         schema:
 *           type: string
 *         required: true
 *         description: Expert ID
 *     responses:
 *       200:
 *         description: Expert data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Expert not found
 */
router.get('/:expertId', authenticateToken, expertController.getExpert.bind(expertController));

/**
 * @swagger
 * /api/experts/{expertId}/stats:
 *   get:
 *     summary: Get statistics for a specific expert
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: expertId
 *         schema:
 *           type: string
 *         required: true
 *         description: Expert ID
 *     responses:
 *       200:
 *         description: Expert statistics
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Expert not found
 */
router.get('/:expertId/stats', authenticateToken, expertController.getExpertStats.bind(expertController));

/**
 * @swagger
 * /api/experts/{expertId}:
 *   put:
 *     summary: Update an expert by ID
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: expertId
 *         schema:
 *           type: string
 *         required: true
 *         description: Expert ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               avatar:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Expert updated
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Expert not found
 */
router.put('/:expertId', authenticateToken, upload, handleUploadError, expertController.updateExpert.bind(expertController));

module.exports = router;