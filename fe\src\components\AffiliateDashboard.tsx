import React, { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Copy,
  DollarSign,
  Users,
  TrendingUp,
  Gift,
  Share2,
  Trophy,
} from "lucide-react";
import { axiosInstance } from "@/lib/api";

interface AffiliateStats {
  userId: number;
  name: string;
  email: string;
  referralCode: string;
  totalReferrals: number;
  totalCommissions: number;
  totalCommissionEarned: number;
  last30DaysCommission: number;
  last7DaysCommission: number;
  lastCommissionDate: string | null;
  totalCommissionEarnedFormatted: string;
  last30DaysCommissionFormatted: string;
  last7DaysCommissionFormatted: string;
}

interface ReferralStats {
  referredUserId: number;
  referredUserName: string;
  referredUserEmail: string;
  referralDate: string;
  totalSessions: number;
  totalMessages: number;
  totalTokensUsed: number;
  totalCostGenerated: number;
  totalCommissionGenerated: number;
  totalCostGeneratedFormatted: string;
  totalCommissionGeneratedFormatted: string;
}

interface Commission {
  id: number;
  referredUserName: string;
  referredUserEmail: string;
  expertName?: string;
  expertOwnerName?: string;
  commissionType: "direct_usage" | "expert_usage";
  baseCost: number;
  markupCost: number;
  commissionAmount: number;
  commissionRate: number;
  tokensUsed: number;
  status: string;
  createdAt: string;
  baseCostFormatted: string;
  commissionAmountFormatted: string;
}

interface AffiliateDashboardProps {
  onClose?: () => void;
}



const AffiliateDashboard: React.FC<AffiliateDashboardProps> = ({ onClose }) => {
  const [stats, setStats] = useState<AffiliateStats | null>(null);
  const [referrals, setReferrals] = useState<ReferralStats[]>([]);
  const [commissions, setCommissions] = useState<Commission[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      localStorage.getItem("token");

      const { data } = await axiosInstance.get(`/api/affiliate/dashboard`);

      setStats(data.dashboard.stats);
      setReferrals(data.dashboard.referrals || []);
      setCommissions(data.dashboard.recentCommissions || []);
    } catch (error) {
      console.error("Error loading affiliate dashboard:", error);
      alert("Failed to load affiliate data");
    } finally {
      setLoading(false);
    }
  };

  const generateReferralCode = async () => {
    try {
      localStorage.getItem("token");

      const { data } = await axiosInstance.post(`/api/affiliate/generate-code`);

      setStats((prev) =>
        prev ? { ...prev, referralCode: data.referralCode } : null
      );
      alert("Referral code generated successfully!");
    } catch (error) {
      console.error("Error generating referral code:", error);
      alert("Failed to generate referral code");
    }
  };

  const copyReferralCode = () => {
    if (stats?.referralCode) {
      navigator.clipboard.writeText(stats.referralCode);
      alert("Referral code copied to clipboard!");
    }
  };

  const copyReferralLink = () => {
    if (stats?.referralCode) {
      const referralLink = `${window.location.origin}/register?ref=${stats.referralCode}`;
      navigator.clipboard.writeText(referralLink);
      alert("Referral link copied to clipboard!");
    }
  };

  const shareReferralLink = async () => {
    if (stats?.referralCode) {
      const referralLink = `${window.location.origin}/register?ref=${stats.referralCode}`;

      if (navigator.share) {
        try {
          await navigator.share({
            title: "Join AI Trainer Hub",
            text: "Get started with AI experts and earn rewards!",
            url: referralLink,
          });
        } catch {
          // User cancelled sharing
        }
      } else {
        copyReferralLink();
      }
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading affiliate dashboard...</p>
        </div>
      </div>
    );
  }

  const referralLink = stats?.referralCode
    ? `${window.location.origin}/register?ref=${stats.referralCode}`
    : "";

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">
            Affiliate Dashboard
          </h2>
          <p className="text-gray-600 mt-1">
            Earn 25% commission from your referrals
          </p>
        </div>
        {onClose && (
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        )}
      </div>

      {/* Referral Code Section */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
              <Gift className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">
                Your Referral Code
              </h3>
              <p className="text-gray-600">
                Share this code to earn commissions
              </p>
            </div>
          </div>
        </div>

        {stats?.referralCode ? (
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="flex-1">
                <Label htmlFor="referralCode">Referral Code</Label>
                <div className="flex gap-2 mt-1">
                  <Input
                    id="referralCode"
                    value={stats.referralCode}
                    readOnly
                    className="font-mono text-lg"
                  />
                  <Button
                    onClick={copyReferralCode}
                    variant="outline"
                    size="sm"
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex-1">
              <Label htmlFor="referralLink">Referral Link</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  id="referralLink"
                  value={referralLink}
                  readOnly
                  className="text-sm"
                />
                <Button onClick={copyReferralLink} variant="outline" size="sm">
                  <Copy className="w-4 h-4" />
                </Button>
                <Button onClick={shareReferralLink} variant="outline" size="sm">
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-4">
            <p className="text-gray-600 mb-4">
              You don't have a referral code yet
            </p>
            <Button
              onClick={generateReferralCode}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Gift className="w-4 h-4 mr-2" />
              Generate Referral Code
            </Button>
          </div>
        )}
      </Card>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Total Referrals
              </p>
              <p className="text-3xl font-bold text-gray-900">
                {stats?.totalReferrals || 0}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Total Commissions
              </p>
              <p className="text-3xl font-bold text-gray-900">
                {stats?.totalCommissions || 0}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <Trophy className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Earned</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.totalCommissionEarnedFormatted || "Rp 0"}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Last 30 Days</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats?.last30DaysCommissionFormatted || "Rp 0"}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="referrals">My Referrals</TabsTrigger>
          <TabsTrigger value="commissions">Commission History</TabsTrigger>
        </TabsList>

        <TabsContent value="referrals" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Referral Performance</h3>
            {referrals.length > 0 ? (
              <div className="space-y-4">
                {referrals.map((referral) => (
                  <div
                    key={referral.referredUserId}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-semibold text-gray-900">
                          {referral.referredUserName}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {referral.referredUserEmail}
                        </p>
                        <p className="text-xs text-gray-500">
                          Joined:{" "}
                          {new Date(referral.referralDate).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <span className="inline-block px-2 py-1 bg-green-100 text-green-800 text-sm rounded">
                          {referral.totalCommissionGeneratedFormatted}
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Sessions</p>
                        <p className="font-medium">{referral.totalSessions}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Messages</p>
                        <p className="font-medium">{referral.totalMessages}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Tokens</p>
                        <p className="font-medium">
                          {referral.totalTokensUsed.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No referrals yet</p>
                <p className="text-sm text-gray-500">
                  Share your referral code to start earning!
                </p>
              </div>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="commissions" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Recent Commissions</h3>
            {commissions.length > 0 ? (
              <div className="space-y-4">
                {commissions.map((commission) => (
                  <div
                    key={commission.id}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-gray-900">
                            {commission.referredUserName}
                          </h4>
                          <span
                            className={`inline-block px-2 py-1 text-xs rounded ${
                              commission.commissionType === "direct_usage"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-purple-100 text-purple-800"
                            }`}
                          >
                            {commission.commissionType === "direct_usage"
                              ? "Direct Usage"
                              : "Expert Usage"}
                          </span>
                        </div>
                        {commission.expertName && (
                          <p className="text-sm text-gray-600">
                            Expert: {commission.expertName}
                          </p>
                        )}
                        <p className="text-xs text-gray-500">
                          {new Date(commission.createdAt).toLocaleString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-600">
                          {commission.commissionAmountFormatted}
                        </p>
                        <p className="text-xs text-gray-500">
                          {commission.commissionRate}% commission
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>Base Cost: {commission.baseCostFormatted}</span>
                      <span>
                        Tokens: {commission.tokensUsed.toLocaleString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No commissions yet</p>
                <p className="text-sm text-gray-500">
                  Start referring users to earn commissions!
                </p>
              </div>
            )}
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AffiliateDashboard;
