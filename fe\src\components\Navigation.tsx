"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useState } from "react";
import {
  ChevronDown,
  User,
  Wallet,
  LogOut,
  LogIn,
  UserPlus,
  Users,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const Navigation = () => {
  const pathname = usePathname();
  const router = useRouter();
  const { user, isAuthenticated, logout } = useAuth();
  const [showExpertsDropdown, setShowExpertsDropdown] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);

  const navItems = [
    { href: "/ai-experts", label: "AI Experts" },
    // History menu only for authenticated users
    ...(isAuthenticated ? [{ href: "/history", label: "History" }] : []),
  ];

  const expertItems = [
    { href: "/experts?view=overview", label: "Overview" },
    { href: "/experts?view=manage", label: "Manage Expert" },
  ];

  const userMenuItems = [
    { href: "/profile", label: "My Profile", icon: User },
    { href: "/affiliate", label: "Affiliate Program", icon: Users },
    { href: "/balance", label: "Saldo", icon: Wallet },
  ];

  const handleLogout = async () => {
    await logout();
    setShowUserDropdown(false);
    router.push("/");
  };

  return (
    <nav className="bg-white shadow-lg border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-8">
            <Link
              href="/"
              className="text-2xl font-bold"
              style={{ color: "#1E3A8A" }}
            >
              PakarAI
            </Link>

            <div className="flex space-x-6">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    pathname === item.href
                      ? "text-white shadow-lg"
                      : "text-gray-600 hover:text-white hover:shadow-md"
                  }`}
                  style={
                    pathname === item.href
                      ? { backgroundColor: "#1E3A8A" }
                      : { backgroundColor: "transparent" }
                  }
                  onMouseEnter={(e) => {
                    if (pathname !== item.href) {
                      e.currentTarget.style.backgroundColor = "#1E3A8A";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (pathname !== item.href) {
                      e.currentTarget.style.backgroundColor = "transparent";
                    }
                  }}
                >
                  {item.label}
                </Link>
              ))}

              {/* AI Experts Dropdown - only for authenticated users */}
              {isAuthenticated && (
                <div
                  className="relative"
                  onMouseEnter={() => setShowExpertsDropdown(true)}
                  onMouseLeave={() => setShowExpertsDropdown(false)}
                >
                  <button
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-1 ${
                      pathname.startsWith("/experts")
                        ? "text-white shadow-lg"
                        : "text-gray-600 hover:text-white hover:shadow-md"
                    }`}
                    style={
                      pathname.startsWith("/experts")
                        ? { backgroundColor: "#1E3A8A" }
                        : { backgroundColor: "transparent" }
                    }
                    onMouseEnter={(e) => {
                      if (!pathname.startsWith("/experts")) {
                        e.currentTarget.style.backgroundColor = "#1E3A8A";
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!pathname.startsWith("/experts")) {
                        e.currentTarget.style.backgroundColor = "transparent";
                      }
                    }}
                  >
                    My Experts
                    <ChevronDown className="w-4 h-4" />
                  </button>

                  {showExpertsDropdown && (
                    <div className="absolute top-full left-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                      {expertItems.map((item) => (
                        <Link
                          key={item.href}
                          href={item.href}
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                        >
                          {item.label}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* User Menu - Authenticated vs Unauthenticated */}
          {isAuthenticated && user ? (
            <div
              className="relative"
              onMouseEnter={() => setShowUserDropdown(true)}
              onMouseLeave={() => setShowUserDropdown(false)}
            >
              <button className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <span className="hidden md:block">{user.name}</span>
                <ChevronDown className="w-4 h-4" />
              </button>

              {showUserDropdown && (
                <div className="absolute top-full right-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                  <div className="px-4 py-2 border-b border-gray-200">
                    <p className="text-sm font-medium text-gray-900">
                      {user.name}
                    </p>
                    <p className="text-xs text-gray-500">{user.email}</p>
                  </div>

                  <Link
                    href="/dashboard"
                    className="flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                  >
                    <User className="w-4 h-4" />
                    <span>Dashboard</span>
                  </Link>

                  {userMenuItems.map((item) => {
                    const IconComponent = item.icon;
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className="flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                      >
                        <IconComponent className="w-4 h-4" />
                        <span>{item.label}</span>
                      </Link>
                    );
                  })}
                  <hr className="my-1 border-gray-200" />
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors w-full text-left"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Logout</span>
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <Link
                href="/login"
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
              >
                <LogIn className="w-4 h-4" />
                <span>Login</span>
              </Link>
              <Link
                href="/register"
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
              >
                <UserPlus className="w-4 h-4" />
                <span>Sign Up</span>
              </Link>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
