const aiGenerationService = require('../services/aiGenerationService');

class AIGenerationController {
  // Generate labels for expert
  async generateLabels(req, res) {
    try {
      const userId = req.user?.user_id;
      const { name, description, systemPrompt, model } = req.body;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      // Validate required fields
      if (!name || !systemPrompt) {
        return res.status(400).json({
          success: false,
          error: 'Name and system prompt are required for label generation'
        });
      }

      const expertData = {
        name: name.trim(),
        description: description?.trim() || '',
        systemPrompt: systemPrompt.trim(),
        model: model || 'gpt-4o-mini'
      };

      const result = await aiGenerationService.generateLabels(expertData, userId);
      
      res.json(result);

    } catch (error) {
      console.error('Generate labels error:', error);
      
      if (error.message.includes('Insufficient balance')) {
        return res.status(402).json({
          success: false,
          error: 'Insufficient balance',
          message: error.message
        });
      }
      
      res.status(500).json({
        success: false,
        error: 'Failed to generate labels',
        message: error.message
      });
    }
  }

  // Generate image for expert
  async generateImage(req, res) {
    try {
      const userId = req.user?.user_id;
      const { name, description, labels } = req.body;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      // Validate required fields
      if (!name) {
        return res.status(400).json({
          success: false,
          error: 'Name is required for image generation'
        });
      }

      const expertData = {
        name: name.trim(),
        description: description?.trim() || '',
        labels: labels || []
      };

      const result = await aiGenerationService.generateExpertImage(expertData, userId);
      
      res.json(result);

    } catch (error) {
      console.error('Generate image error:', error);
      
      if (error.message.includes('Insufficient balance')) {
        return res.status(402).json({
          success: false,
          error: 'Insufficient balance',
          message: error.message
        });
      }
      
      res.status(500).json({
        success: false,
        error: 'Failed to generate image',
        message: error.message
      });
    }
  }

  // Get cost estimation
  async getCostEstimation(req, res) {
    try {
      const result = await aiGenerationService.getCostEstimation();
      res.json({
        success: true,
        costs: result
      });
    } catch (error) {
      console.error('Get cost estimation error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get cost estimation',
        message: error.message
      });
    }
  }

  // Get user's AI generation history
  async getGenerationHistory(req, res) {
    try {
      const userId = req.user?.user_id;
      const limit = parseInt(req.query.limit) || 50;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      const history = await aiGenerationService.getGenerationHistory(userId, limit);
      
      res.json({
        success: true,
        history: history
      });

    } catch (error) {
      console.error('Get generation history error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get generation history',
        message: error.message
      });
    }
  }

  // Validate labels (for manual editing)
  async validateLabels(req, res) {
    try {
      const { labels } = req.body;

      if (!Array.isArray(labels)) {
        return res.status(400).json({
          success: false,
          error: 'Labels must be an array'
        });
      }

      // Use the same validation logic as the service
      const validatedLabels = labels
        .filter(label => typeof label === 'string' && label.trim().length > 0)
        .map(label => label.trim())
        .filter(label => label.length <= 50)
        .slice(0, 10);

      const invalidLabels = labels.filter(label => 
        typeof label !== 'string' || 
        label.trim().length === 0 || 
        label.trim().length > 50
      );

      res.json({
        success: true,
        validLabels: validatedLabels,
        invalidLabels: invalidLabels,
        totalValid: validatedLabels.length,
        maxAllowed: 10
      });

    } catch (error) {
      console.error('Validate labels error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to validate labels',
        message: error.message
      });
    }
  }
}

module.exports = new AIGenerationController();