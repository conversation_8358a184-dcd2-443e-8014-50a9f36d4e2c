# Design Document - AI Trainer Hub Platform

## Overview

AI Trainer Hub adalah platform marketplace berbasis web yang memungkinkan pengguna untuk membuat, be<PERSON><PERSON>, dan berinteraksi dengan AI experts yang dikustom<PERSON>si. Platform ini mengintegrasikan OpenAI APIs (GPT models, DALL-E, Whisper, TTS) dengan sistem ekonomi berbasis poin/kredit, real-time chat streaming, dan fitur-fitur advanced seperti voice interaction dan AI-generated content.

### Key Design Principles

1. **Modular Architecture**: Setiap komponen dapat dikembangkan dan di-deploy secara independen
2. **Real-time First**: Prioritas pada pengalaman real-time untuk chat dan notifications
3. **AI-Native**: Memanfaatkan AI untuk enhancing user experience (auto-labels, image generation, recommendations)
4. **Scalable Economics**: Sistem revenue sharing yang fair dan transparan
5. **Privacy by Design**: User control penuh atas data sharing dan privacy settings

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        FE[Next.js Frontend]
        PWA[Progressive Web App]
    end
    
    subgraph "Backend Layer"
        API[Express.js API Server]
        WS[WebSocket Server]
        AUTH[Authentication Service]
    end
    
    subgraph "Data Layer"
        DB[(MySQL Database)]
        REDIS[(Redis Cache)]
        FILES[File Storage]
    end
    
    subgraph "External Services"
        OPENAI[OpenAI APIs]
        CDN[CDN for Images]
        SMTP[Email Service]
    end
    
    FE --> API
    FE --> WS
    API --> DB
    API --> REDIS
    API --> OPENAI
    WS --> REDIS
    FILES --> CDN
```

### Technology Stack

**Frontend:**
- Next.js 15 with App Router
- TypeScript for type safety
- Tailwind CSS + Radix UI components
- React Query for state management
- Socket.IO client for real-time features
- Web Speech API for voice features

**Backend:**
- Node.js with Express.js
- Socket.IO for real-time communication
- MySQL with connection pooling
- Redis for caching and session management
- JWT for authentication
- Multer for file uploads

**External Integrations:**
- OpenAI GPT models for chat
- OpenAI DALL-E for image generation
- OpenAI Whisper for speech-to-text
- OpenAI TTS for text-to-speech
- CDN for image delivery
## Components and Interfaces

### Frontend Components Architecture

```
src/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Authentication routes
│   │   ├── login/
│   │   ├── register/
│   │   └── verify-otp/
│   ├── dashboard/                # User dashboard
│   ├── marketplace/              # Expert marketplace
│   ├── chat/                     # Chat interface
│   │   └── [expertId]/
│   ├── expert/                   # Expert management
│   │   ├── create/
│   │   ├── edit/[id]/
│   │   └── [id]/
│   ├── profile/                  # User profile
│   ├── affiliate/                # Affiliate dashboard
│   └── admin/                    # Admin panel
├── components/
│   ├── ui/                       # Reusable UI components
│   ├── chat/                     # Chat-specific components
│   │   ├── ChatInterface.tsx
│   │   ├── MessageBubble.tsx
│   │   ├── VoiceRecorder.tsx
│   │   └── StreamingResponse.tsx
│   ├── expert/                   # Expert-related components
│   │   ├── ExpertCard.tsx
│   │   ├── ExpertForm.tsx
│   │   ├── ExpertMarketplace.tsx
│   │   └── ExpertStats.tsx
│   ├── shared/                   # Shared components
│   │   ├── Navigation.tsx
│   │   ├── BalanceDisplay.tsx
│   │   ├── RatingStars.tsx
│   │   └── ShareModal.tsx
│   └── forms/                    # Form components
├── contexts/                     # React contexts
│   ├── AuthContext.tsx
│   ├── SocketContext.tsx
│   └── BalanceContext.tsx
├── hooks/                        # Custom hooks
│   ├── useChat.ts
│   ├── useVoice.ts
│   ├── useExperts.ts
│   └── useBalance.ts
├── lib/                          # Utilities
│   ├── api.ts
│   ├── socket.ts
│   ├── voice.ts
│   └── utils.ts
└── types/                        # TypeScript types
    ├── user.ts
    ├── expert.ts
    ├── chat.ts
    └── api.ts
```

### Backend API Architecture

```
src/
├── controllers/                  # Request handlers
│   ├── authController.js
│   ├── expertController.js
│   ├── chatController.js
│   ├── balanceController.js
│   ├── affiliateController.js
│   ├── reviewController.js
│   └── adminController.js
├── services/                     # Business logic
│   ├── authService.js
│   ├── expertService.js
│   ├── chatService.js
│   ├── openaiService.js
│   ├── balanceService.js
│   ├── affiliateService.js
│   ├── recommendationService.js
│   └── voiceService.js
├── routes/                       # API routes
│   ├── auth/
│   ├── experts/
│   ├── chat/
│   ├── balance/
│   ├── affiliate/
│   ├── reviews/
│   └── admin/
├── middleware/                   # Custom middleware
│   ├── auth.js
│   ├── validation.js
│   ├── rateLimit.js
│   ├── upload.js
│   └── errorHandler.js
├── models/                       # Data models
│   ├── User.js
│   ├── Expert.js
│   ├── ChatSession.js
│   ├── Transaction.js
│   └── Review.js
├── utils/                        # Utilities
│   ├── database.js
│   ├── redis.js
│   ├── encryption.js
│   └── validation.js
└── sockets/                      # WebSocket handlers
    ├── chatSocket.js
    ├── notificationSocket.js
    └── streamingSocket.js
```##
 Data Models

### Database Schema (Existing)

The database schema is already implemented and includes sophisticated features:

#### Key Tables Overview
- **`user`**: User management with balance tracking (points/credits)
- **`experts`**: AI expert definitions with OpenAI assistant integration
- **`chat_sessions`**: Chat session management with thread tracking
- **`chat_messages`**: Individual messages with cost tracking
- **`affiliate_visits`**: Visitor tracking for affiliate system
- **`affiliate_commissions`**: Commission calculations and payments
- **`point_transactions`** & **`credit_transactions`**: Detailed transaction history
- **`otp_codes`**: OTP verification system
- **`token_pricing`**: Dynamic pricing for different AI models

#### Key Features Already Implemented
- ✅ Comprehensive affiliate system with commission tracking
- ✅ Dual balance system (points + credits) with transaction history
- ✅ Advanced cost calculation with IDR conversion
- ✅ Commission processing with triggers and stored procedures
- ✅ Database views for analytics and reporting
- ✅ Referral code generation and tracking
- ✅ Welcome bonus automation via triggers

#### Missing Features to Add

Based on requirements vs existing schema, we need to add:

**1. Expert Enhancements:**
```sql
-- Add missing columns to experts table
ALTER TABLE experts ADD COLUMN first_message TEXT AFTER image_url;
ALTER TABLE experts ADD COLUMN voice_enabled BOOLEAN DEFAULT FALSE AFTER first_message;
ALTER TABLE experts ADD COLUMN total_chats INT DEFAULT 0 AFTER labels;
ALTER TABLE experts ADD COLUMN total_revenue DECIMAL(10,2) DEFAULT 0 AFTER total_chats;
ALTER TABLE experts ADD COLUMN average_rating DECIMAL(3,2) DEFAULT 0 AFTER total_revenue;
ALTER TABLE experts ADD COLUMN total_reviews INT DEFAULT 0 AFTER average_rating;
```

**2. Reviews & Ratings System:**
```sql
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    expert_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    is_hidden BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(user_id),
    FOREIGN KEY (expert_id) REFERENCES experts(id),
    UNIQUE KEY unique_user_expert_review (user_id, expert_id),
    INDEX idx_expert_reviews (expert_id, created_at DESC),
    INDEX idx_rating (rating)
);
```

**3. Expert Sharing System:**
```sql
CREATE TABLE expert_shares (
    id INT PRIMARY KEY AUTO_INCREMENT,
    expert_id INT NOT NULL,
    shared_by_user_id INT NOT NULL,
    share_token VARCHAR(255) UNIQUE NOT NULL,
    monitor_enabled BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (expert_id) REFERENCES experts(id),
    FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id),
    INDEX idx_share_token (share_token),
    INDEX idx_shared_by (shared_by_user_id)
);
```

**4. Voice & Image Generation Tracking:**
```sql
CREATE TABLE ai_generation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    generation_type ENUM('image', 'voice_tts', 'voice_stt', 'labels') NOT NULL,
    cost DECIMAL(10,6) NOT NULL,
    cost_idr DECIMAL(15,2) NOT NULL,
    reference_type ENUM('expert_creation', 'chat_message', 'profile_image') NOT NULL,
    reference_id INT,
    prompt_used TEXT,
    result_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(user_id),
    INDEX idx_user_generations (user_id, created_at DESC),
    INDEX idx_generation_type (generation_type)
);
```

#### Existing Schema Alignment

The current `chat_sessions` table structure is:
```sql
-- Current structure (already exists)
CREATE TABLE chat_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    thread_id VARCHAR(255) UNIQUE NOT NULL,
    expert_id INT,
    expert_name VARCHAR(255),
    expert_model VARCHAR(100),
    session_title VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1
);
```

**Enhancements needed:**
```sql
-- Add sharing functionality to existing chat_sessions
ALTER TABLE chat_sessions ADD COLUMN is_shared BOOLEAN DEFAULT FALSE;
ALTER TABLE chat_sessions ADD COLUMN shared_by_user_id INT;
ALTER TABLE chat_sessions ADD COLUMN share_token VARCHAR(255) UNIQUE;
ALTER TABLE chat_sessions ADD COLUMN monitor_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE chat_sessions ADD FOREIGN KEY (shared_by_user_id) REFERENCES user(user_id);
ALTER TABLE chat_sessions ADD INDEX idx_share_token (share_token);
```

#### Existing Messages Schema

The current `chat_messages` table is already comprehensive:
```sql
-- Current structure (already exists)
CREATE TABLE chat_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    thread_id VARCHAR(255) NOT NULL,
    role ENUM('user','assistant') NOT NULL,
    content TEXT NOT NULL,
    message_order INT NOT NULL,
    tokens_used INT DEFAULT 0,
    input_tokens INT DEFAULT 0,
    output_tokens INT DEFAULT 0,
    cost DECIMAL(10,6) DEFAULT 0.000000,
    base_cost DECIMAL(10,6) DEFAULT 0.000000,
    base_cost_idr DECIMAL(15,2) DEFAULT 0.00,
    markup_cost DECIMAL(10,6) DEFAULT 0.000000,
    markup_cost_idr DECIMAL(15,2) DEFAULT 0.00,
    exchange_rate_used DECIMAL(10,4) DEFAULT 20000.0000,
    commission_processed TINYINT(1) DEFAULT 0,
    points_used DECIMAL(15,2) DEFAULT 0.00,
    credits_used DECIMAL(15,2) DEFAULT 0.00,
    generates_commission TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Enhancements needed for multimedia:**
```sql
-- Add multimedia support to existing chat_messages
ALTER TABLE chat_messages ADD COLUMN message_type ENUM('text', 'image', 'voice') DEFAULT 'text';
ALTER TABLE chat_messages ADD COLUMN file_url VARCHAR(500);
ALTER TABLE chat_messages ADD COLUMN voice_duration INT; -- in seconds
ALTER TABLE chat_messages ADD INDEX idx_message_type (message_type);
```

#### Balance & Transactions Tables
```sql
CREATE TABLE user_balances (
    user_id INT PRIMARY KEY,
    points DECIMAL(10,2) DEFAULT 0,
    credits DECIMAL(10,2) DEFAULT 0,
    total_earned DECIMAL(10,2) DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type ENUM('points', 'credits') NOT NULL,
    operation ENUM('add', 'deduct') NOT NULL,
    amount DECIMAL(10,4) NOT NULL,
    description TEXT,
    reference_type ENUM('chat', 'expert_commission', 'affiliate_commission', 'welcome_bonus', 'admin_adjustment'),
    reference_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_user_transactions (user_id, created_at DESC),
    INDEX idx_reference (reference_type, reference_id)
);
```#
### Reviews & Ratings Table
```sql
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    expert_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    is_hidden BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (expert_id) REFERENCES experts(id),
    UNIQUE KEY unique_user_expert_review (user_id, expert_id),
    INDEX idx_expert_reviews (expert_id, created_at DESC),
    INDEX idx_rating (rating)
);
```

#### Affiliate System Tables
```sql
CREATE TABLE affiliate_visitors (
    id INT PRIMARY KEY AUTO_INCREMENT,
    visitor_id VARCHAR(255) UNIQUE NOT NULL,
    referral_code VARCHAR(20) NOT NULL,
    referrer_user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    converted_user_id INT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_user_id) REFERENCES users(user_id),
    FOREIGN KEY (converted_user_id) REFERENCES users(user_id),
    INDEX idx_referral_code (referral_code),
    INDEX idx_visitor_id (visitor_id)
);

CREATE TABLE affiliate_commissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    affiliate_user_id INT NOT NULL,
    referred_user_id INT NOT NULL,
    commission_type ENUM('registration', 'credit_purchase', 'expert_usage') NOT NULL,
    base_amount DECIMAL(10,4) NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL,
    commission_amount DECIMAL(10,4) NOT NULL,
    reference_transaction_id INT,
    status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (affiliate_user_id) REFERENCES users(user_id),
    FOREIGN KEY (referred_user_id) REFERENCES users(user_id),
    FOREIGN KEY (reference_transaction_id) REFERENCES transactions(id),
    INDEX idx_affiliate_commissions (affiliate_user_id, created_at DESC)
);
```

### API Response Models

#### Standard API Response Format
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

#### Expert Model
```typescript
interface Expert {
  id: number;
  user_id: number;
  name: string;
  description: string;
  system_prompt: string;
  model: 'gpt-4o' | 'gpt-4o-mini';
  pricing_percentage: number;
  is_public: boolean;
  assistant_id: string;
  image_url?: string;
  first_message?: string;
  voice_enabled: boolean;
  labels: string[];
  total_chats: number;
  total_revenue: number;
  average_rating: number;
  total_reviews: number;
  creator_name?: string;
  created_at: string;
  updated_at: string;
}
```

#### Chat Session Model
```typescript
interface ChatSession {
  id: number;
  user_id: number;
  expert_id: number;
  thread_id: string;
  title?: string;
  total_messages: number;
  total_cost: number;
  last_message_at?: string;
  is_shared: boolean;
  shared_by?: number;
  share_token?: string;
  monitor_enabled: boolean;
  expert?: Expert;
  created_at: string;
  updated_at: string;
}
```

#### Message Model
```typescript
interface Message {
  id: number;
  session_id: number;
  role: 'user' | 'assistant';
  content: string;
  message_type: 'text' | 'image' | 'voice';
  file_url?: string;
  token_count?: number;
  cost?: number;
  created_at: string;
}
```## 
Error Handling

### Error Classification

#### Client Errors (4xx)
```typescript
enum ClientErrorCodes {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE'
}
```

#### Server Errors (5xx)
```typescript
enum ServerErrorCodes {
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  OPENAI_API_ERROR = 'OPENAI_API_ERROR',
  FILE_UPLOAD_ERROR = 'FILE_UPLOAD_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR'
}
```

### Error Response Format
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    requestId: string;
  };
}
```

### Error Handling Strategy

#### Frontend Error Handling
```typescript
// Global error boundary
class GlobalErrorBoundary extends Component {
  handleError(error: Error, errorInfo: ErrorInfo) {
    // Log to monitoring service
    console.error('Global error:', error, errorInfo);
    
    // Show user-friendly error message
    toast.error('Something went wrong. Please try again.');
  }
}

// API error handling
const handleApiError = (error: AxiosError) => {
  if (error.response?.status === 401) {
    // Redirect to login
    router.push('/login');
  } else if (error.response?.status === 402) {
    // Show insufficient balance modal
    showBalanceModal();
  } else {
    // Show generic error
    toast.error(error.response?.data?.error?.message || 'An error occurred');
  }
};
```

#### Backend Error Handling
```javascript
// Global error handler middleware
const errorHandler = (err, req, res, next) => {
  const requestId = req.headers['x-request-id'] || generateRequestId();
  
  // Log error with context
  logger.error('API Error', {
    error: err.message,
    stack: err.stack,
    requestId,
    userId: req.user?.user_id,
    endpoint: req.path,
    method: req.method
  });
  
  // Determine error type and response
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: err.message,
        details: err.details,
        timestamp: new Date().toISOString(),
        requestId
      }
    });
  }
  
  // Default server error
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An internal server error occurred',
      timestamp: new Date().toISOString(),
      requestId
    }
  });
};
```

## Testing Strategy

### Unit Testing
- **Frontend**: Jest + React Testing Library
- **Backend**: Jest + Supertest
- **Coverage Target**: >80% code coverage

### Integration Testing
- API endpoint testing
- Database integration testing
- OpenAI API integration testing
- WebSocket connection testing

### End-to-End Testing
- User registration and authentication flow
- Expert creation and marketplace browsing
- Complete chat session with AI expert
- Balance and transaction management
- Affiliate tracking and commission calculation

### Performance Testing
- Load testing for concurrent chat sessions
- Database query performance testing
- OpenAI API rate limiting testing
- WebSocket connection scalability testing

### Test Data Management
```javascript
// Test fixtures
const testUsers = {
  regularUser: {
    phone: '+1234567890',
    email: '<EMAIL>',
    name: 'Test User',
    password: 'password123'
  },
  expertCreator: {
    phone: '+1234567891',
    email: '<EMAIL>',
    name: 'Expert Creator',
    password: 'password123'
  }
};

const testExperts = {
  publicExpert: {
    name: 'Test Expert',
    description: 'A test expert for automated testing',
    system_prompt: 'You are a helpful test assistant',
    model: 'gpt-4o-mini',
    pricing_percentage: 10,
    is_public: true
  }
};
```##
 Real-time Communication Design

### WebSocket Architecture

#### Socket.IO Implementation
```javascript
// Server-side socket handlers
io.on('connection', (socket) => {
  // Authentication
  socket.on('authenticate', async (token) => {
    try {
      const user = await verifyJWT(token);
      socket.userId = user.user_id;
      socket.join(`user_${user.user_id}`);
      socket.emit('authenticated', { success: true });
    } catch (error) {
      socket.emit('auth_error', { message: 'Invalid token' });
    }
  });
  
  // Chat streaming
  socket.on('start_chat', async (data) => {
    const { expertId, message } = data;
    const chatRoom = `chat_${socket.userId}_${expertId}`;
    socket.join(chatRoom);
    
    // Process chat with streaming
    await processChatWithStreaming(socket, chatRoom, message, expertId);
  });
  
  // Voice message handling
  socket.on('voice_message', async (data) => {
    const { audioBlob, expertId } = data;
    
    // Convert speech to text
    const transcript = await openaiService.speechToText(audioBlob);
    
    // Process as regular chat
    await processChatWithStreaming(socket, `chat_${socket.userId}_${expertId}`, transcript, expertId);
  });
});

// Streaming chat processing
const processChatWithStreaming = async (socket, room, message, expertId) => {
  try {
    // Get expert and create/get thread
    const expert = await expertService.getById(expertId);
    const thread = await openaiService.getOrCreateThread(socket.userId, expertId);
    
    // Add user message
    await openaiService.addMessage(thread.id, message);
    
    // Start streaming run
    const stream = await openaiService.createStreamingRun(thread.id, expert.assistant_id);
    
    let responseText = '';
    
    stream.on('textDelta', (delta) => {
      responseText += delta.value;
      // Emit streaming response
      io.to(room).emit('message_stream', {
        type: 'delta',
        content: delta.value,
        messageId: generateMessageId()
      });
    });
    
    stream.on('end', async () => {
      // Calculate cost and update balance
      const cost = calculateTokenCost(stream.usage);
      await balanceService.deductBalance(socket.userId, cost);
      
      // Save message to database
      await chatService.saveMessage({
        sessionId: thread.sessionId,
        role: 'assistant',
        content: responseText,
        tokenCount: stream.usage.total_tokens,
        cost
      });
      
      // Emit completion
      io.to(room).emit('message_complete', {
        messageId: generateMessageId(),
        content: responseText,
        cost,
        tokenCount: stream.usage.total_tokens
      });
    });
    
  } catch (error) {
    socket.emit('chat_error', { message: error.message });
  }
};
```

#### Frontend Socket Integration
```typescript
// Socket context
const SocketContext = createContext<SocketContextType | null>(null);

export const SocketProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { user } = useAuth();
  
  useEffect(() => {
    if (user?.token) {
      const newSocket = io(process.env.NEXT_PUBLIC_WS_URL, {
        auth: { token: user.token }
      });
      
      newSocket.on('connect', () => {
        setIsConnected(true);
        newSocket.emit('authenticate', user.token);
      });
      
      newSocket.on('disconnect', () => {
        setIsConnected(false);
      });
      
      setSocket(newSocket);
      
      return () => {
        newSocket.close();
      };
    }
  }, [user]);
  
  return (
    <SocketContext.Provider value={{ socket, isConnected }}>
      {children}
    </SocketContext.Provider>
  );
};

// Chat hook with streaming
export const useChat = (expertId: string) => {
  const { socket } = useSocket();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState('');
  
  useEffect(() => {
    if (!socket) return;
    
    socket.on('message_stream', (data) => {
      if (data.type === 'delta') {
        setStreamingMessage(prev => prev + data.content);
        setIsStreaming(true);
      }
    });
    
    socket.on('message_complete', (data) => {
      setMessages(prev => [...prev, {
        id: data.messageId,
        role: 'assistant',
        content: data.content,
        cost: data.cost,
        tokenCount: data.tokenCount,
        created_at: new Date().toISOString()
      }]);
      setStreamingMessage('');
      setIsStreaming(false);
    });
    
    return () => {
      socket.off('message_stream');
      socket.off('message_complete');
    };
  }, [socket]);
  
  const sendMessage = useCallback((message: string) => {
    if (!socket) return;
    
    // Add user message immediately
    const userMessage: Message = {
      id: generateId(),
      role: 'user',
      content: message,
      created_at: new Date().toISOString()
    };
    setMessages(prev => [...prev, userMessage]);
    
    // Send to server
    socket.emit('start_chat', { expertId, message });
  }, [socket, expertId]);
  
  return {
    messages,
    isStreaming,
    streamingMessage,
    sendMessage
  };
};
```## AI Integ
ration Design

### OpenAI Service Architecture

```javascript
class OpenAIService {
  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }
  
  // Assistant management
  async createAssistant(expertData) {
    const assistant = await this.client.beta.assistants.create({
      name: expertData.name,
      instructions: expertData.system_prompt,
      model: expertData.model,
      tools: [
        { type: 'code_interpreter' },
        { type: 'retrieval' }
      ]
    });
    
    return assistant;
  }
  
  // Image generation for experts
  async generateExpertImage(name, description) {
    const prompt = `Create a professional avatar image for an AI expert named "${name}". ${description}. Style: modern, clean, professional, suitable for a business platform.`;
    
    const response = await this.client.images.generate({
      model: 'dall-e-3',
      prompt,
      size: '1024x1024',
      quality: 'standard',
      n: 1
    });
    
    return response.data[0].url;
  }
  
  // Label generation
  async generateLabels(name, description, systemPrompt) {
    const prompt = `Based on this AI expert information:
    Name: ${name}
    Description: ${description}
    System Prompt: ${systemPrompt}
    
    Generate exactly 5 relevant hashtag labels that users would search for. Return only the labels separated by commas, without # symbols.`;
    
    const response = await this.client.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 100,
      temperature: 0.3
    });
    
    const labels = response.choices[0].message.content
      .split(',')
      .map(label => label.trim())
      .slice(0, 5);
    
    return labels;
  }
  
  // Voice processing
  async speechToText(audioBuffer) {
    const response = await this.client.audio.transcriptions.create({
      file: audioBuffer,
      model: 'whisper-1'
    });
    
    return response.text;
  }
  
  async textToSpeech(text, voice = 'alloy') {
    const response = await this.client.audio.speech.create({
      model: 'tts-1',
      voice,
      input: text
    });
    
    return response.body;
  }
  
  // Streaming chat
  async createStreamingRun(threadId, assistantId, instructions) {
    const stream = await this.client.beta.threads.runs.stream(threadId, {
      assistant_id: assistantId,
      instructions
    });
    
    return stream;
  }
  
  // Cost calculation
  calculateCost(model, inputTokens, outputTokens) {
    const pricing = {
      'gpt-4o': { input: 0.005, output: 0.015 }, // per 1K tokens
      'gpt-4o-mini': { input: 0.00015, output: 0.0006 }
    };
    
    const modelPricing = pricing[model] || pricing['gpt-4o-mini'];
    const inputCost = (inputTokens / 1000) * modelPricing.input;
    const outputCost = (outputTokens / 1000) * modelPricing.output;
    
    return inputCost + outputCost;
  }
}
```

### AI Recommendation Engine

```javascript
class RecommendationService {
  // Collaborative filtering
  async getRecommendations(userId, limit = 10) {
    // Get user's interaction history
    const userHistory = await this.getUserInteractionHistory(userId);
    
    if (userHistory.length < 3) {
      // New user - return popular experts
      return this.getPopularExperts(limit);
    }
    
    // Find similar users
    const similarUsers = await this.findSimilarUsers(userId, userHistory);
    
    // Get experts liked by similar users
    const recommendations = await this.getExpertsFromSimilarUsers(similarUsers, userHistory);
    
    // Apply content-based filtering
    const contentRecommendations = await this.getContentBasedRecommendations(userId, userHistory);
    
    // Combine and rank recommendations
    const combinedRecommendations = this.combineRecommendations(
      recommendations,
      contentRecommendations
    );
    
    return combinedRecommendations.slice(0, limit);
  }
  
  async getUserInteractionHistory(userId) {
    const query = `
      SELECT 
        e.id as expert_id,
        e.labels,
        r.rating,
        cs.total_messages,
        cs.total_cost
      FROM chat_sessions cs
      JOIN experts e ON cs.expert_id = e.id
      LEFT JOIN reviews r ON r.expert_id = e.id AND r.user_id = ?
      WHERE cs.user_id = ?
      ORDER BY cs.last_message_at DESC
    `;
    
    const [rows] = await db.execute(query, [userId, userId]);
    return rows;
  }
  
  async findSimilarUsers(userId, userHistory) {
    // Use cosine similarity based on expert usage patterns
    const query = `
      SELECT 
        cs.user_id,
        COUNT(DISTINCT cs.expert_id) as common_experts,
        AVG(r.rating) as avg_rating
      FROM chat_sessions cs
      JOIN reviews r ON r.user_id = cs.user_id AND r.expert_id = cs.expert_id
      WHERE cs.expert_id IN (${userHistory.map(() => '?').join(',')})
        AND cs.user_id != ?
      GROUP BY cs.user_id
      HAVING common_experts >= 2
      ORDER BY common_experts DESC, avg_rating DESC
      LIMIT 50
    `;
    
    const expertIds = userHistory.map(h => h.expert_id);
    const [rows] = await db.execute(query, [...expertIds, userId]);
    return rows;
  }
  
  combineRecommendations(collaborative, contentBased) {
    const combined = new Map();
    
    // Weight collaborative filtering higher
    collaborative.forEach(expert => {
      combined.set(expert.id, {
        ...expert,
        score: expert.score * 0.7,
        reason: 'Users like you also enjoyed this expert'
      });
    });
    
    // Add content-based recommendations
    contentBased.forEach(expert => {
      if (combined.has(expert.id)) {
        const existing = combined.get(expert.id);
        existing.score += expert.score * 0.3;
      } else {
        combined.set(expert.id, {
          ...expert,
          score: expert.score * 0.3,
          reason: 'Based on your interests'
        });
      }
    });
    
    return Array.from(combined.values())
      .sort((a, b) => b.score - a.score);
  }
}
```## S
ecurity Design

### Authentication & Authorization

#### JWT Token Strategy
```javascript
// Token generation
const generateTokens = (user) => {
  const accessToken = jwt.sign(
    { 
      user_id: user.user_id,
      phone: user.phone,
      type: 'access'
    },
    process.env.JWT_SECRET,
    { expiresIn: '15m' }
  );
  
  const refreshToken = jwt.sign(
    {
      user_id: user.user_id,
      type: 'refresh'
    },
    process.env.JWT_REFRESH_SECRET,
    { expiresIn: '7d' }
  );
  
  return { accessToken, refreshToken };
};

// Token validation middleware
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if token is blacklisted
    const isBlacklisted = await redis.get(`blacklist:${token}`);
    if (isBlacklisted) {
      return res.status(401).json({ error: 'Token has been revoked' });
    }
    
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};
```

#### Role-Based Access Control
```javascript
const authorize = (roles = []) => {
  return async (req, res, next) => {
    const user = await User.findById(req.user.user_id);
    
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }
    
    if (roles.length && !roles.includes(user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    
    req.user.role = user.role;
    next();
  };
};

// Usage
app.get('/api/admin/users', authenticateToken, authorize(['admin']), adminController.getUsers);
```

### Input Validation & Sanitization

```javascript
// Validation schemas
const expertValidationSchema = {
  name: {
    required: true,
    type: 'string',
    minLength: 3,
    maxLength: 100,
    sanitize: true
  },
  description: {
    required: true,
    type: 'string',
    minLength: 10,
    maxLength: 500,
    sanitize: true
  },
  system_prompt: {
    required: true,
    type: 'string',
    minLength: 10,
    maxLength: 2000,
    sanitize: true
  },
  model: {
    required: true,
    type: 'string',
    enum: ['gpt-4o', 'gpt-4o-mini']
  },
  pricing_percentage: {
    required: true,
    type: 'number',
    min: 0,
    max: 100
  }
};

// Validation middleware
const validateInput = (schema) => {
  return (req, res, next) => {
    const errors = [];
    
    for (const [field, rules] of Object.entries(schema)) {
      const value = req.body[field];
      
      // Required check
      if (rules.required && (!value || value.toString().trim() === '')) {
        errors.push(`${field} is required`);
        continue;
      }
      
      if (value !== undefined && value !== null) {
        // Type validation
        if (rules.type === 'string' && typeof value !== 'string') {
          errors.push(`${field} must be a string`);
        }
        
        // Length validation
        if (rules.minLength && value.length < rules.minLength) {
          errors.push(`${field} must be at least ${rules.minLength} characters`);
        }
        
        if (rules.maxLength && value.length > rules.maxLength) {
          errors.push(`${field} must not exceed ${rules.maxLength} characters`);
        }
        
        // Enum validation
        if (rules.enum && !rules.enum.includes(value)) {
          errors.push(`${field} must be one of: ${rules.enum.join(', ')}`);
        }
        
        // Sanitization
        if (rules.sanitize && typeof value === 'string') {
          req.body[field] = sanitizeHtml(value, {
            allowedTags: [],
            allowedAttributes: {}
          });
        }
      }
    }
    
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: errors
        }
      });
    }
    
    next();
  };
};
```

### Rate Limiting

```javascript
// Rate limiting configuration
const rateLimitConfig = {
  // General API rate limiting
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP'
  },
  
  // Chat-specific rate limiting
  chat: {
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 10, // 10 messages per minute
    keyGenerator: (req) => `chat:${req.user.user_id}`,
    message: 'Too many chat messages. Please wait before sending another message.'
  },
  
  // Expert creation rate limiting
  expertCreation: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 5, // 5 experts per hour
    keyGenerator: (req) => `expert_creation:${req.user.user_id}`,
    message: 'Too many experts created. Please wait before creating another.'
  }
};

// Apply rate limiting
app.use('/api/', rateLimit(rateLimitConfig.general));
app.use('/api/chat', authenticateToken, rateLimit(rateLimitConfig.chat));
app.use('/api/experts', authenticateToken, rateLimit(rateLimitConfig.expertCreation));
```

### File Upload Implementation (Existing)

The file upload system is already implemented with comprehensive features:

#### Current Upload Configuration
```javascript
// From be/src/middleware/upload.js
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir); // Central uploads directory
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '');
    cb(null, `${uniqueSuffix}-${sanitizedName}`);
  }
});

// Dual field support
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 2 // Knowledge base + image
  }
}).fields([
  { name: 'file', maxCount: 1 },    // Knowledge base file
  { name: 'image', maxCount: 1 }    // Image file
]);
```

#### Knowledge Base File Types Supported
- PDF documents
- Text files (.txt)
- Word documents (.docx, .doc)
- Markdown files (.md)
- JSON files
- CSV files

#### OpenAI Integration Flow
```javascript
// From assistantService.js
async createAssistant(assistantData, file = null) {
  // 1. Create OpenAI assistant
  const assistant = await openai.beta.assistants.create(assistantConfig);
  
  // 2. Upload file to OpenAI if provided
  if (file) {
    const fileId = await this.uploadFile(file.path, file.originalname);
    
    // 3. Create vector store for knowledge base
    const vectorStore = await vectorStoresAPI.create({
      name: `${assistantData.name} Knowledge Base`,
      file_ids: [fileId]
    });
    
    // 4. Attach vector store to assistant
    await openai.beta.assistants.update(assistant.id, {
      tool_resources: {
        file_search: {
          vector_store_ids: [vectorStore.id]
        }
      }
    });
  }
  
  return assistant;
}
```

#### File Processing Features
- ✅ Automatic file upload to OpenAI
- ✅ Vector store creation for knowledge base
- ✅ File attachment to AI assistant
- ✅ Support for file updates
- ✅ Temporary file cleanup
- ✅ Comprehensive error handling## Pe
rformance Optimization

### Caching Strategy

#### Redis Caching Implementation
```javascript
// Cache configuration
const cacheConfig = {
  // Expert marketplace cache
  experts: {
    key: 'experts:public',
    ttl: 300, // 5 minutes
    tags: ['experts']
  },
  
  // User balance cache
  balance: {
    key: (userId) => `balance:${userId}`,
    ttl: 60, // 1 minute
    tags: ['balance']
  },
  
  // Chat session cache
  sessions: {
    key: (userId) => `sessions:${userId}`,
    ttl: 600, // 10 minutes
    tags: ['sessions']
  }
};

// Cache middleware
const cacheMiddleware = (config) => {
  return async (req, res, next) => {
    const cacheKey = typeof config.key === 'function' 
      ? config.key(req.user?.user_id) 
      : config.key;
    
    try {
      const cached = await redis.get(cacheKey);
      if (cached) {
        return res.json(JSON.parse(cached));
      }
      
      // Store original res.json
      const originalJson = res.json;
      res.json = function(data) {
        // Cache the response
        redis.setex(cacheKey, config.ttl, JSON.stringify(data));
        return originalJson.call(this, data);
      };
      
      next();
    } catch (error) {
      next(); // Continue without cache on error
    }
  };
};
```

### Database Optimization

#### Connection Pooling
```javascript
const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 20,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  charset: 'utf8mb4'
};

const pool = mysql.createPool(dbConfig);

// Optimized queries with proper indexing
const optimizedQueries = {
  getPublicExperts: `
    SELECT 
      e.*,
      u.name as creator_name,
      COUNT(DISTINCT cs.id) as total_chats,
      AVG(r.rating) as average_rating,
      COUNT(DISTINCT r.id) as total_reviews
    FROM experts e
    LEFT JOIN users u ON e.user_id = u.user_id
    LEFT JOIN chat_sessions cs ON e.id = cs.expert_id
    LEFT JOIN reviews r ON e.id = r.expert_id AND r.is_hidden = FALSE
    WHERE e.is_public = TRUE
    GROUP BY e.id
    ORDER BY total_chats DESC, average_rating DESC
    LIMIT ? OFFSET ?
  `,
  
  getUserChatSessions: `
    SELECT 
      cs.*,
      e.name as expert_name,
      e.image_url as expert_image
    FROM chat_sessions cs
    JOIN experts e ON cs.expert_id = e.id
    WHERE cs.user_id = ?
    ORDER BY cs.last_message_at DESC
    LIMIT ?
  `
};
```

## Deployment Architecture

### Production Environment Setup

#### Docker Configuration
```dockerfile
# Backend Dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Create uploads directory
RUN mkdir -p uploads

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

CMD ["node", "server.js"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  frontend:
    build: ./fe
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3001
    depends_on:
      - backend

  backend:
    build: ./be
    ports:
      - "3001:3001"
    environment:
      - DB_HOST=mysql
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - mysql
      - redis
    volumes:
      - ./uploads:/app/uploads

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./db/init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### Monitoring & Logging

#### Application Monitoring
```javascript
// Monitoring middleware
const monitoringMiddleware = (req, res, next) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    // Log request metrics
    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      userId: req.user?.user_id,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
    
    // Send metrics to monitoring service
    metrics.increment('api.requests.total', {
      method: req.method,
      status: res.statusCode,
      endpoint: req.route?.path || 'unknown'
    });
    
    metrics.histogram('api.request.duration', duration, {
      endpoint: req.route?.path || 'unknown'
    });
  });
  
  next();
};
```

## Current Implementation Status

### ✅ **FULLY IMPLEMENTED FEATURES**

#### Backend (Node.js + Express)
- **Authentication System**: Complete user registration, login, OTP verification
- **Expert Management**: Full CRUD operations with OpenAI assistant integration
- **Chat System**: Real-time chat with OpenAI assistants, session management
- **Balance System**: Dual balance (points/credits) with transaction tracking
- **Affiliate System**: Comprehensive referral tracking and commission calculation
- **File Upload**: Knowledge base and image upload with OpenAI integration
- **Database**: Sophisticated schema with triggers, views, and stored procedures
- **API Documentation**: Complete Swagger documentation

#### Frontend (Next.js + TypeScript)
- **User Interface**: Modern UI with Tailwind CSS and Radix components
- **Expert Marketplace**: Browse and discover public experts
- **Expert Creation**: Form-based expert creation with file uploads
- **Chat Interface**: Real-time chat with AI experts
- **Balance Management**: View balance, transaction history
- **Affiliate Dashboard**: Complete affiliate tracking and statistics
- **Authentication**: Login, registration, OTP verification flows

#### Database Features
- **Advanced Schema**: 15+ tables with relationships and constraints
- **Automated Triggers**: Welcome bonus, commission calculation
- **Analytics Views**: Pre-built views for reporting and statistics
- **Transaction Tracking**: Detailed audit trail for all operations
- **Currency Support**: IDR conversion with exchange rate tracking

### 🚧 **PARTIALLY IMPLEMENTED FEATURES**

#### Chat Enhancements
- **Basic Chat**: ✅ Working chat with OpenAI
- **Real-time Streaming**: ❌ Not implemented (uses polling)
- **Voice Support**: ❌ Not implemented
- **Image Generation in Chat**: ❌ Not implemented

#### Expert Features
- **Basic Expert Creation**: ✅ Working
- **AI-Generated Labels**: ❌ Not implemented
- **AI-Generated Images**: ❌ Not implemented
- **First Message**: ❌ Database column exists but not in UI
- **Voice Capabilities**: ❌ Not implemented

### ❌ **MISSING FEATURES FROM REQUIREMENTS**

#### Core Missing Features
1. **Rating & Review System**: Complete system needed
2. **Expert Sharing with Privacy Controls**: Not implemented
3. **Real-time Chat Streaming**: WebSocket/SSE implementation needed
4. **Voice Input/Output**: Whisper + TTS integration needed
5. **AI Content Generation**: Labels and images for experts
6. **Recommendation Engine**: AI-based expert recommendations
7. **Admin Dashboard**: Management interface needed

#### Technical Enhancements Needed
1. **WebSocket Integration**: For real-time features
2. **Redis Caching**: Performance optimization
3. **Rate Limiting**: API protection
4. **Content Moderation**: Safety features
5. **Mobile Optimization**: PWA features

### 📊 **IMPLEMENTATION PRIORITY**

**Phase 1 (High Priority - Core UX)**
- Real-time chat streaming with WebSocket
- Rating and review system
- AI-generated labels and images for experts
- Expert sharing functionality

**Phase 2 (Medium Priority - Advanced Features)**
- Voice input/output capabilities
- AI recommendation engine
- Admin dashboard and analytics
- Performance optimizations (caching, rate limiting)

**Phase 3 (Low Priority - Polish)**
- Mobile PWA features
- Advanced content moderation
- Internationalization
- Advanced analytics

This comprehensive design document covers all major aspects of the AI Trainer Hub platform architecture. The platform has a solid foundation with most core features implemented, requiring focused development on real-time features, AI enhancements, and user experience improvements.

<function_calls>
<invoke name="userInput">
<parameter name="question">**Design document sudah selesai dan mencakup semua aspek teknis: arsitektur sistem, komponen frontend/backend, database schema, real-time communication, AI integration, security, performance optimization, dan deployment. Apakah design ini sudah sesuai dan siap untuk lanjut ke tahap implementation plan?**