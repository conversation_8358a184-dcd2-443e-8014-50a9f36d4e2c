'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Share2, 
  BarChart3, 
  TrendingUp,
  Eye,
  MessageCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSharing } from '@/hooks/useSharing';
import ShareCreator from '@/components/sharing/ShareCreator';
import ShareList from '@/components/sharing/ShareList';
import { api } from '@/lib/api';

interface DashboardStats {
  totalShares: number;
  activeShares: number;
  totalClicks: number;
  totalConversions: number;
  conversionRate: number;
  topPerformingShare: {
    shareToken: string;
    expertName: string;
    clicks: number;
    conversions: number;
  } | null;
}

export default function SharesDashboard() {
  const { toast } = useToast();
  useSharing();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadDashboardStats();
  }, [refreshTrigger]);

  const loadDashboardStats = async () => {
    try {
      setStatsLoading(true);
      const response = await api.get('/sharing/dashboard/stats', {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (err: any) {
      console.error('Error loading dashboard stats:', err);
      // Don't show error toast for stats loading failure
    } finally {
      setStatsLoading(false);
    }
  };

  const handleShareCreated = () => {
    setShowCreateForm(false);
    setRefreshTrigger(prev => prev + 1);
    toast({
      title: "Share Created!",
      description: "Your expert has been shared successfully."
    });
  };

  const refreshData = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Share Management</h1>
              <p className="text-gray-600 mt-1">
                Create, manage, and track your shared AI experts
              </p>
            </div>
            <Button 
              onClick={() => setShowCreateForm(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create New Share
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="shares">My Shares</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 text-blue-600 mb-2">
                    <Share2 className="h-5 w-5" />
                    <span className="font-medium">Total Shares</span>
                  </div>
                  <p className="text-3xl font-bold text-blue-900">
                    {statsLoading ? '...' : stats?.totalShares || 0}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    {statsLoading ? '...' : stats?.activeShares || 0} active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 text-green-600 mb-2">
                    <Eye className="h-5 w-5" />
                    <span className="font-medium">Total Views</span>
                  </div>
                  <p className="text-3xl font-bold text-green-900">
                    {statsLoading ? '...' : stats?.totalClicks?.toLocaleString() || 0}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    Across all shares
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 text-purple-600 mb-2">
                    <MessageCircle className="h-5 w-5" />
                    <span className="font-medium">Conversations</span>
                  </div>
                  <p className="text-3xl font-bold text-purple-900">
                    {statsLoading ? '...' : stats?.totalConversions?.toLocaleString() || 0}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    Total chats started
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2 text-orange-600 mb-2">
                    <TrendingUp className="h-5 w-5" />
                    <span className="font-medium">Conversion Rate</span>
                  </div>
                  <p className="text-3xl font-bold text-orange-900">
                    {statsLoading ? '...' : `${stats?.conversionRate?.toFixed(1) || 0}%`}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    Views to chats
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Top Performing Share */}
            {stats?.topPerformingShare && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="h-5 w-5 text-yellow-500" />
                    <span>Top Performing Share</span>
                  </CardTitle>
                  <CardDescription>
                    Your most successful shared expert this period
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
                    <div>
                      <h3 className="font-semibold text-gray-900">{stats.topPerformingShare.expertName}</h3>
                      <p className="text-sm text-gray-600">Share Token: {stats.topPerformingShare.shareToken}</p>
                    </div>
                    <div className="text-right">
                      <div className="flex space-x-6">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-blue-600">{stats.topPerformingShare.clicks}</p>
                          <p className="text-xs text-gray-600">Views</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-green-600">{stats.topPerformingShare.conversions}</p>
                          <p className="text-xs text-gray-600">Chats</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-purple-600">
                            {stats.topPerformingShare.clicks > 0 
                              ? Math.round((stats.topPerformingShare.conversions / stats.topPerformingShare.clicks) * 100)
                              : 0
                            }%
                          </p>
                          <p className="text-xs text-gray-600">Rate</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common tasks for managing your shared experts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col space-y-2"
                    onClick={() => setShowCreateForm(true)}
                  >
                    <Plus className="h-6 w-6" />
                    <span>Create New Share</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col space-y-2"
                    onClick={() => setActiveTab('shares')}
                  >
                    <Share2 className="h-6 w-6" />
                    <span>Manage Shares</span>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col space-y-2"
                    onClick={() => setActiveTab('analytics')}
                  >
                    <BarChart3 className="h-6 w-6" />
                    <span>View Analytics</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Getting Started */}
            {(!stats || stats.totalShares === 0) && (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="p-12 text-center">
                  <Share2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Start Sharing Your Experts</h3>
                  <p className="text-gray-600 mb-6 max-w-md mx-auto">
                    Share your AI experts with others and track their performance. 
                    Create your first share to get started with analytics and insights.
                  </p>
                  <Button 
                    onClick={() => setShowCreateForm(true)}
                    size="lg"
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Create Your First Share
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="shares" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">My Shares</h2>
                <p className="text-gray-600">Manage all your shared expert links</p>
              </div>
              <Button 
                onClick={refreshData}
                variant="outline"
              >
                Refresh
              </Button>
            </div>
            <ShareList refreshTrigger={refreshTrigger} />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Analytics Overview</h2>
              <p className="text-gray-600">Detailed insights into your sharing performance</p>
            </div>
            
            {/* Analytics Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Performance Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Shares</span>
                    <span className="font-semibold">{stats?.totalShares || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Active Shares</span>
                    <span className="font-semibold">{stats?.activeShares || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Views</span>
                    <span className="font-semibold">{stats?.totalClicks?.toLocaleString() || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Chats</span>
                    <span className="font-semibold">{stats?.totalConversions?.toLocaleString() || 0}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Conversion Insights</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {stats?.conversionRate?.toFixed(1) || 0}%
                    </div>
                    <p className="text-gray-600">Overall Conversion Rate</p>
                  </div>
                  <div className="pt-4 border-t">
                    <p className="text-sm text-gray-600 text-center">
                      {stats?.totalConversions || 0} conversations started from {stats?.totalClicks || 0} total views
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => setActiveTab('shares')}
                  >
                    View All Shares
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => setShowCreateForm(true)}
                  >
                    Create New Share
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={refreshData}
                  >
                    Refresh Data
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Detailed Analytics Note */}
            <Card>
              <CardContent className="p-6 text-center">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Detailed Analytics</h3>
                <p className="text-gray-600 mb-4">
                  For detailed analytics of individual shares, visit the share management section 
                  and click on "View Analytics" for any specific share.
                </p>
                <Button 
                  variant="outline"
                  onClick={() => setActiveTab('shares')}
                >
                  Go to Share Management
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Create Share Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Create New Share</h2>
                <Button 
                  variant="ghost" 
                  onClick={() => setShowCreateForm(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ×
                </Button>
              </div>
              <ShareCreator 
                expert={{
                  id: 0,
                  name: 'New Expert',
                  description: 'Create a new expert to share',
                  imageUrl: undefined,
                  labels: ['general']
                }}
                onShareCreated={handleShareCreated}
                onCancel={() => setShowCreateForm(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}