const { pool } = require('../config/database');

const authenticateToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '') || req.headers.token;
    
    if (!token) {
      return res.status(401).json({ 
        error: 'Access token is required',
        message: 'Please provide a valid token in the Authorization header or token header'
      });
    }

    // Check token in database
    console.log('🔍 Auth Debug - Checking token:', token);
    const [rows] = await pool.execute(
      'SELECT user_id, phone, name, email FROM user WHERE token = ? AND is_verified = TRUE',
      [token]
    );
    console.log('🔍 Auth Debug - Query result rows:', rows);

    if (rows.length === 0) {
      console.log('🔍 Auth Debug - No user found or not verified');
      return res.status(401).json({ 
        error: 'Invalid token',
        message: 'The provided token is not valid or has expired'
      });
    }

    console.log('🔍 Auth Debug - User found:', rows[0]);
    // Add user info to request object
    req.user = {
      user_id: rows[0].user_id,
      phone: rows[0].phone,
      name: rows[0].name,
      email: rows[0].email,
      token: token
    };
    console.log('🔍 Auth Debug - req.user set to:', req.user);

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({ 
      error: 'Authentication failed',
      message: 'Internal server error during authentication'
    });
  }
};

// Optional authentication middleware - doesn't fail if no token provided
const optionalAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '') || req.headers.token;
    
    if (!token) {
      // No token provided, continue without user info
      req.user = null;
      return next();
    }

    // Check token in database
    const [rows] = await pool.execute(
      'SELECT user_id, phone, name, email FROM user WHERE token = ? AND is_verified = TRUE',
      [token]
    );

    if (rows.length === 0) {
      // Invalid token, continue without user info
      req.user = null;
      return next();
    }

    // Add user info to request object
    req.user = {
      user_id: rows[0].user_id,
      phone: rows[0].phone,
      name: rows[0].name,
      email: rows[0].email,
      token: token
    };

    next();
  } catch (error) {
    console.error('Optional authentication error:', error);
    // On error, continue without user info
    req.user = null;
    next();
  }
};

module.exports = { authenticateToken, optionalAuth };