'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  MessageCircle, 
  Star, 
  Users, 
  Tag, 
  ExternalLink,
  Share2,
  AlertCircle,
  Loader2,
  ArrowRight
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useShareState } from '@/hooks/useSharing';
import Link from 'next/link';

function SharedExpertPageContent() {
  const params = useParams();
  const shareToken = params.shareToken as string;
  const router = useRouter();
  const { toast } = useToast();
  const {
    loading,
    error,
    shareData,
    loadSharedExpert,
    loadConsent,
    trackClick,
    trackConversion
  } = useShareState(shareToken);

  const [hasTrackedClick, setHasTrackedClick] = useState(false);
  const [isStartingChat, setIsStartingChat] = useState(false);

  useEffect(() => {
    if (shareToken) {
      loadSharedExpert(shareToken);
      loadConsent(shareToken);
    }
  }, [shareToken, loadSharedExpert, loadConsent]);

  useEffect(() => {
    // Track click only once when page loads
    if (shareData && !hasTrackedClick) {
      trackClick(shareToken, {
        source: 'direct_link',
        userAgent: navigator.userAgent,
        referrer: document.referrer
      });
      setHasTrackedClick(true);
    }
  }, [shareData, hasTrackedClick, shareToken, trackClick]);

  const handleStartChat = async () => {
    if (!shareData) return;

    try {
      setIsStartingChat(true);

      // Check if user is logged in
      const token = localStorage.getItem('token');
      if (!token) {
        // Redirect to login with return URL
        const returnUrl = encodeURIComponent(window.location.href);
        router.push(`/auth/login?returnUrl=${returnUrl}`);
        return;
      }

      // Track conversion
      await trackConversion(shareToken, {
        expertId: shareData.expert.id,
        source: 'shared_link'
      });

      // Redirect to chat with the expert
      router.push(`/chat/${shareData.expert.id}?source=shared&shareToken=${shareToken}`);
    } catch (error: any) {
      console.error('Error starting chat:', error);
      toast({
        title: "Error",
        description: "Failed to start chat. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsStartingChat(false);
    }
  };

  const handleShare = async () => {
    try {
      const shareUrl = window.location.href;
      await navigator.clipboard.writeText(shareUrl);
      toast({
        title: "Link Copied!",
        description: "Share link copied to clipboard."
      });
    } catch {
      toast({
        title: "Error",
        description: "Failed to copy link. Please copy manually.",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading Expert...</h2>
          <p className="text-gray-600">Please wait while we prepare your AI expert.</p>
        </div>
      </div>
    );
  }

  if (error || !shareData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Expert Not Found</h2>
          <p className="text-gray-600 mb-6">
            {error || "The shared expert link you're looking for doesn't exist or has been deactivated."}
          </p>
          <div className="space-y-3">
            <Button onClick={() => router.push('/ai-experts')} className="w-full">
              Browse All Experts
            </Button>
            <Button variant="outline" onClick={() => router.push('/')} className="w-full">
              Go to Homepage
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const { expert, share } = shareData;

  // Check if share is inactive
  const isInactive = !share.isActive;

  if (isInactive) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="h-16 w-16 text-gray-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Link Inactive
          </h2>
          <p className="text-gray-600 mb-6">
            This shared expert link has been deactivated by the creator.
          </p>
          <div className="space-y-3">
            <Button onClick={() => router.push('/ai-experts')} className="w-full">
              Browse Available Experts
            </Button>
            <Button variant="outline" onClick={() => router.push('/')} className="w-full">
              Go to Homepage
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Share2 className="h-6 w-6 text-blue-600" />
              <span className="font-semibold text-gray-900">Shared AI Expert</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/ai-experts">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Browse More
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Expert Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Expert Header */}
            <Card className="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
              <CardContent className="p-8">
                <div className="flex items-start space-x-6">
                  <Avatar className="h-20 w-20 border-4 border-white shadow-lg">
                    <AvatarImage src={expert.imageUrl} alt={expert.name} />
                    <AvatarFallback className="text-2xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                      {expert.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">{expert.name}</h1>
                    <p className="text-lg text-gray-600 mb-4">{expert.description}</p>
                    
                    {/* Tags */}
                    {expert.tags && expert.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {expert.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-800">
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Category */}
                    {expert.category && (
                      <Badge variant="outline" className="mb-4">
                        {expert.category}
                      </Badge>
                    )}

                    {/* Creator Info */}
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Users className="h-4 w-4" />
                      <span>Created by {expert.createdBy.displayName || expert.createdBy.username}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Share Stats */}
            <Card className="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span>Share Statistics</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{share.clickCount}</div>
                    <div className="text-sm text-gray-600">Total Views</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {new Date(share.createdAt).toLocaleDateString()}
                    </div>
                    <div className="text-sm text-gray-600">Shared On</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* What You Can Do */}
            <Card className="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>What You Can Do</CardTitle>
                <CardDescription>
                  This AI expert can help you with specialized knowledge and assistance.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <MessageCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-gray-900">Start a Conversation</h4>
                      <p className="text-sm text-gray-600">Chat directly with this AI expert to get personalized assistance.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Star className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-gray-900">Get Expert Advice</h4>
                      <p className="text-sm text-gray-600">Receive specialized knowledge and insights tailored to your needs.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Users className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-gray-900">Join the Community</h4>
                      <p className="text-sm text-gray-600">Discover more AI experts and expand your knowledge network.</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Panel */}
          <div className="space-y-6">
            {/* Main CTA */}
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-500 to-purple-600 text-white">
              <CardContent className="p-6 text-center">
                <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-90" />
                <h3 className="text-xl font-bold mb-2">Start Chatting Now</h3>
                <p className="text-blue-100 mb-6 text-sm">
                  Begin your conversation with {expert.name} and get the help you need.
                </p>
                <Button 
                  onClick={handleStartChat}
                  disabled={isStartingChat}
                  className="w-full bg-white text-blue-600 hover:bg-gray-100 font-semibold"
                  size="lg"
                >
                  {isStartingChat ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Starting Chat...
                    </>
                  ) : (
                    <>
                      Start Chat
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Share Info */}
            <Card className="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Share Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Monitoring</span>
                  <Badge variant={share.monitorEnabled ? 'default' : 'secondary'}>
                    {share.monitorEnabled ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Explore More */}
            <Card className="border-0 shadow-lg bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Explore More</CardTitle>
                <CardDescription>
                  Discover other AI experts that might interest you.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/ai-experts">
                      <Users className="h-4 w-4 mr-2" />
                      Browse All Experts
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/auth/register">
                      <Star className="h-4 w-4 mr-2" />
                      Create Your Own Expert
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-white/80 backdrop-blur-sm border-t mt-12">
        <div className="max-w-4xl mx-auto px-4 py-6 text-center">
          <p className="text-gray-600 text-sm">
            Powered by <span className="font-semibold text-blue-600">AI Trainer Hub</span> - 
            Your marketplace for AI expertise
          </p>
        </div>
      </div>
    </div>
  );
}

export default function SharedExpertPage() {
  return <SharedExpertPageContent />;
}