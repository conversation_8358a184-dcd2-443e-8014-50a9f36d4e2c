const express = require('express');
const expertController = require('../../controllers/expertController');
const { upload, handleUploadError } = require('../../middleware/upload');
const { authenticateToken } = require('../../middleware/auth');

const router = express.Router();

// Create expert
/**
 * @swagger
 * /api/experts:
 *   post:
 *     summary: Create a new AI expert
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               avatar:
 *                 type: string
 *                 format: binary
 *     responses:
 *       201:
 *         description: Expert created
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post('/api/experts', authenticateToken, upload, handleUploadError, expertController.createExpert.bind(expertController));

// List all experts for user
/**
 * @swagger
 * /api/experts:
 *   get:
 *     summary: List all experts for authenticated user
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of experts
 *       401:
 *         description: Unauthorized
 */
router.get('/api/experts', authenticateToken, expertController.listExperts.bind(expertController));

// Get specific expert
/**
 * @swagger
 * /api/experts/{expertId}:
 *   get:
 *     summary: Get a specific expert by ID
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: expertId
 *         schema:
 *           type: string
 *         required: true
 *         description: Expert ID
 *     responses:
 *       200:
 *         description: Expert data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Expert not found
 */
router.get('/api/experts/:expertId', authenticateToken, expertController.getExpert.bind(expertController));

// Get expert statistics
/**
 * @swagger
 * /api/experts/{expertId}/stats:
 *   get:
 *     summary: Get statistics for a specific expert
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: expertId
 *         schema:
 *           type: string
 *         required: true
 *         description: Expert ID
 *     responses:
 *       200:
 *         description: Expert statistics
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Expert not found
 */
router.get('/api/experts/:expertId/stats', authenticateToken, expertController.getExpertStats.bind(expertController));

// Update expert
/**
 * @swagger
 * /api/experts/{expertId}:
 *   put:
 *     summary: Update an expert by ID
 *     tags: [Experts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: expertId
 *         schema:
 *           type: string
 *         required: true
 *         description: Expert ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               avatar:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Expert updated
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Expert not found
 */
router.put('/api/experts/:expertId', authenticateToken, upload, handleUploadError, expertController.updateExpert.bind(expertController));

module.exports = router;
