import ExpertMarketplace from "@/components/ExpertMarketplace";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/5 to-indigo-900/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 py-20">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-900 via-blue-800 to-indigo-900 bg-clip-text text-transparent">
                AI Expert Marketplace
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Connect with specialized AI experts tailored to your needs. 
                From business consulting to creative solutions.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="px-6 py-3 bg-white rounded-full shadow-lg border border-gray-100">
                <span className="text-sm text-gray-600">✨ Over <strong className="font-semibold" style={{color: '#1E3A8A'}}>50+ AI Experts</strong> Available</span>
              </div>
              <div className="px-6 py-3 bg-white rounded-full shadow-lg border border-gray-100">
                <span className="text-sm text-gray-600">🚀 <strong className="font-semibold" style={{color: '#1E3A8A'}}>Instant</strong> Responses</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Experts Section */}
      <section className="max-w-7xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Featured AI Experts
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our curated selection of top-performing AI experts, each specialized in their domain
          </p>
        </div>
        
        <ExpertMarketplace />
      </section>

      {/* Features Section */}
      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Our Platform?
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-100">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl" style={{backgroundColor: '#1E3A8A', color: 'white'}}>
                🎯
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Specialized Expertise</h3>
              <p className="text-gray-600">Each AI expert is fine-tuned for specific domains, ensuring highly relevant and accurate responses.</p>
            </div>
            
            <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-green-50 to-emerald-50 border border-green-100">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl" style={{backgroundColor: '#1E3A8A', color: 'white'}}>
                ⚡
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Instant Access</h3>
              <p className="text-gray-600">Start conversations immediately. No waiting, no appointments - just instant expert guidance.</p>
            </div>
            
            <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-100">
              <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl" style={{backgroundColor: '#1E3A8A', color: 'white'}}>
                🔒
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Secure & Private</h3>
              <p className="text-gray-600">Your conversations are encrypted and private. We prioritize your data security above all.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
