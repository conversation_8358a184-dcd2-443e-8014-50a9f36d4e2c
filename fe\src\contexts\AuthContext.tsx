'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI, User } from '@/lib/api';

interface AuthContextType {
    user: User | null;
    token: string | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    login: (phone: string, password: string) => Promise<void>;
    register: (userData: { name: string; email: string; phone: string; password: string; referralCode?: string }) => Promise<any>;
    verifyOTP: (phone: string, code: string) => Promise<void>;
    logout: () => void;
    resendOTP: (phone: string) => Promise<any>;
    forgotPassword: (phone: string) => Promise<any>;
    resetPassword: (phone: string, code: string, newPassword: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
    children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
    const [user, setUser] = useState<User | null>(null);
    const [token, setToken] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    // Initialize auth state from localStorage
    useEffect(() => {
        const initAuth = async () => {
            const storedToken = localStorage.getItem('token');
            const storedUser = localStorage.getItem('user');

            if (storedToken && storedUser) {
                try {
                    setToken(storedToken);
                    setUser(JSON.parse(storedUser));
                    
                    // Verify token is still valid by fetching profile
                    const profile = await authAPI.getProfile();
                    setUser(profile.user);
                } catch (error) {
                    console.error('Token validation failed:', error);
                    // Clear invalid token
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    setToken(null);
                    setUser(null);
                }
            }
            setIsLoading(false);
        };

        initAuth();
    }, []);

    const login = async (phone: string, password: string) => {
        try {
            const response = await authAPI.login({ phone, password });
            const userData = response.user; // Backend returns user data directly
            const userToken = userData.token; // Token is within the user object
            
            // Remove token from user data for storage
            const userDataForStorage = {
                user_id: userData.user_id,
                phone: userData.phone,
                name: userData.name,
                email: userData.email
            };
            
            setUser(userDataForStorage);
            setToken(userToken);
            
            // Store in localStorage
            localStorage.setItem('token', userToken);
            localStorage.setItem('user', JSON.stringify(userDataForStorage));
        } catch (error: any) {
            throw new Error(error.message || 'Login failed');
        }
    };

    const register = async (userData: { name: string; email: string; phone: string; password: string; referralCode?: string }) => {
        try {
            const response = await authAPI.register(userData);
            return response;
        } catch (error: any) {
            throw new Error(error.message || 'Registration failed');
        }
    };

    const verifyOTP = async (phone: string, code: string) => {
        try {
            const response = await authAPI.verifyOTP({ phone, code });
            const userData = response.user; // Backend returns user data directly
            const userToken = userData.token; // Token is within the user object
            
            // Remove token from user data for storage
            const userDataForStorage = {
                user_id: userData.user_id,
                phone: userData.phone,
                name: userData.name,
                email: userData.email
            };
            
            setUser(userDataForStorage);
            setToken(userToken);
            
            // Store in localStorage
            localStorage.setItem('token', userToken);
            localStorage.setItem('user', JSON.stringify(userDataForStorage));
        } catch (error: any) {
            throw new Error(error.message || 'OTP verification failed');
        }
    };

    const logout = async () => {
        try {
            if (token) {
                await authAPI.logout();
            }
        } catch (error) {
            console.error('Logout API call failed:', error);
        } finally {
            // Clear state regardless of API call success
            setUser(null);
            setToken(null);
            localStorage.removeItem('token');
            localStorage.removeItem('user');
        }
    };

    const resendOTP = async (phone: string) => {
        try {
            const response = await authAPI.resendOTP(phone);
            return response;
        } catch (error: any) {
            throw new Error(error.message || 'Failed to resend OTP');
        }
    };

    const forgotPassword = async (phone: string) => {
        try {
            const response = await authAPI.forgotPassword(phone);
            return response;
        } catch (error: any) {
            throw new Error(error.message || 'Failed to request password reset');
        }
    };

    const resetPassword = async (phone: string, code: string, newPassword: string) => {
        try {
            await authAPI.resetPassword(phone, code, newPassword);
        } catch (error: any) {
            throw new Error(error.message || 'Password reset failed');
        }
    };

    const value: AuthContextType = {
        user,
        token,
        isLoading,
        isAuthenticated: !!user && !!token,
        login,
        register,
        verifyOTP,
        logout,
        resendOTP,
        forgotPassword,
        resetPassword
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = (): AuthContextType => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
