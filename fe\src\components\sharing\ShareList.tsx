'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  Share2, 
  Copy, 
  MoreVertical, 
  Eye, 
  MessageCircle, 
  Calendar, 
  Trash2, 
  BarChart3,
  ExternalLink,
  Check,
  Search,
  Filter
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { api } from '@/lib/api';


interface ShareItem {
  id: number;
  shareToken: string;
  monitorEnabled: boolean;
  isActive: boolean;
  clickCount: number;
  conversionCount: number;
  lastAccessedAt: string | null;
  createdAt: string;
  expert: {
    id: number;
    name: string;
    description: string;
    imageUrl?: string;
  };
}

interface ShareListProps {
  refreshTrigger?: number;
}

export default function ShareList({ refreshTrigger }: ShareListProps) {
  const { toast } = useToast();
  const [shares, setShares] = useState<ShareItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'active' | 'inactive'>('all');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [shareToDelete, setShareToDelete] = useState<ShareItem | null>(null);
  const [copiedToken, setCopiedToken] = useState<string | null>(null);

  const loadShares = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get('/sharing/shares/my', {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setShares(response.data.data);
      }
    } catch (err: any) {
      console.error('Error loading shares:', err);
      toast({
        title: "Error",
        description: "Failed to load shares",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadShares();
  }, [refreshTrigger, loadShares]);


  const copyShareLink = async (shareToken: string) => {
    const shareUrl = `${window.location.origin}/shared/${shareToken}`;
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopiedToken(shareToken);
      toast({
        title: "Copied!",
        description: "Share link copied to clipboard."
      });
      setTimeout(() => setCopiedToken(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
      toast({
        title: "Error",
        description: "Failed to copy link. Please copy manually.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteShare = async () => {
    if (!shareToDelete) return;

    try {
      const response = await api.delete(`/sharing/shares/${shareToDelete.shareToken}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setShares(prev => prev.filter(share => share.id !== shareToDelete.id));
        toast({
          title: "Share Deleted",
          description: "The share link has been deactivated successfully."
        });
      }
    } catch (err: any) {
      console.error('Error deleting share:', err);
      toast({
        title: "Error",
        description: err.response?.data?.message || "Failed to delete share. Please try again.",
        variant: "destructive"
      });
    } finally {
      setDeleteDialogOpen(false);
      setShareToDelete(null);
    }
  };

  const toggleShareStatus = async (share: ShareItem) => {
    try {
      const response = await api.put(`/sharing/shares/${share.shareToken}`, {
        body: {
          isActive: !share.isActive
        },
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setShares(prev => prev.map(s => 
          s.id === share.id ? { ...s, isActive: !s.isActive } : s
        ));
        toast({
          title: share.isActive ? "Share Deactivated" : "Share Activated",
          description: `The share link has been ${share.isActive ? 'deactivated' : 'activated'}.`
        });
      }
    } catch (err: any) {
      console.error('Error toggling share status:', err);
      toast({
        title: "Error",
        description: "Failed to update share status. Please try again.",
        variant: "destructive"
      });
    }
  };

  const filteredShares = shares.filter(share => {
    const matchesSearch = share.expert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         share.expert.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterType === 'all' || 
                         (filterType === 'active' && share.isActive) ||
                         (filterType === 'inactive' && !share.isActive);
    
    return matchesSearch && matchesFilter;
  });

  const getConversionRate = (share: ShareItem) => {
    if (share.clickCount === 0) return 0;
    return Math.round((share.conversionCount / share.clickCount) * 100);
  };



  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="flex space-x-4">
                <div className="h-8 bg-gray-200 rounded w-16"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">My Shares</h2>
          <p className="text-gray-600">Manage your shared expert links and track their performance</p>
        </div>
        <div className="flex space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search shares..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                {filterType === 'all' ? 'All' : filterType === 'active' ? 'Active' : 'Inactive'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setFilterType('all')}>All Shares</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterType('active')}>Active Only</DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterType('inactive')}>Inactive Only</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Shares List */}
      {filteredShares.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Share2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {shares.length === 0 ? 'No shares created yet' : 'No shares match your filters'}
            </h3>
            <p className="text-gray-600 mb-6">
              {shares.length === 0 
                ? 'Start sharing your AI experts to track their performance and reach more users.'
                : 'Try adjusting your search or filter criteria.'
              }
            </p>
            {shares.length === 0 && (
              <Button onClick={() => window.location.reload()}>
                Create Your First Share
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredShares.map((share) => (
            <Card key={share.id} className={`transition-all ${!share.isActive ? 'opacity-60' : ''}`}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{share.expert.name}</h3>
                      <div className="flex space-x-2">
                        <Badge variant={share.isActive ? "default" : "secondary"}>
                          {share.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                        {share.monitorEnabled && (
                          <Badge variant="outline">
                            <BarChart3 className="h-3 w-3 mr-1" />
                            Monitored
                          </Badge>
                        )}
                      </div>
                    </div>
                    <p className="text-gray-600 mb-4">{share.expert.description}</p>
                    
                    {/* Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <div className="flex items-center justify-center space-x-1 text-blue-600 mb-1">
                          <Eye className="h-4 w-4" />
                          <span className="text-sm font-medium">Clicks</span>
                        </div>
                        <p className="text-2xl font-bold text-blue-900">{share.clickCount}</p>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="flex items-center justify-center space-x-1 text-green-600 mb-1">
                          <MessageCircle className="h-4 w-4" />
                          <span className="text-sm font-medium">Chats</span>
                        </div>
                        <p className="text-2xl font-bold text-green-900">{share.conversionCount}</p>
                      </div>
                      <div className="text-center p-3 bg-purple-50 rounded-lg">
                        <div className="flex items-center justify-center space-x-1 text-purple-600 mb-1">
                          <BarChart3 className="h-4 w-4" />
                          <span className="text-sm font-medium">Rate</span>
                        </div>
                        <p className="text-2xl font-bold text-purple-900">{getConversionRate(share)}%</p>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-center space-x-1 text-gray-600 mb-1">
                          <Calendar className="h-4 w-4" />
                          <span className="text-sm font-medium">Created</span>
                        </div>
                        <p className="text-sm font-bold text-gray-900">
                          {new Date(share.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    {/* Share URL */}
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <Input
                        value={`${window.location.origin}/shared/${share.shareToken}`}
                        readOnly
                        className="flex-1 text-sm"
                      />
                      <Button
                        onClick={() => copyShareLink(share.shareToken)}
                        variant="outline"
                        size="sm"
                        className="px-3"
                      >
                        {copiedToken === share.shareToken ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Actions */}
                  <DropdownMenu>
                    <DropdownMenuTrigger>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => window.open(`/shared/${share.shareToken}`, '_blank')}>
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Share Page
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => window.open(`/dashboard/shares/${share.shareToken}/analytics`, '_blank')}>
                        <BarChart3 className="h-4 w-4 mr-2" />
                        View Analytics
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => toggleShareStatus(share)}>
                        <Eye className="h-4 w-4 mr-2" />
                        {share.isActive ? 'Deactivate' : 'Activate'}
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => {
                          setShareToDelete(share);
                          setDeleteDialogOpen(true);
                        }}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Share
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Share Link</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this share link for "{shareToDelete?.expert.name}"? 
              This action cannot be undone and the link will no longer be accessible.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteShare} className="bg-red-600 hover:bg-red-700">
              Delete Share
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}