const express = require('express');
const assistantController = require('../../controllers/assistantController');
const { upload, handleUploadError } = require('../../middleware/upload');
const { authenticateToken } = require('../../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Assistants
 *   description: AI Assistant management
 */

// Create assistant
/**
 * @swagger
 * /api/assistants:
 *   post:
 *     summary: Create a new AI assistant
 *     tags: [Assistants]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               instructions:
 *                 type: string
 *               model:
 *                 type: string
 *               file:
 *                 type: string
 *                 format: binary
 *     responses:
 *       201:
 *         description: Assistant created successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post('/api/assistants', authenticateToken, upload, handleUploadError, assistantController.createAssistant);

// List assistants
/**
 * @swagger
 * /api/assistants:
 *   get:
 *     summary: List all assistants
 *     tags: [Assistants]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of assistants
 *       401:
 *         description: Unauthorized
 */
router.get('/api/assistants', authenticateToken, assistantController.listAssistants);

// Get specific assistant
/**
 * @swagger
 * /api/assistants/{assistantId}:
 *   get:
 *     summary: Get a specific assistant by ID
 *     tags: [Assistants]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: assistantId
 *         schema:
 *           type: string
 *         required: true
 *         description: Assistant ID
 *     responses:
 *       200:
 *         description: Assistant data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Assistant not found
 */
router.get('/api/assistants/:assistantId', authenticateToken, assistantController.getAssistant);

module.exports = router;
