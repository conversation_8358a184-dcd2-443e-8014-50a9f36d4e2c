const openai = require('../config/openai');
const BalanceService = require('./balanceService');
const db = require('../config/database');
const fs = require('fs');
const path = require('path');
const https = require('https');

class AIGenerationService {
  // AI Label Generation
  async generateLabels(expertData, userId) {
    try {
      // Check user balance first
      const balanceInfo = await BalanceService.getUserBalance(userId);
      const estimatedCost = this.estimateLabelGenerationCost();
      
      if (balanceInfo.totalBalance < estimatedCost) {
        throw new Error('Insufficient balance for AI label generation');
      }

      // Create prompt for label generation
      const prompt = this.createLabelGenerationPrompt(expertData);
      
      // Call OpenAI API
      const response = await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'You are an AI assistant that generates relevant labels/tags for AI experts based on their description and system prompt. Generate 5-10 relevant, concise labels that describe the expert\'s capabilities, domain, and use cases. Return only a JSON array of strings.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 200,
        temperature: 0.7
      });

      // Parse the response
      let labelsText = response.choices[0].message.content.trim();
      
      // Clean up markdown code blocks if present
      labelsText = labelsText.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      
      let labels;
      
      try {
        labels = JSON.parse(labelsText);
      } catch (parseError) {
        // Fallback: extract labels from text response
        labels = this.extractLabelsFromText(labelsText);
      }

      // Validate and filter labels
      const validatedLabels = this.validateAndFilterLabels(labels);
      
      // Calculate actual cost
      const actualCost = this.calculateLabelGenerationCost(
        response.usage.prompt_tokens,
        response.usage.completion_tokens
      );

      // Deduct balance
      await BalanceService.useBalance(userId, actualCost, 'AI Label Generation', null, 'ai_generation');
      
      // Log the generation
      await this.logAIGeneration({
        userId,
        type: 'label_generation',
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        cost: actualCost,
        model: 'gpt-4o-mini',
        metadata: {
          expertName: expertData.name,
          labelsGenerated: validatedLabels.length
        }
      });

      return {
        success: true,
        labels: validatedLabels,
        cost: actualCost,
        tokensUsed: response.usage.total_tokens
      };

    } catch (error) {
      console.error('AI Label Generation Error:', error);
      throw error;
    }
  }

  // AI Image Generation
  async generateExpertImage(expertData, userId) {
    try {
      // Check user balance first
      const balanceInfo = await BalanceService.getUserBalance(userId);
      const estimatedCost = this.estimateImageGenerationCost();
      
      if (balanceInfo.totalBalance < estimatedCost) {
        throw new Error('Insufficient balance for AI image generation');
      }

      // Create prompt for image generation
      const imagePrompt = this.createImageGenerationPrompt(expertData);
      
      // Call DALL-E API
      const response = await openai.images.generate({
        model: 'dall-e-3',
        prompt: imagePrompt,
        size: '1024x1024',
        quality: 'standard',
        n: 1
      });

      const dalleImageUrl = response.data[0].url;
      
      // Download and save image to local server
      const localImageUrl = await this.downloadAndSaveImage(dalleImageUrl, expertData.name);
      
      // Calculate actual cost (DALL-E 3 standard 1024x1024 = $0.040)
      const actualCost = this.calculateImageGenerationCost('dall-e-3', '1024x1024', 'standard');

      // Deduct balance
      await BalanceService.useBalance(userId, actualCost, 'AI Image Generation', null, 'ai_generation');
      
      // Log the generation
      await this.logAIGeneration({
        userId,
        type: 'image_generation',
        promptTokens: 0,
        completionTokens: 0,
        cost: actualCost,
        model: 'dall-e-3',
        metadata: {
          expertName: expertData.name,
          imageSize: '1024x1024',
          quality: 'standard',
          imageUrl: localImageUrl
        }
      });

      return {
        success: true,
        imageUrl: localImageUrl,
        cost: actualCost
      };

    } catch (error) {
      console.error('AI Image Generation Error:', error);
      throw error;
    }
  }

  // Helper methods
  createLabelGenerationPrompt(expertData) {
    return `Generate relevant labels for this AI expert:

Name: ${expertData.name}
Description: ${expertData.description || 'No description provided'}
System Prompt: ${expertData.systemPrompt}
Model: ${expertData.model}

Generate 5-10 concise, relevant labels that describe this expert's capabilities, domain, and potential use cases. Focus on:
- Technical skills and domains
- Industry applications
- User types who would benefit
- Specific capabilities

Return as a JSON array of strings.`;
  }

  createImageGenerationPrompt(expertData) {
    const basePrompt = `Create a professional, modern avatar image for an AI assistant named "${expertData.name}".`;
    
    // Add context based on labels or description
    let contextPrompt = '';
    if (expertData.labels && expertData.labels.length > 0) {
      contextPrompt = ` The assistant specializes in: ${expertData.labels.join(', ')}.`;
    } else if (expertData.description) {
      contextPrompt = ` ${expertData.description}.`;
    }
    
    const stylePrompt = ' Style: Clean, professional, friendly, technology-focused. Use modern colors and geometric shapes. Avoid human faces, use abstract or symbolic representations.';
    
    return basePrompt + contextPrompt + stylePrompt;
  }

  // Download and save DALL-E image to local server
  async downloadAndSaveImage(imageUrl, expertName) {
    try {
      // Create uploads directory if it doesn't exist
      const uploadsDir = path.join(__dirname, '../../uploads');
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      // Generate unique filename
      const timestamp = Date.now();
      const sanitizedName = expertName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
      const filename = `ai-generated-${sanitizedName}-${timestamp}.png`;
      const filePath = path.join(uploadsDir, filename);

      // Download image from DALL-E URL
      await new Promise((resolve, reject) => {
        const file = fs.createWriteStream(filePath);
        https.get(imageUrl, (response) => {
          response.pipe(file);
          file.on('finish', () => {
            file.close();
            resolve();
          });
        }).on('error', (err) => {
          fs.unlink(filePath, () => {}); // Delete the file on error
          reject(err);
        });
      });

      // Return absolute URL path pointing to backend server
      return `http://localhost:3001/uploads/${filename}`;
    } catch (error) {
      console.error('Error downloading and saving image:', error);
      throw new Error('Failed to save generated image');
    }
  }

  validateAndFilterLabels(labels) {
    if (!Array.isArray(labels)) {
      return [];
    }
    
    return labels
      .filter(label => typeof label === 'string' && label.trim().length > 0)
      .map(label => label.trim())
      .filter(label => label.length <= 50) // Max 50 characters per label
      .slice(0, 10); // Max 10 labels
  }

  extractLabelsFromText(text) {
    // Fallback method to extract labels from non-JSON response
    const lines = text.split('\n');
    const labels = [];
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('[') && !trimmed.startsWith(']')) {
        // Remove common prefixes like "1.", "-", "*"
        const cleaned = trimmed.replace(/^[\d\-\*\.\s]+/, '').replace(/["']/g, '');
        if (cleaned.length > 0 && cleaned.length <= 50) {
          labels.push(cleaned);
        }
      }
    }
    
    return labels.slice(0, 10);
  }

  // Cost calculation methods
  estimateLabelGenerationCost() {
    // Estimate based on typical usage: ~150 prompt tokens + ~50 completion tokens
    return this.calculateLabelGenerationCost(150, 50);
  }

  calculateLabelGenerationCost(promptTokens, completionTokens) {
    // GPT-4o-mini pricing: $0.00015 per 1K prompt tokens, $0.0006 per 1K completion tokens
    const promptCost = (promptTokens / 1000) * 0.00015;
    const completionCost = (completionTokens / 1000) * 0.0006;
    const totalCostUSD = promptCost + completionCost;
    
    // Convert to IDR (assuming 1 USD = 15000 IDR)
    return Math.ceil(totalCostUSD * 15000);
  }

  estimateImageGenerationCost() {
    return this.calculateImageGenerationCost('dall-e-3', '1024x1024', 'standard');
  }

  calculateImageGenerationCost(model, size, quality) {
    let costUSD = 0;
    
    if (model === 'dall-e-3') {
      if (size === '1024x1024') {
        costUSD = quality === 'hd' ? 0.080 : 0.040;
      } else if (size === '1792x1024' || size === '1024x1792') {
        costUSD = quality === 'hd' ? 0.120 : 0.080;
      }
    } else if (model === 'dall-e-2') {
      if (size === '1024x1024') costUSD = 0.020;
      else if (size === '512x512') costUSD = 0.018;
      else if (size === '256x256') costUSD = 0.016;
    }
    
    // Convert to IDR
    return Math.ceil(costUSD * 15000);
  }

  // Logging method
  async logAIGeneration(data) {
    try {
      const query = `
        INSERT INTO ai_generation_logs 
        (user_id, type, model, prompt_tokens, completion_tokens, cost_idr, metadata, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
      `;
      
      await db.execute(query, [
        data.userId,
        data.type,
        data.model,
        data.promptTokens,
        data.completionTokens,
        data.cost,
        JSON.stringify(data.metadata)
      ]);
    } catch (error) {
      console.error('Failed to log AI generation:', error);
      // Don't throw error here to avoid breaking the main flow
    }
  }

  // Get user's AI generation history
  async getGenerationHistory(userId, limit = 50) {
    try {
      const query = `
        SELECT * FROM ai_generation_logs 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ?
      `;
      
      const [rows] = await db.execute(query, [userId, limit]);
      return rows;
    } catch (error) {
      console.error('Failed to get generation history:', error);
      throw error;
    }
  }

  // Get cost estimation for frontend
  async getCostEstimation() {
    return {
      labelGeneration: {
        estimated: this.estimateLabelGenerationCost(),
        description: 'Generate 5-10 relevant labels for your expert'
      },
      imageGeneration: {
        estimated: this.estimateImageGenerationCost(),
        description: 'Generate a professional avatar image for your expert'
      }
    };
  }
}

module.exports = new AIGenerationService();