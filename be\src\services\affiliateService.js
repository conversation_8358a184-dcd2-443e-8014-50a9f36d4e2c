const pool = require('../config/database');
const { CurrencyUtils } = require('../utils/currencyUtils');
const crypto = require('crypto');

class AffiliateService {
  
  // Exchange rate USD to IDR (can be made dynamic later)
  static USD_TO_IDR_RATE = 20000;

  /**
   * Generate unique visitor ID
   */
  generateVisitorId() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Track affiliate visit with cookie
   */
  async trackAffiliateVisit(referralCode, visitorData = {}) {
    try {
      const { 
        visitorId = this.generateVisitorId(),
        ipAddress = null,
        userAgent = null,
        referer = null,
        landingPage = null
      } = visitorData;

      // Get affiliate user by referral code
      const referrerResult = await this.getUserByReferralCode(referralCode);
      if (!referrerResult.success) {
        return {
          success: false,
          error: 'Invalid referral code'
        };
      }

      const affiliateUserId = referrerResult.user.user_id;
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      // Check if visit already exists for this visitor and affiliate
      const [existingVisit] = await pool.execute(
        'SELECT id, expires_at FROM affiliate_visits WHERE visitor_id = ? AND affiliate_user_id = ?',
        [visitorId, affiliateUserId]
      );

      if (existingVisit.length > 0) {
        // Update existing visit to extend expiry
        await pool.execute(
          'UPDATE affiliate_visits SET expires_at = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [expiresAt, existingVisit[0].id]
        );

        return {
          success: true,
          visitorId: visitorId,
          message: 'Visit tracking updated',
          existingVisit: true
        };
      }

      // Create new visit record
      const [result] = await pool.execute(
        `INSERT INTO affiliate_visits 
         (visitor_id, affiliate_user_id, referral_code, ip_address, user_agent, referer, landing_page, expires_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [visitorId, affiliateUserId, referralCode, ipAddress, userAgent, referer, landingPage, expiresAt]
      );

      return {
        success: true,
        visitorId: visitorId,
        visitId: result.insertId,
        message: 'Visit tracked successfully',
        expiresAt: expiresAt
      };
    } catch (error) {
      console.error('Track affiliate visit error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Convert visitor to user (when they register)
   */
  async convertVisitorToUser(visitorId, userId) {
    try {
      // Find active affiliate visit for this visitor
      const [visits] = await pool.execute(
        'SELECT * FROM affiliate_visits WHERE visitor_id = ? AND expires_at > NOW() AND converted = 0 ORDER BY created_at DESC LIMIT 1',
        [visitorId]
      );

      if (visits.length === 0) {
        return {
          success: false,
          error: 'No active affiliate visit found'
        };
      }

      const visit = visits[0];

      // Update visit to mark as converted
      await pool.execute(
        'UPDATE affiliate_visits SET converted = 1, converted_user_id = ?, conversion_date = CURRENT_TIMESTAMP WHERE id = ?',
        [userId, visit.id]
      );

      // Update user with referral information
      await pool.execute(
        'UPDATE user SET referred_by = ? WHERE user_id = ?',
        [visit.affiliate_user_id, userId]
      );

      return {
        success: true,
        affiliateUserId: visit.affiliate_user_id,
        referralCode: visit.referral_code,
        message: 'Visitor converted to user successfully'
      };
    } catch (error) {
      console.error('Convert visitor to user error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Generate referral code for user
   */
  async generateReferralCode(userId) {
    try {
      const [result] = await pool.execute(
        'UPDATE user SET referral_code = generate_referral_code(?) WHERE user_id = ? AND referral_code IS NULL',
        [userId, userId]
      );

      if (result.affectedRows > 0) {
        const [userRows] = await pool.execute(
          'SELECT referral_code FROM user WHERE user_id = ?',
          [userId]
        );
        
        return {
          success: true,
          referralCode: userRows[0]?.referral_code
        };
      }

      // If no rows affected, user might already have a referral code
      const [userRows] = await pool.execute(
        'SELECT referral_code FROM user WHERE user_id = ?',
        [userId]
      );

      return {
        success: true,
        referralCode: userRows[0]?.referral_code
      };
    } catch (error) {
      console.error('Generate referral code error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user by referral code
   */
  async getUserByReferralCode(referralCode) {
    try {
      const [rows] = await pool.execute(
        'SELECT user_id, name, email, referral_code FROM user WHERE referral_code = ?',
        [referralCode]
      );

      if (rows.length === 0) {
        return {
          success: false,
          error: 'Invalid referral code'
        };
      }

      return {
        success: true,
        user: rows[0]
      };
    } catch (error) {
      console.error('Get user by referral code error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Set referral relationship
   */
  async setReferral(userId, referralCode) {
    try {
      // Check if user already has a referrer
      const [existingRows] = await pool.execute(
        'SELECT referred_by FROM user WHERE user_id = ?',
        [userId]
      );

      if (existingRows[0]?.referred_by) {
        return {
          success: false,
          error: 'User already has a referrer'
        };
      }

      // Get referrer user ID
      const referrerResult = await this.getUserByReferralCode(referralCode);
      if (!referrerResult.success) {
        return referrerResult;
      }

      const referrerId = referrerResult.user.user_id;

      // Check if user is trying to refer themselves
      if (referrerId === userId) {
        return {
          success: false,
          error: 'Cannot refer yourself'
        };
      }

      // Update user with referrer
      const [result] = await pool.execute(
        'UPDATE user SET referred_by = ? WHERE user_id = ?',
        [referrerId, userId]
      );

      return {
        success: true,
        message: 'Referral relationship established',
        referrerId: referrerId
      };
    } catch (error) {
      console.error('Set referral error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate and record commission for token usage (IDR version)
   */
  async recordCommission(data) {
    const {
      userId,
      expertId = null,
      sessionId = null,
      messageId = null,
      baseCostUSD,
      markupCostUSD = 0,
      tokensUsed = 0,
      inputTokens = 0,
      outputTokens = 0
    } = data;

    try {
      // Get user's referrer
      const [userRows] = await pool.execute(
        'SELECT referred_by FROM user WHERE user_id = ?',
        [userId]
      );

      if (!userRows[0]?.referred_by) {
        // No referrer, no commission to record
        return { success: true, message: 'No referrer found' };
      }

      const affiliateUserId = userRows[0].referred_by;

      // Calculate commission (25% of base cost)
      const commissionRate = 25.00;
      const commissionAmountUSD = (baseCostUSD * commissionRate) / 100;
      
      // Convert to IDR
      const baseCostIDR = baseCostUSD * AffiliateService.USD_TO_IDR_RATE;
      const markupCostIDR = markupCostUSD * AffiliateService.USD_TO_IDR_RATE;
      const commissionAmountIDR = commissionAmountUSD * AffiliateService.USD_TO_IDR_RATE;

      let commissionType = 'direct_usage';
      let expertOwnerId = null;

      // If expert is used, check if it's expert usage
      if (expertId) {
        const [expertRows] = await pool.execute(
          'SELECT user_id FROM experts WHERE id = ?',
          [expertId]
        );

        if (expertRows[0]) {
          expertOwnerId = expertRows[0].user_id;
          commissionType = 'expert_usage';
        }
      }

      // Record commission with IDR amounts
      const [result] = await pool.execute(
        `INSERT INTO affiliate_commissions 
         (affiliate_user_id, referred_user_id, expert_id, expert_owner_id, 
          session_id, message_id, commission_type, base_cost, markup_cost, 
          commission_amount, commission_rate, base_cost_idr, markup_cost_idr,
          commission_amount_idr, exchange_rate_used, tokens_used, status) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'confirmed')`,
        [
          affiliateUserId,
          userId,
          expertId,
          expertOwnerId,
          sessionId,
          messageId,
          commissionType,
          baseCostUSD,
          markupCostUSD,
          commissionAmountUSD,
          commissionRate,
          baseCostIDR,
          markupCostIDR,
          commissionAmountIDR,
          AffiliateService.USD_TO_IDR_RATE,
          tokensUsed
        ]
      );

      // If expert usage, also record commission for expert owner's referrer
      if (commissionType === 'expert_usage' && expertOwnerId && expertOwnerId !== affiliateUserId) {
        const [expertOwnerRows] = await pool.execute(
          'SELECT referred_by FROM user WHERE user_id = ?',
          [expertOwnerId]
        );

        if (expertOwnerRows[0]?.referred_by) {
          const expertOwnerReferrerId = expertOwnerRows[0].referred_by;
          
          await pool.execute(
            `INSERT INTO affiliate_commissions 
             (affiliate_user_id, referred_user_id, expert_id, expert_owner_id, 
              session_id, message_id, commission_type, base_cost, markup_cost, 
              commission_amount, commission_rate, base_cost_idr, markup_cost_idr,
              commission_amount_idr, exchange_rate_used, tokens_used, status) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'confirmed')`,
            [
              expertOwnerReferrerId,
              expertOwnerId,
              expertId,
              expertOwnerId,
              sessionId,
              messageId,
              'expert_usage',
              baseCostUSD,
              markupCostUSD,
              commissionAmountUSD,
              commissionRate,
              baseCostIDR,
              markupCostIDR,
              commissionAmountIDR,
              AffiliateService.USD_TO_IDR_RATE,
              tokensUsed
            ]
          );
        }
      }

      return {
        success: true,
        commissionId: result.insertId,
        commissionAmountUSD: commissionAmountUSD,
        commissionAmountIDR: commissionAmountIDR,
        commissionType: commissionType
      };
    } catch (error) {
      console.error('Record commission error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get affiliate stats for a user (IDR version)
   */
  async getAffiliateStats(userId) {
    try {
      const [statsRows] = await pool.execute(
        'SELECT * FROM affiliate_stats WHERE user_id = ?',
        [userId]
      );

      if (statsRows.length === 0) {
        return {
          success: true,
          stats: {
            userId: userId,
            referralCode: null,
            totalReferrals: 0,
            totalCommissions: 0,
            totalCommissionEarnedIDR: 0,
            last30DaysCommissionIDR: 0,
            last7DaysCommissionIDR: 0,
            totalVisits: 0,
            totalConversions: 0,
            conversionRate: 0,
            lastCommissionDate: null
          }
        };
      }

      const stats = statsRows[0];
      return {
        success: true,
        stats: {
          userId: stats.user_id,
          name: stats.name,
          email: stats.email,
          referralCode: stats.referral_code,
          totalReferrals: parseInt(stats.total_referrals || 0),
          totalCommissions: parseInt(stats.total_commissions || 0),
          totalCommissionEarnedIDR: parseFloat(stats.total_commission_earned_idr || 0),
          last30DaysCommissionIDR: parseFloat(stats.last_30_days_commission_idr || 0),
          last7DaysCommissionIDR: parseFloat(stats.last_7_days_commission_idr || 0),
          totalVisits: parseInt(stats.total_visits || 0),
          totalConversions: parseInt(stats.total_conversions || 0),
          conversionRate: parseFloat(stats.conversion_rate || 0),
          lastCommissionDate: stats.last_commission_date,
          totalCommissionEarnedFormatted: CurrencyUtils.formatIDR(stats.total_commission_earned_idr || 0),
          last30DaysCommissionFormatted: CurrencyUtils.formatIDR(stats.last_30_days_commission_idr || 0),
          last7DaysCommissionFormatted: CurrencyUtils.formatIDR(stats.last_7_days_commission_idr || 0)
        }
      };
    } catch (error) {
      console.error('Get affiliate stats error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get referral stats for a user
   */
  async getReferralStats(userId) {
    try {
      const [statsRows] = await pool.execute(
        'SELECT * FROM referral_stats WHERE affiliate_user_id = ?',
        [userId]
      );

      const referrals = statsRows.map(row => ({
        referredUserId: row.referred_user_id,
        referredUserName: row.referred_user_name,
        referredUserEmail: row.referred_user_email,
        referralDate: row.referral_date,
        totalSessions: parseInt(row.total_sessions || 0),
        totalMessages: parseInt(row.total_messages || 0),
        totalTokensUsed: parseInt(row.total_tokens_used || 0),
        totalCostGenerated: parseFloat(row.total_cost_generated || 0),
        totalCommissionGenerated: parseFloat(row.total_commission_generated || 0),
        totalCostGeneratedFormatted: CurrencyUtils.formatIDR(row.total_cost_generated || 0),
        totalCommissionGeneratedFormatted: CurrencyUtils.formatIDR(row.total_commission_generated || 0)
      }));

      return {
        success: true,
        referrals: referrals
      };
    } catch (error) {
      console.error('Get referral stats error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get commission history for a user
   */
  async getCommissionHistory(userId, page = 1, limit = 50) {
    try {
      const offset = (page - 1) * limit;

      const [commissionRows] = await pool.execute(
        `SELECT 
          ac.*,
          ru.name as referred_user_name,
          ru.email as referred_user_email,
          e.name as expert_name,
          eo.name as expert_owner_name
         FROM affiliate_commissions ac
         LEFT JOIN user ru ON ac.referred_user_id = ru.user_id
         LEFT JOIN experts e ON ac.expert_id = e.id
         LEFT JOIN user eo ON ac.expert_owner_id = eo.user_id
         WHERE ac.affiliate_user_id = ?
         ORDER BY ac.created_at DESC
         LIMIT ? OFFSET ?`,
        [userId, limit, offset]
      );

      const [countRows] = await pool.execute(
        'SELECT COUNT(*) as total FROM affiliate_commissions WHERE affiliate_user_id = ?',
        [userId]
      );

      const commissions = commissionRows.map(row => ({
        id: row.id,
        referredUserName: row.referred_user_name,
        referredUserEmail: row.referred_user_email,
        expertName: row.expert_name,
        expertOwnerName: row.expert_owner_name,
        commissionType: row.commission_type,
        baseCost: parseFloat(row.base_cost),
        markupCost: parseFloat(row.markup_cost || 0),
        commissionAmount: parseFloat(row.commission_amount),
        commissionRate: parseFloat(row.commission_rate),
        tokensUsed: parseInt(row.tokens_used || 0),
        status: row.status,
        createdAt: row.created_at,
        baseCostFormatted: CurrencyUtils.formatIDR(row.base_cost),
        commissionAmountFormatted: CurrencyUtils.formatIDR(row.commission_amount)
      }));

      return {
        success: true,
        commissions: commissions,
        pagination: {
          page: page,
          limit: limit,
          total: countRows[0].total,
          totalPages: Math.ceil(countRows[0].total / limit)
        }
      };
    } catch (error) {
      console.error('Get commission history error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get top affiliates
   */
  async getTopAffiliates(limit = 10) {
    try {
      const [rows] = await pool.execute(
        `SELECT 
          user_id,
          name,
          email,
          referral_code,
          total_referrals,
          total_commissions,
          total_commission_earned,
          last_30_days_commission
         FROM affiliate_stats 
         WHERE total_commission_earned > 0
         ORDER BY total_commission_earned DESC 
         LIMIT ?`,
        [limit]
      );

      const affiliates = rows.map(row => ({
        userId: row.user_id,
        name: row.name,
        email: row.email,
        referralCode: row.referral_code,
        totalReferrals: parseInt(row.total_referrals || 0),
        totalCommissions: parseInt(row.total_commissions || 0),
        totalCommissionEarned: parseFloat(row.total_commission_earned || 0),
        last30DaysCommission: parseFloat(row.last_30_days_commission || 0),
        totalCommissionEarnedFormatted: CurrencyUtils.formatIDR(row.total_commission_earned || 0),
        last30DaysCommissionFormatted: CurrencyUtils.formatIDR(row.last_30_days_commission || 0)
      }));

      return {
        success: true,
        affiliates: affiliates
      };
    } catch (error) {
      console.error('Get top affiliates error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new AffiliateService();
