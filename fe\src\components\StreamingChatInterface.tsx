"use client";

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { Send, Mic, MicOff, ArrowLeft, Volume2, VolumeX, Play, Pause } from 'lucide-react';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { StreamingMessage, TypingIndicator } from '@/components/ui/streaming-message';
import { RealTimeBalance } from '@/components/ui/real-time-balance';
import { ConnectionStatus } from '@/components/ui/connection-status';
import { useStreamingChat } from '@/hooks/useStreamingChat';
import { useSocket } from '@/contexts/SocketContext';
import { asset } from '@/lib/utils';
import { api } from '@/lib/api';

interface Expert {
  id: number;
  name: string;
  description: string;
  systemPrompt: string;
  model: string;
  assistantId: string;
  imageUrl?: string;
  pricingPercentage: number;
  isPublic: boolean;
  voiceEnabled?: boolean;
  labels: string[];
  createdAt: string;
  updatedAt: string;
}

interface StreamingChatInterfaceProps {
  expert: Expert | null;
  sessionId?: string;
  initialMessages?: any[];
}

export const StreamingChatInterface: React.FC<StreamingChatInterfaceProps> = ({
  expert,
  sessionId,
  initialMessages = []
}) => {
  const [input, setInput] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  
  // TTS and Audio Playback State
  const [isTTSEnabled, setIsTTSEnabled] = useState(false);
  const [isGeneratingTTS, setIsGeneratingTTS] = useState(false);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [selectedVoice, setSelectedVoice] = useState<'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer'>('alloy');
  
  const chatRef = useRef<HTMLDivElement>(null);
  const { isConnected, connectionError, reconnect } = useSocket();

  const {
    messages,
    isStreaming,
    isTyping,
    error,
    currentCost,
    totalTokens,
    balanceUpdate,
    sendMessage,
    sendVoice,
    clearError,
    addMessage
  } = useStreamingChat({
    expertId: expert?.id.toString() || '',
    sessionId,
    onMessageComplete: (message) => {
      console.log('✅ Message completed:', message);
    },
    onCostUpdate: (cost, tokens) => {
      console.log('💰 Cost updated:', { cost, tokens });
    },
    onBalanceUpdate: (balance) => {
      console.log('💳 Balance updated:', balance);
    },
    onError: (error) => {
      console.error('❌ Chat error:', error);
    }
  });

  // Load initial messages (only once)
  const initialMessagesLoadedRef = useRef(false);
  useEffect(() => {
    if (initialMessages.length > 0 && !initialMessagesLoadedRef.current) {
      console.log('📥 Loading initial messages:', initialMessages.length);
      initialMessages.forEach(msg => {
        addMessage({
          role: msg.role,
          content: msg.content,
          timestamp: new Date(msg.timestamp || msg.created_at).getTime()
        });
      });
      initialMessagesLoadedRef.current = true;
    }
  }, [initialMessages, addMessage]);

  // Auto-enable TTS for voice-enabled experts when last message is from assistant
  useEffect(() => {
    if (expert?.voiceEnabled && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'assistant' && !isTTSEnabled) {
        console.log('🔊 Auto-enabling TTS for voice-enabled expert');
        setIsTTSEnabled(true);
      }
    }
  }, [expert, messages, isTTSEnabled]);

  // Auto-play TTS when chat opens and last message is from assistant (for voice-enabled experts)
  const initialAutoPlayRef = useRef(false);
  useEffect(() => {
    if (expert?.voiceEnabled && messages.length > 0 && !initialAutoPlayRef.current && initialMessagesLoadedRef.current) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'assistant' && lastMessage.content && !lastMessage.isStreaming) {
        console.log('🎵 Auto-playing TTS for last assistant message on chat open');
        // Enable TTS first
        setIsTTSEnabled(true);
        // Then play the audio after a short delay
        setTimeout(() => {
          generateTTS(lastMessage.content);
        }, 1000);
        initialAutoPlayRef.current = true;
      }
    }
  }, [expert, messages, initialMessagesLoadedRef.current]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (chatRef.current) {
      chatRef.current.scrollTo({
        top: chatRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, [messages, isTyping]);

  const getExpertIcon = (labels: string[]) => {
    if (!labels) return "🤖";
    if (labels.includes("business") || labels.includes("marketing")) return "💼";
    if (labels.includes("code") || labels.includes("programming")) return "💻";
    if (labels.includes("creative") || labels.includes("design")) return "🎨";
    if (labels.includes("education") || labels.includes("learning")) return "📚";
    if (labels.includes("health") || labels.includes("medical")) return "🏥";
    if (labels.includes("finance") || labels.includes("money")) return "💰";
    return "🤖";
  };

  const handleSend = async () => {
    if (!input.trim() || isStreaming) return;
    
    // Check if expert is loaded
    if (!expert || !expert.id) {
      console.warn('⚠️ Cannot send message: expert not loaded');
      alert('Please wait for the expert to load before sending messages.');
      return;
    }
    
    const messageText = input;
    setInput('');
    
    if (isConnected) {
      // Use streaming chat
      console.log('📤 Sending message with expert:', { expertId: expert.id, message: messageText.substring(0, 50) + '...' });
      sendMessage(messageText);
    } else {
      // Fallback to regular API
      console.log('🔄 Using fallback API for message:', messageText);
      // You could implement a fallback to the regular chat API here
      // For now, we'll just show that streaming is not available
      alert('Real-time chat is not available. Please log in or refresh the page to enable streaming features.');
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      const audioChunks: BlobPart[] = [];

      recorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64Audio = (reader.result as string).split(',')[1];
          sendVoice(base64Audio);
        };
        reader.readAsDataURL(audioBlob);
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };

      recorder.start();
      setMediaRecorder(recorder);
      setIsRecording(true);
    } catch (error) {
      console.error('Failed to start recording:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
      setMediaRecorder(null);
      setIsRecording(false);
    }
  };

  // TTS Functions
  const generateTTS = async (text: string) => {
    if (!text.trim() || isGeneratingTTS) return;
    
    try {
      setIsGeneratingTTS(true);
      
      const response = await api.textToSpeech(text.trim(), selectedVoice);
      
      // Create audio URL from blob
      const audioUrl = URL.createObjectURL(response);
      const audio = new HTMLAudioElement();
      audio.src = audioUrl;
      
      // Set up audio event listeners
      audio.onplay = () => setIsPlaying(true);
      audio.onpause = () => setIsPlaying(false);
      audio.onended = () => {
        setIsPlaying(false);
        setCurrentAudio(null);
        URL.revokeObjectURL(audioUrl);
      };
      
      // Stop current audio if playing
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }
      
      setCurrentAudio(audio);
      audio.play();
      
    } catch (error) {
      console.error('TTS generation failed:', error);
      alert('Failed to generate speech. Please try again.');
    } finally {
      setIsGeneratingTTS(false);
    }
  };
  
  const toggleAudioPlayback = () => {
    if (!currentAudio) return;
    
    if (isPlaying) {
      currentAudio.pause();
    } else {
      currentAudio.play();
    }
  };
  
  const stopAudioPlayback = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setIsPlaying(false);
    }
  };
  
  // Auto-generate TTS for assistant messages when TTS is enabled
  useEffect(() => {
    if (isTTSEnabled && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === 'assistant' && lastMessage.content && !lastMessage.isStreaming) {
        // Wait a bit for the message to be fully rendered
        setTimeout(() => {
          generateTTS(lastMessage.content);
        }, 500);
      }
    }
  }, [messages, isTTSEnabled]);
  
  // Cleanup audio on unmount
  useEffect(() => {
    return () => {
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.src = '';
      }
    };
  }, [currentAudio]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <Link
              href={expert ? `/expert/${expert.id}` : "/"}
              className="inline-flex items-center space-x-2 text-gray-600 hover:text-blue-900 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>{expert ? "Back to Profile" : "Back to Home"}</span>
            </Link>

            {expert && (
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm text-gray-500">Chatting with</p>
                  <p className="font-semibold text-gray-900">{expert.name}</p>
                </div>
                {expert.imageUrl ? (
                  <Image
                    src={asset(expert.imageUrl)}
                    alt={expert.name}
                    width={40}
                    height={40}
                    className="w-10 h-10 object-cover rounded-full border-2 border-gray-200"
                  />
                ) : (
                  <div
                    className="w-10 h-10 rounded-full flex items-center justify-center text-white text-sm"
                    style={{ backgroundColor: "#1E3A8A" }}
                  >
                    {getExpertIcon(expert.labels)}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Chat Interface */}
          <div className="lg:col-span-3">
            <Card className="bg-white/80 backdrop-blur-sm border border-gray-200 shadow-xl">
              <div className="p-6">
                {/* Expert Info Banner */}
                {expert && (
                  <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                    <div className="flex items-center space-x-4">
                      {expert.imageUrl ? (
                        <Image
                          src={asset(expert.imageUrl)}
                          alt={expert.name}
                          width={48}
                          height={48}
                          className="w-12 h-12 object-cover rounded-full border-2 border-white shadow-sm"
                        />
                      ) : (
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center text-white text-lg shadow-sm"
                          style={{ backgroundColor: "#1E3A8A" }}
                        >
                          {getExpertIcon(expert.labels)}
                        </div>
                      )}
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{expert.name}</h3>
                        <p className="text-sm text-gray-600">{expert.description}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                            {expert.model}
                          </span>
                          <span className={`text-xs ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                            {isConnected ? '● Online' : '● Offline'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Connection Status */}
                <div className="mb-4">
                  <ConnectionStatus 
                    isConnected={isConnected}
                    connectionError={connectionError}
                    onReconnect={reconnect}
                  />
                  {connectionError && connectionError.includes('log in') && (
                    <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-700">
                        💡 <strong>Tip:</strong> You can still use the regular chat without real-time features. 
                        <Link href="/login" className="underline ml-1">Log in</Link> to enable streaming chat.
                      </p>
                    </div>
                  )}
                </div>

                {/* Error Display */}
                {error && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-red-600">{error}</span>
                      <button
                        onClick={clearError}
                        className="text-red-600 hover:text-red-800 text-sm underline"
                      >
                        Dismiss
                      </button>
                    </div>
                  </div>
                )}

                {/* Chat Messages */}
                <div
                  ref={chatRef}
                  className="h-[60vh] overflow-y-auto space-y-4 mb-6 px-2"
                  style={{ scrollbarWidth: "thin" }}
                >
                  {messages.length === 0 && expert && (
                    <div className="text-center mt-20">
                      <div className="text-6xl mb-4">💬</div>
                      <h3 className="text-xl font-semibold text-gray-700 mb-2">
                        Start a Conversation
                      </h3>
                      <p className="text-gray-500">
                        Hello! I'm {expert.name}. {expert.description} How can I assist you today?
                      </p>
                    </div>
                  )}

                  {messages.filter(message => message && message.id).map((message) => (
                    <StreamingMessage
                      key={message.id}
                      role={message.role}
                      content={message.content || ''}
                      isStreaming={message.isStreaming}
                      timestamp={message.timestamp}
                      cost={message.cost}
                      tokens={message.tokens}
                      expertName={expert?.name}
                      expertImageUrl={expert?.imageUrl}
                      expertIcon={expert ? getExpertIcon(expert.labels) : '🤖'}
                    />
                  ))}

                  {/* Typing Indicator */}
                  {isTyping && (
                    <TypingIndicator
                      expertName={expert?.name}
                      expertImageUrl={expert?.imageUrl}
                      expertIcon={expert ? getExpertIcon(expert.labels) : '🤖'}
                    />
                  )}
                </div>

                {/* TTS Controls */}
                <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <Button
                        type="button"
                        onClick={() => setIsTTSEnabled(!isTTSEnabled)}
                        className={`px-3 py-2 rounded-lg font-medium transition-all duration-200 ${
                          isTTSEnabled 
                            ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                        }`}
                      >
                        {isTTSEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
                        <span className="ml-2 text-sm">{isTTSEnabled ? 'TTS On' : 'TTS Off'}</span>
                      </Button>
                      
                      {isTTSEnabled && (
                        <select
                          value={selectedVoice}
                          onChange={(e) => setSelectedVoice(e.target.value as any)}
                          className="px-3 py-2 rounded-lg border border-gray-200 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-200"
                        >
                          <option value="alloy">Alloy</option>
                          <option value="echo">Echo</option>
                          <option value="fable">Fable</option>
                          <option value="onyx">Onyx</option>
                          <option value="nova">Nova</option>
                          <option value="shimmer">Shimmer</option>
                        </select>
                      )}
                    </div>
                    
                    {/* Audio Playback Controls */}
                    {currentAudio && (
                      <div className="flex items-center gap-2">
                        <Button
                          type="button"
                          onClick={toggleAudioPlayback}
                          className="px-3 py-2 rounded-lg bg-green-600 hover:bg-green-700 text-white transition-all duration-200"
                        >
                          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </Button>
                        <Button
                          type="button"
                          onClick={stopAudioPlayback}
                          className="px-3 py-2 rounded-lg bg-red-600 hover:bg-red-700 text-white transition-all duration-200"
                        >
                          <VolumeX className="w-4 h-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                  
                  {/* TTS Status */}
                  {isGeneratingTTS && (
                    <div className="flex items-center gap-2 text-sm text-blue-600">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span>Generating speech...</span>
                    </div>
                  )}
                </div>

                {/* Input Area */}
                <form
                  className="flex gap-3 items-end"
                  onSubmit={(e) => {
                    e.preventDefault();
                    handleSend();
                  }}
                >
                  <div className="flex-1">
                    <Input
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder={
                        expert
                          ? `Ask ${expert.name} anything...`
                          : "Type your message..."
                      }
                      disabled={isStreaming || !isConnected}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !e.shiftKey) {
                          e.preventDefault();
                          handleSend();
                        }
                      }}
                    />
                  </div>

                  {/* Manual TTS Button */}
                  {messages.length > 0 && messages[messages.length - 1]?.role === 'assistant' && (
                    <Button
                      type="button"
                      onClick={() => {
                        const lastMessage = messages[messages.length - 1];
                        if (lastMessage?.content) {
                          generateTTS(lastMessage.content);
                        }
                      }}
                      disabled={isGeneratingTTS || !isConnected}
                      className="px-4 py-3 rounded-xl bg-purple-600 hover:bg-purple-700 text-white font-medium transition-all duration-200"
                      title="Generate speech for last message"
                    >
                      <Volume2 className="w-5 h-5" />
                    </Button>
                  )}

                  {/* Voice Recording Button */}
                  <Button
                    type="button"
                    onClick={isRecording ? stopRecording : startRecording}
                    disabled={isStreaming || !isConnected}
                    className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                      isRecording 
                        ? 'bg-red-600 hover:bg-red-700 text-white' 
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    }`}
                  >
                    {isRecording ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
                  </Button>

                  {/* Send Button */}
                  <Button
                    type="submit"
                    disabled={isStreaming || !input.trim() || !isConnected}
                    className="px-6 py-3 rounded-xl text-white font-medium transition-all duration-200 hover:shadow-lg"
                    style={{ backgroundColor: "#1E3A8A" }}
                  >
                    <Send className="w-5 h-5" />
                  </Button>
                </form>
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-4">
            {/* Real-time Balance */}
            <RealTimeBalance
              currentCost={currentCost}
              totalTokens={totalTokens}
              balanceUpdate={balanceUpdate}
              isStreaming={isStreaming}
            />

            {/* Session Info */}
            {expert && (
              <Card className="p-4">
                <h3 className="font-semibold text-gray-900 mb-3">Session Info</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Expert:</span>
                    <span className="font-medium">{expert.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Model:</span>
                    <span className="font-medium">{expert.model}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Messages:</span>
                    <span className="font-medium">{messages.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className={`font-medium ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
                      {isConnected ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};