const express = require('express');
const expertController = require('../../controllers/expertController');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Experts
 *   description: AI Expert management and marketplace
 */

// Get available models
/**
 * @swagger
 * /api/models:
 *   get:
 *     summary: Get available AI models
 *     tags: [Experts]
 *     responses:
 *       200:
 *         description: List of models
 */
router.get('/api/models', expertController.getAvailableModels.bind(expertController));

// Get model pricing
/**
 * @swagger
 * /api/models/{model}/pricing:
 *   get:
 *     summary: Get pricing for a specific model
 *     tags: [Experts]
 *     parameters:
 *       - in: path
 *         name: model
 *         schema:
 *           type: string
 *         required: true
 *         description: Model name
 *     responses:
 *       200:
 *         description: Model pricing
 *       404:
 *         description: Model not found
 */
router.get('/api/models/:model/pricing', expertController.getModelPricing.bind(expertController));

// Calculate cost
/**
 * @swagger
 * /api/calculate-cost:
 *   post:
 *     summary: Calculate cost for a model usage
 *     tags: [Experts]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               model:
 *                 type: string
 *               tokens:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Cost calculated
 *       400:
 *         description: Invalid input
 */
router.post('/api/calculate-cost', expertController.calculateCost.bind(expertController));

// Get public experts
/**
 * @swagger
 * /api/experts/public:
 *   get:
 *     summary: Get public AI experts
 *     tags: [Experts]
 *     responses:
 *       200:
 *         description: List of public experts
 */
router.get('/api/experts/public', expertController.getPublicExperts.bind(expertController));

module.exports = router;
